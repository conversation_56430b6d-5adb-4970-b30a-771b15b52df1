﻿// <auto-generated />
using System;
using Anis.Cashflow.Report.Infra.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Anis.Cashflow.Report.Infra.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250720095858_AddLinkDateTime")]
    partial class AddLinkDateTime
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "6.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Account", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("ConfirmedAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(450)");

                    b.Property<DateTime?>("ExpiryAt")
                        .HasColumnType("datetime2");

                    b.Property<string>("LocationId")
                        .IsRequired()
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Number")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OwnerName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Phone")
                        .HasColumnType("nvarchar(450)");

                    b.Property<long>("Sequence")
                        .IsConcurrencyToken()
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("Email");

                    b.HasIndex("ExpiryAt");

                    b.HasIndex("LocationId");

                    b.HasIndex("Phone");

                    b.ToTable("Accounts");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Holder", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ArabicName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EnglishName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Sequence")
                        .IsConcurrencyToken()
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("Holders");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Region", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ArabicName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Code")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EnglishName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("Sequence")
                        .IsConcurrencyToken()
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.ToTable("Regions");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Subscription", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("AccountId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("StateVersion")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AccountId");

                    b.ToTable("Subscriptions");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.SubscriptionWallet", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<int>("Id"), 1L, 1);

                    b.Property<DateTime?>("LinkDateTime")
                        .HasColumnType("datetime");

                    b.Property<string>("SubscriptionId")
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("WalletId")
                        .HasColumnType("nvarchar(128)");

                    b.HasKey("Id");

                    b.HasIndex("SubscriptionId");

                    b.HasIndex("WalletId");

                    b.ToTable("SubscriptionWallets");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Wallet", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<decimal>("Balance")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("CurrencyType")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<decimal>("CurrentDelayed")
                        .HasColumnType("decimal(18,3)");

                    b.Property<decimal>("CurrentUrgent")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("HolderId")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<DateTime?>("LastDelayRefundDate")
                        .HasColumnType("datetime2");

                    b.Property<decimal>("NetBalance")
                        .HasColumnType("decimal(18,3)");

                    b.Property<string>("RegionId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<int>("Sequence")
                        .IsConcurrencyToken()
                        .HasColumnType("int");

                    b.Property<string>("SubscriptionId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("HolderId");

                    b.HasIndex("RegionId");

                    b.HasIndex("SubscriptionId");

                    b.ToTable("Wallets");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Account", b =>
                {
                    b.HasOne("Anis.Cashflow.Report.Domain.Entities.Region", "Location")
                        .WithMany("Accounts")
                        .HasForeignKey("LocationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Location");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Subscription", b =>
                {
                    b.HasOne("Anis.Cashflow.Report.Domain.Entities.Account", "Account")
                        .WithMany("Subscriptions")
                        .HasForeignKey("AccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Account");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.SubscriptionWallet", b =>
                {
                    b.HasOne("Anis.Cashflow.Report.Domain.Entities.Subscription", "Subscription")
                        .WithMany("SubscriptionWallets")
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.HasOne("Anis.Cashflow.Report.Domain.Entities.Wallet", "Wallet")
                        .WithMany("SubscriptionWallets")
                        .HasForeignKey("WalletId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("Subscription");

                    b.Navigation("Wallet");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Wallet", b =>
                {
                    b.HasOne("Anis.Cashflow.Report.Domain.Entities.Holder", "Holder")
                        .WithMany("Wallets")
                        .HasForeignKey("HolderId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Anis.Cashflow.Report.Domain.Entities.Region", "Region")
                        .WithMany("Wallets")
                        .HasForeignKey("RegionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Anis.Cashflow.Report.Domain.Entities.Subscription", "Subscription")
                        .WithMany("Wallets")
                        .HasForeignKey("SubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Holder");

                    b.Navigation("Region");

                    b.Navigation("Subscription");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Account", b =>
                {
                    b.Navigation("Subscriptions");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Holder", b =>
                {
                    b.Navigation("Wallets");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Region", b =>
                {
                    b.Navigation("Accounts");

                    b.Navigation("Wallets");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Subscription", b =>
                {
                    b.Navigation("SubscriptionWallets");

                    b.Navigation("Wallets");
                });

            modelBuilder.Entity("Anis.Cashflow.Report.Domain.Entities.Wallet", b =>
                {
                    b.Navigation("SubscriptionWallets");
                });
#pragma warning restore 612, 618
        }
    }
}
