﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.EventsData.Holders.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.HolderHandlers
{
    public class NameChangedHandler : IRequestHandler<MessageBody<NameChangedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public NameChangedHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> Handle(MessageBody<NameChangedData> message, CancellationToken cancellationToken)
        {
            var holder = await _unitOfWork.Holders.FindAsync(message.AggregateId);

            if (holder is null)
                return false;

            if (holder.Sequence != message.Sequence - 1)
                return holder.Sequence >= message.Sequence;

            holder.Update(message);

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}
