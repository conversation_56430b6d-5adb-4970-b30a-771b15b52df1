syntax = "proto3";

package anis.daily_location_sales;

import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Anis.Cashflow.Report.Infra.Services.GrpcServices.Protos.DailyLocationSales"; 
 
service  DailyLocationSale {
	rpc GetAll (Request) returns (Response);
	rpc GetSaleAverage (GetSaleAverageRequest) returns (GetSaleAverageResponse);
}

message Request{
	google.protobuf.Int32Value current_page = 1; 
	google.protobuf.Int32Value page_size = 2; 
	google.protobuf.StringValue Location_id = 3;
	google.protobuf.Timestamp date_from = 4;
	google.protobuf.Timestamp date_to = 5;
	Currency purchase_currency = 6;
	bool group_results = 7;
}

message Response{
	int32 current_page = 1;
	int32 page_size = 2;
	int32 total = 3;
	repeated LocationSalesResponse Location_sales = 4;
}

message GetSaleAverageRequest{
	int32 days_count = 1;
	google.protobuf.StringValue Location_id = 2;

}

message GetSaleAverageResponse{
	google.protobuf.StringValue Location_id = 1;
	double average_sale = 2;
}



message LocationSalesResponse{
	google.protobuf.StringValue Location_id = 1;
	google.protobuf.StringValue Location_name = 2;
	google.protobuf.Timestamp date = 3;
	int32 total_sold = 4;
	double total_supplier_cost = 5;
	double total_price = 6;
	Currency purchase_currency = 7;
	google.protobuf.DoubleValue real_actual_cost = 8;
	google.protobuf.DoubleValue estimated_actual_cost = 9;
}

enum Currency {
	Non  = 0;
	LYD  = 1;
	USD  = 2;
}



