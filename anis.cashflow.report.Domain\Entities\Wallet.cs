﻿using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;
using Anis.Cashflow.Report.Domain.Models;

namespace Anis.Cashflow.Report.Domain.Entities
{
    public class Wallet
    {
        private Wallet() { }

        public Wallet(MessageBody<WalletCreatedData> model)
        {
            Id = model.AggregateId;
            Sequence = 1;
            Balance = 0;
            CurrencyType = model.Data.CurrencyType;
            SubscriptionId = model.Data.SubscriptionId;
            RegionId = model.Data.RegionId;
            Type = model.Data.Type;
            HolderId = model.Data.HolderId;
        }

        public Wallet(WalletInputModel model, decimal currentUrgent, decimal currentDelayed)
        {
            Id = model.WalletId;
            Sequence = model.Sequence;
            Balance = model.Balance;
            CurrencyType = model.CurrencyType;
            Type = model.Type;
            HolderId = model.HolderId;
            SubscriptionId = model.SubscriptionId;
            RegionId = model.RegionId;
            CurrentUrgent = currentUrgent;
            CurrentDelayed = currentDelayed;
            NetBalance = model.Balance - currentDelayed - currentUrgent;
        }

        public string Id { get; private set; }
        public int Sequence { get; private set; }
        public CurrencyType CurrencyType { get; private set; }
        public decimal Balance { get; private set; }
        public decimal CurrentUrgent { get; private set; }
        public decimal CurrentDelayed { get; private set; }
        public decimal NetBalance { get; private set; }
        public string SubscriptionId { get; private set; }
        public WalletType Type { get; private set; }
        public Subscription Subscription { get; private set; }
        public string RegionId { get; private set; }
        public Region Region { get; private set; }
        public string HolderId { get; private set; }
        public Holder Holder { get; set; }
        public DateTime? LastDelayRefundDate { get; private set; }

        private HashSet<SubscriptionWallet> _SubscriptionWallets = new HashSet<SubscriptionWallet>();
        public IReadOnlyCollection<SubscriptionWallet> SubscriptionWallets => _SubscriptionWallets;

        public void UpdateDebitBalance(decimal balance)
        {
            Sequence++;
            Balance += balance;
            NetBalance += balance;
        }

        public void UpdateCreditBalance(decimal balance)
        {
            Sequence++;
            Balance -= balance;
            NetBalance -= balance;
        }

        public void IncrementSequence()
        {
            Sequence++;
        }

        public void UpdateDebitCurrentUrgent(decimal debit)
        {
            CurrentUrgent += debit;
            NetBalance -= debit;
        }

        public void UpdateCreditCurrentUrgent(decimal credit)
        {
            CurrentUrgent -= credit;
            NetBalance += credit;
        }

        public void UpdateDebitCurrentDelayed(decimal debit)
        {
            CurrentDelayed += debit;
            NetBalance -= debit;
        }

        public void UpdateCreditCurrentDelayed(decimal credit, DateTime? dateTime)
        {
            CurrentDelayed -= credit;

            if (CurrentDelayed == 0)
            {
                LastDelayRefundDate = dateTime;
            }

            NetBalance += credit;
        }
    }
}
