﻿using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Helpers;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Resources;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.SubscriptionWallets;
using Anis.Cashflow.Report.Test.Grpc.Protos.Consumer;
using Calzolari.Grpc.Net.Client.Validation;
using Grpc.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;
using SubscriptionType = Anis.Cashflow.Report.Test.Grpc.Protos.Consumer.SubscriptionType;

namespace Anis.Cashflow.Report.Test.GrpcTest.Consumer
{
    public class IndexTest : TestBase
    {
        public IndexTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Index_WhenGivenInvalidRequest_ThrowException()
        {
            Initialize(salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.Region, region)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.Region, region)
                .Generate();

            var subscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddAsync(subscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var request = new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                DaysCount = -1
            };

            var exception = await Assert.ThrowsAsync<RpcException>(async () => await client.IndexAsync(request));

            Assert.NotEmpty(exception.Status.Detail);

            Assert.Equal(StatusCode.InvalidArgument, exception.StatusCode);

            Assert.Contains(exception.GetValidationErrors(), e => e.PropertyName.EndsWith("Days count"));
        }

        [Fact]
        public async Task Index_ValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.Region, region)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.Region, region)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                OperatorId = thirdAccount.Id,
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, thirdSubscription.Name);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, thirdSubscription.Name);

        }

        [Fact]
        public async Task Index_DefaultData_ReturnValid()
        {

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.Location, region).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.Region, region)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.Region, region)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request() { });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);

        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task Index_ValidDataWithCodeLengthTwoDigit_ReturnValid(bool isConfirmedAtNull)
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .RuleFor(r => r.ConfirmedAt, r => isConfirmedAtNull ? null : DateTime.UtcNow)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .RuleFor(r => r.ConfirmedAt, r => isConfirmedAtNull ? null : DateTime.UtcNow)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .RuleFor(r => r.ConfirmedAt, r => isConfirmedAtNull ? null : DateTime.UtcNow)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request() { CurrentPage = 1, PageSize = 25, LocationId = firstRegion.Id });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);

        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task Index_ValidDataWithCodeLengthMoreTwoDigit_ReturnValid(bool isConfirmedAtNull)
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .RuleFor(r => r.ConfirmedAt, r => isConfirmedAtNull ? null : DateTime.UtcNow)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .RuleFor(r => r.ConfirmedAt, r => isConfirmedAtNull ? null : DateTime.UtcNow)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .RuleFor(r => r.ConfirmedAt, r => isConfirmedAtNull ? null : DateTime.UtcNow)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = secondRegion.Id
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.WalletDtos.Count());

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, secondSubscription.Name);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithOperatorSubscription_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request() { CurrentPage = 1, PageSize = 25, LocationId = firstRegion.Id, Type = SubscriptionType.Operator });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Single(response.WalletDtos);

            Assert.Equal(1, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithCurrencyType_ReturnValid()
        {

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = firstRegion.Id,
                Type = SubscriptionType.Operator,
                DebtType = DebtType.All
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Single(response.WalletDtos);

            Assert.Equal(1, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithPhoneFilter_ReturnValid()
        {

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .RuleFor(r => r.Phone, "**********")
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(r => r.CurrencyType, Domain.Enums.CurrencyType.Lyd)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = firstRegion.Id,
                PhoneNumber = "**********"
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Single(response.WalletDtos);

            Assert.Equal(1, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithDebitOnlyFilter_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(
                new Request()
                {
                    CurrentPage = 1,
                    PageSize = 25,
                    LocationId = firstRegion.Id,
                    DebtType = DebtType.All
                });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.WalletDtos.Count());

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_BlackListSubscriptionsModel_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            var firstId = "B565A818-8C58-4E5E-2B3E-08DA3675F384";

            var blackListSubscriptions = new List<string>()
            {
                 firstId
            };

            Initialize(averageSaleLocation: averageSaleLocation,
                  blackListSubscriptions: blackListSubscriptions);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Id, firstId)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(
                new Request()
                {
                    CurrentPage = 1,
                    PageSize = 25,
                    LocationId = firstRegion.Id,
                    DebtType = DebtType.All
                });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.WalletDtos.Count());

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithDebitTypNone_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 200)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 0)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, secondRegion.Id)
                .RuleFor(w => w.Balance, 0)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(
                new Request()
                {
                    CurrentPage = 1,
                    PageSize = 25,
                    LocationId = firstRegion.Id,
                    DebtType = DebtType.None
                });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.WalletDtos.Count());

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithDebitTypDebitOnly_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var forthAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var forthSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, forthAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()

                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, -200)
                .RuleFor(w => w.CurrentUrgent, -100)
                .RuleFor(w => w.CurrentDelayed, -90)
                .Generate();

            var secondWallet = new WalletFaker()

                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 30)
                .RuleFor(w => w.CurrentUrgent, 300)
                .RuleFor(w => w.CurrentDelayed, -20)
                .Generate();

            var thirdWallet = new WalletFaker()

                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 1500)
                .RuleFor(w => w.CurrentUrgent, -40)
                .RuleFor(w => w.CurrentDelayed, 70)
                .Generate();

            var forthWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, forthSubscription.Id)
                .RuleFor(s => s.Subscription, forthSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 170)
                .RuleFor(w => w.CurrentUrgent, -60)
                .RuleFor(w => w.CurrentDelayed, -40)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount, forthAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription, forthSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet, forthWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(
                new Request()
                {
                    CurrentPage = 1,
                    PageSize = 25,
                    LocationId = firstRegion.Id,
                    DebtType = DebtType.DebitOnly
                });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            var thirdWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            var forthWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == forthWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(3, response.WalletDtos.Count());

            Assert.Equal(3, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var resThirdWallet = response.WalletDtos.Single(i => i.Id == thirdWalletDb.Id);

            var resForthWallet = response.WalletDtos.SingleOrDefault(i => i.Id == forthWalletDb.Id);

            Assert.Null(resForthWallet);
            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondSubscription.Name);
            EntityAssert.AssertEquality(resThirdWallet, thirdWalletDb, thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithDebitTypeUrgent_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.Location, secondRegion)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, thirdAccount)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.Region, firstRegion)
                .RuleFor(w => w.Balance, 200)
                .RuleFor(w => w.CurrentUrgent, 500)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.Region, firstRegion)
                .RuleFor(w => w.Balance, 0)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.Region, firstRegion)
                .RuleFor(w => w.Balance, 0)
                .RuleFor(w => w.CurrentUrgent, 600)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(
                new Request()
                {
                    CurrentPage = 1,
                    PageSize = 25,
                    LocationId = firstRegion.Id,
                    DebtType = DebtType.Urgent
                });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).ThenInclude(a => a.Location).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.WalletDtos.Count());

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithDebitTypeDelayed_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 200)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 500)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 0)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 0)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 600)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(
                new Request()
                {
                    CurrentPage = 1,
                    PageSize = 25,
                    LocationId = firstRegion.Id,
                    DebtType = DebtType.Delayed
                });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).ThenInclude(a => a.Location).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.WalletDtos.Count());

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithDebitTypeHasDebit_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, -200)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 0)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, -60)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(
                new Request()
                {
                    CurrentPage = 1,
                    PageSize = 25,
                    LocationId = firstRegion.Id,
                    DebtType = DebtType.HasDebit
                });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).ThenInclude(a => a.Location).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.WalletDtos.Count());

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithAscendingSort_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 3000)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 1000)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 2000)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(
                new Request()
                {
                    CurrentPage = 1,
                    PageSize = 25,
                    LocationId = firstRegion.Id,
                    DebtType = DebtType.All,
                    IsAscending = true
                });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).ThenInclude(a => a.Location).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).ThenInclude(a => a.Location).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);
            var thirdWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).ThenInclude(a => a.Location).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(3, response.WalletDtos.Count());

            Assert.Equal(3, response.Total);

            var firstResult = response.WalletDtos.First();
            var secondResult = response.WalletDtos.Skip(1).Take(1).Single();
            var thirdResult = response.WalletDtos.Last();

            EntityAssert.AssertEquality(thirdResult, firstWalletDb, firstSubscription.Name);
            EntityAssert.AssertEquality(firstResult, secondWalletDb, secondSubscription.Name);
            EntityAssert.AssertEquality(secondResult, thirdWalletDb, thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithDascendingSort_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 1000)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 3000)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 2000)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(
                new Request()
                {
                    CurrentPage = 1,
                    PageSize = 25,
                    LocationId = firstRegion.Id,
                    DebtType = DebtType.All,
                    IsAscending = false
                });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).ThenInclude(a => a.Location).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).ThenInclude(a => a.Location).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);
            var thirdWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).ThenInclude(a => a.Location).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(3, response.WalletDtos.Count());

            Assert.Equal(3, response.Total);

            var firstResult = response.WalletDtos.First();
            var secondResult = response.WalletDtos.Skip(1).Take(1).Single();
            var thirdResult = response.WalletDtos.Last();

            EntityAssert.AssertEquality(thirdResult, firstWalletDb, firstSubscription.Name);
            EntityAssert.AssertEquality(firstResult, secondWalletDb, secondSubscription.Name);
            EntityAssert.AssertEquality(secondResult, thirdWalletDb, thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataAndProfitWallet_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.Region, region)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.Region, region)
                .Generate();

            var thirdWallet = new WalletFaker()
               .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
               .RuleFor(s => s.Subscription, thirdSubscription)
               .RuleFor(s => s.Type, WalletType.Normal)
               .RuleFor(r => r.Region, region)
               .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                WalletTypeFilter = WalletTypeFilter.ProfitWallets
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWallet.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWallet.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataAndNormalOnlyWallet_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.Region, region)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.Region, region)
                .Generate();

            var thirdWallet = new WalletFaker()
               .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
               .RuleFor(s => s.Subscription, thirdSubscription)
               .RuleFor(s => s.Type, WalletType.Normal)
               .RuleFor(r => r.Region, region)
               .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                WalletTypeFilter = WalletTypeFilter.NormalWallets
            });

            var firstWalletDb = await context.Wallets
               .Include(s => s.Subscription)
               .ThenInclude(a => a.Account).
                Include(s => s.Region)
               .SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenWalletIdIsNotExistInGetAverageSaleWalletReturn_ReturnValid()
        {
            var secAverageSale = 222.3m;

            var firstWalletNetBalance = 1000;
            var secondWalletNetBalance = 2000;

            var firstWalletId = Guid.NewGuid().ToString();
            var secondWalletId = Guid.NewGuid().ToString();

            var walletAverageSales = new List<WalletAverageSaleModel>()
            {
                new WalletAverageSaleModel
                {
                    WalletId =secondWalletId,
                    AverageSales=secAverageSale
                }
            };

            Initialize(salesWalletAverage: walletAverageSales);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.Location, region).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Id, firstWalletId)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.Region, region)
                .RuleFor(s => s.NetBalance, firstWalletNetBalance)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Id, secondWalletId)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.Region, region)
                .RuleFor(s => s.NetBalance, secondWalletNetBalance)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(walletDto: resFirstWallet,
                                        dbWallet: firstWalletDb,
                                        operatorName: firstSubscription.Name,
                                        averageSale: 0,
                                        positiveBalanceRate: 0,
                                        totalSalesLastDay: 0);

            EntityAssert.AssertEquality(walletDto: resSecondWallet,
                                        dbWallet: secondWalletDb,
                                        operatorName: secondSubscription.Name,
                                        averageSale: secAverageSale,
                                        positiveBalanceRate: 8.996m);
        }

        [Theory]
        [InlineData(0, 0)]
        [InlineData(111.8, 8.944)]
        public async Task Index_AddAverageSales_ReturnValid(decimal firstAverageSale, decimal expectedPositiveBalanceRate)
        {
            var secAverageSale = 222.3m;

            var firstWalletNetBalance = 1000;
            var secondWalletNetBalance = 2000;

            var firstWalletId = Guid.NewGuid().ToString();
            var secondWalletId = Guid.NewGuid().ToString();

            var walletAverageSales = new List<WalletAverageSaleModel>()
            {
                new WalletAverageSaleModel
                {
                    WalletId =firstWalletId,
                    AverageSales=firstAverageSale
                },

                new WalletAverageSaleModel
                {
                    WalletId =secondWalletId,
                    AverageSales=secAverageSale
                }
            };

            Initialize(salesWalletAverage: walletAverageSales);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.Location, region).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Id, firstWalletId)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.Region, region)
                .RuleFor(s => s.NetBalance, firstWalletNetBalance)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Id, secondWalletId)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.Region, region)
                .RuleFor(s => s.NetBalance, secondWalletNetBalance)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb,
                averageSale: firstAverageSale,
                operatorName: firstSubscription.Name,
                positiveBalanceRate: expectedPositiveBalanceRate);

            EntityAssert.AssertEquality(resSecondWallet,
                secondWalletDb,
                operatorName: secondSubscription.Name,
                averageSale: secAverageSale,
                positiveBalanceRate: 8.996m);
        }

        [Theory]
        [InlineData(0, 0)]
        [InlineData(1000.50, 1523.35)]
        public async Task Index_AddTotalSales_ReturnValid(decimal firstTotalSalesLastDay, decimal secondTotalSalesLastDay)
        {
            var firstWalletNetBalance = 1000;
            var secondWalletNetBalance = 2000;

            var firstWalletId = Guid.NewGuid().ToString();
            var secondWalletId = Guid.NewGuid().ToString();

            var walletAverageSales = new List<WalletAverageSaleModel>()
            {
                new WalletAverageSaleModel
                {
                    WalletId =firstWalletId,
                    TotalSalesLastDay=firstTotalSalesLastDay
                },

                new WalletAverageSaleModel
                {
                    WalletId =secondWalletId,
                    TotalSalesLastDay=secondTotalSalesLastDay
                }
            };

            Initialize(salesWalletAverage: walletAverageSales);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.Location, region).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Id, firstWalletId)

                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.Region, region)
                .RuleFor(s => s.NetBalance, firstWalletNetBalance)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Id, secondWalletId)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.Region, region)
                .RuleFor(s => s.NetBalance, secondWalletNetBalance)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(walletDto: resFirstWallet,
                                        dbWallet: firstWalletDb,
                                        operatorName: firstSubscription.Name,
                                        totalSalesLastDay: firstTotalSalesLastDay);

            EntityAssert.AssertEquality(walletDto: resSecondWallet,
                                        dbWallet: secondWalletDb,
                                        operatorName: secondSubscription.Name,
                                        totalSalesLastDay: secondTotalSalesLastDay);
        }

        [Theory]
        [InlineData(0, 0, 0, 0)]
        [InlineData(111.8, 8.944, 1000.50, 1523.35)]
        public async Task Index_AddAverageSalesAndTotalSales_ReturnValid(
            decimal firstAverageSale,
            decimal expectedPositiveBalanceRate,
            decimal firstTotalSalesLastDay,
            decimal secondTotalSalesLastDay)
        {
            var secAverageSale = 222.3m;

            var firstWalletNetBalance = 1000;
            var secondWalletNetBalance = 2000;

            var firstWalletId = Guid.NewGuid().ToString();
            var secondWalletId = Guid.NewGuid().ToString();

            var walletAverageSales = new List<WalletAverageSaleModel>()
            {
                new WalletAverageSaleModel
                {
                    WalletId =firstWalletId,
                    AverageSales=firstAverageSale,
                    TotalSalesLastDay= firstTotalSalesLastDay
                },

                new WalletAverageSaleModel
                {
                    WalletId =secondWalletId,
                    AverageSales=secAverageSale,
                    TotalSalesLastDay= secondTotalSalesLastDay
                }
            };

            Initialize(salesWalletAverage: walletAverageSales);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.Location, region).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Id, firstWalletId)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.Region, region)
                .RuleFor(s => s.NetBalance, firstWalletNetBalance)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Id, secondWalletId)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.Region, region)
                .RuleFor(s => s.NetBalance, secondWalletNetBalance)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(walletDto: resFirstWallet,
                                        dbWallet: firstWalletDb,
                                        operatorName: firstSubscription.Name,
                                        averageSale: firstAverageSale,
                                        positiveBalanceRate: expectedPositiveBalanceRate,
                                        totalSalesLastDay: firstTotalSalesLastDay);

            EntityAssert.AssertEquality(walletDto: resSecondWallet,
                                        dbWallet: secondWalletDb,
                                        operatorName: secondSubscription.Name,
                                        averageSale: secAverageSale,
                                        positiveBalanceRate: 8.996m,
                                        totalSalesLastDay: secondTotalSalesLastDay);
        }

        #region When set wallet id in request

        [Fact]
        public async Task Index_ValidDataAndRecievedWalletId_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation,
                       salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.Region, region)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Type, WalletType.Normal)
                .RuleFor(r => r.Region, region)
                .Generate();

            var thirdWallet = new WalletFaker()
               .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
               .RuleFor(s => s.Subscription, thirdSubscription)
               .RuleFor(s => s.Type, WalletType.Normal)
               .RuleFor(r => r.Region, region)
               .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                OperatorId = firstAccount.Id,
            });

            var firstWalletDb = await context.Wallets
               .Include(s => s.Subscription)
               .ThenInclude(a => a.Account).
                Include(s => s.Region)
               .SingleAsync(i => i.Id == firstWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataAndNormalOnlyWalletAndRecievedWalletId_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation,
                       salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.Region, region)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Type, WalletType.Normal)
                .RuleFor(r => r.Region, region)
                .Generate();

            var thirdWallet = new WalletFaker()
               .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
               .RuleFor(s => s.Subscription, thirdSubscription)
               .RuleFor(s => s.Type, WalletType.Normal)
               .RuleFor(r => r.Region, region)
               .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                WalletTypeFilter = WalletTypeFilter.NormalWallets,
                OperatorId = thirdAccount.Id
            });

            var firstWalletDb = await context.Wallets
               .Include(s => s.Subscription)
               .ThenInclude(a => a.Account).
                Include(s => s.Region)
               .SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == thirdWallet.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithDebitTypeHasDebitAndRecievedWalletId_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, -200)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, 0)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(w => w.Balance, -60)
                .RuleFor(w => w.CurrentUrgent, 0)
                .RuleFor(w => w.CurrentDelayed, 0)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(
                new Request()
                {
                    CurrentPage = 1,
                    PageSize = 25,
                    LocationId = firstRegion.Id,
                    DebtType = DebtType.HasDebit
                });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).ThenInclude(a => a.Location).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.WalletDtos.Count());

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, thirdSubscription.Name);
        }

        #endregion

        [Fact]
        public async Task Index_ValidDataWithSearechByOperatorItWasSendedCashDeposit_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var cashDepositAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondCashDepositAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdCashDepositAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var firstCashDepositSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, cashDepositAccount)
                .Generate();

            var secondCashDepositSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondCashDepositAccount)
                .Generate();

            var thirdCashDepositSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdCashDepositAccount.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var subscriptionWalletFaker = new SubscriptionWalletFaker()
                .RuleFor(a => a.Subscription, firstCashDepositSubscription)
                .RuleFor(a => a.WalletId, firstWallet.Id)
                .Generate();

            var secondSubscriptionWalletFaker = new SubscriptionWalletFaker()
                .RuleFor(a => a.Subscription, secondCashDepositSubscription)
                .RuleFor(a => a.WalletId, secondWallet.Id)
                .Generate();

            var thirdSubscriptionWalletFaker = new SubscriptionWalletFaker()
                .RuleFor(a => a.Subscription, thirdCashDepositSubscription)
                .RuleFor(a => a.WalletId, thirdWallet.Id)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount,
                cashDepositAccount, secondCashDepositAccount, thirdCashDepositAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(subscriptionWalletFaker,
                secondSubscriptionWalletFaker, thirdSubscriptionWalletFaker);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = firstRegion.Id,
                Type = SubscriptionType.AllOptions,
                OperatorId = cashDepositAccount.Id
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Single(response.WalletDtos);

            Assert.Equal(1, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstCashDepositSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithSearechByOperatorIdHaveMultiWalltes_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var firstRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secondRegion = new RegionFaker()
                .Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var cashDepositAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var secondCashDepositAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var thirdCashDepositAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var firstCashDepositSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, cashDepositAccount)
                .Generate();

            var secondCashDepositSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondCashDepositAccount)
                .Generate();

            var thirdCashDepositSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdCashDepositAccount.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Operator)
                .RuleFor(a => a.Account, firstAccount)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, secondAccount)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .Generate();

            var subscriptionWalletFaker = new SubscriptionWalletFaker()
                .RuleFor(a => a.Subscription, firstCashDepositSubscription)
                .RuleFor(a => a.WalletId, firstWallet.Id)
                .Generate();

            var secondSubscriptionWalletFaker = new SubscriptionWalletFaker()
                .RuleFor(a => a.Subscription, firstCashDepositSubscription)
                .RuleFor(a => a.WalletId, secondWallet.Id)
                .Generate();

            var thirdSubscriptionWalletFaker = new SubscriptionWalletFaker()
                .RuleFor(a => a.Subscription, thirdCashDepositSubscription)
                .RuleFor(a => a.WalletId, thirdWallet.Id)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount,
                cashDepositAccount, secondCashDepositAccount, thirdCashDepositAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(subscriptionWalletFaker,
                secondSubscriptionWalletFaker, thirdSubscriptionWalletFaker);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = firstRegion.Id,
                Type = SubscriptionType.AllOptions,
                OperatorId = cashDepositAccount.Id
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);
            var thirdWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);
            Assert.NotNull(firstWalletDb);
            Assert.NotNull(secondWalletDb);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.WalletDtos.Count());

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);
            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstCashDepositSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, firstCashDepositSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenExpirationOnlyIsFalseReturnAllAccountsWithRemainingTime_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();
            
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            
            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker()
                .Generate();
           
            var account = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .RuleFor(r => r.ExpiryAt, DateTime.UtcNow.AddHours(1)) // Active account
                .Generate();

            var subscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, account)
                .Generate();

            var wallet = new WalletFaker()
                .RuleFor(s => s.Subscription, subscription)
                .RuleFor(r => r.Region, region)
                .Generate();

            var subscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, subscription)
                .RuleFor(s => s.Wallet, wallet)
                .Generate();

            await context.SubscriptionWallets.AddAsync(subscriptionWallet);
            
            await context.SaveChangesAsync();
            
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                ExpirationOnly = true
            });

            Assert.NotNull(response);
            
            Assert.Empty(response.WalletDtos);
            
            Assert.Equal(0, response.Total);
        }

        [Fact]
        public async Task Index_ValidDataWithExpiredAccountAndExpirationOnly_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();
            
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
           
            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker()
                .Generate();
            
            var account = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .RuleFor(r => r.ExpiryAt, DateTime.UtcNow.AddHours(-1)) // Expired account
                .Generate();

            var subscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, account)
                .Generate();

            var wallet = new WalletFaker()
                .RuleFor(s => s.Subscription, subscription)
                .RuleFor(r => r.Region, region)
                .Generate();

            var subscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, subscription)
                .RuleFor(s => s.Wallet, wallet)
                .Generate();

            await context.SubscriptionWallets.AddAsync(subscriptionWallet);
            
            await context.SaveChangesAsync();
            
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                ExpirationOnly = true
            });

            var dbWallet = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == wallet.Id);

            Assert.NotNull(response);
            
            Assert.Single(response.WalletDtos);
            
            Assert.Equal(1, response.Total);

            var resWallet = response.WalletDtos.Single(i => i.Id == dbWallet.Id);
            
            EntityAssert.AssertEquality(resWallet, dbWallet, subscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithMixedExpirationStatesFilterCorrectly_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();
            
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            
            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker().Generate();

            // Create expired account
            var expiredAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .RuleFor(r => r.ExpiryAt, DateTime.UtcNow.AddHours(-1))
                .Generate();

            var expiredSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, expiredAccount)
                .Generate();

            var expiredWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, expiredSubscription)
                .RuleFor(r => r.Region, region)
                .Generate();

            var expiredSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, expiredSubscription)
                .RuleFor(s => s.Wallet, expiredWallet)
                .Generate();

            // Create active account
            var activeAccount = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .RuleFor(r => r.ExpiryAt, DateTime.UtcNow.AddHours(1))
                .Generate();

            var activeSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, activeAccount)
                .Generate();

            var activeWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, activeSubscription)
                .RuleFor(r => r.Region, region)
                .Generate();

            var activeSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, activeSubscription)
                .RuleFor(s => s.Wallet, activeWallet)
                .Generate();

            await context.Regions.AddAsync(region);
            
            await context.Accounts.AddRangeAsync(expiredAccount, activeAccount);
            
            await context.Subscriptions.AddRangeAsync(expiredSubscription, activeSubscription);
            
            await context.Wallets.AddRangeAsync(expiredWallet, activeWallet);
            
            await context.SubscriptionWallets.AddRangeAsync(expiredSubscriptionWallet, activeSubscriptionWallet);
            
            await context.SaveChangesAsync();
            
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                ExpirationOnly = true
            });

            var dbExpiredWallet = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == expiredWallet.Id);

            Assert.NotNull(response);
            
            Assert.Single(response.WalletDtos);
            
            Assert.Equal(1, response.Total);

            var resWallet = response.WalletDtos.Single(i => i.Id == dbExpiredWallet.Id);
            
            EntityAssert.AssertEquality(resWallet, dbExpiredWallet, expiredSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataWithExpirationOnlyAndTimeRemainingDisplayCorrectTime_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();
            
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            
            var client = new CashflowReportConsumerV5.CashflowReportConsumerV5Client(Channel);

            var region = new RegionFaker()
                .Generate();
            
            var account = new AccountFaker()
                .RuleFor(r => r.Location, region)
                .RuleFor(r => r.ExpiryAt, DateTime.UtcNow.AddHours(-1)) // Expired account
                .Generate();

            var subscription = new SubscriptionFaker()
                .RuleFor(a => a.Account, account)
                .Generate();

            var wallet = new WalletFaker()
                .RuleFor(s => s.Subscription, subscription)
                .RuleFor(r => r.Region, region)
                .Generate();

            var subscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, subscription)
                .RuleFor(s => s.Wallet, wallet)
                .Generate();

            await context.SubscriptionWallets.AddAsync(subscriptionWallet);
            
            await context.SaveChangesAsync();
            
            context.ChangeTracker.Clear();

            var response = await client.IndexAsync(new Request()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                ExpirationOnly = true
            });

            Assert.NotNull(response);
            
            Assert.Single(response.WalletDtos);
            
            var resWallet = response.WalletDtos.First();

            // Expired accounts should have null remaining time
            Assert.Null(resWallet.RemainingTime);
            // Account state should be disabled
            Assert.Contains(resWallet.AccountState, "Disabled");
        }
    }
}
