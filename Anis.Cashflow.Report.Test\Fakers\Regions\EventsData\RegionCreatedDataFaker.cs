﻿using Anis.Cashflow.Report.Domain.EventsData.Regions;

namespace Anis.Cashflow.Report.Test.Fakers.Regions.EventsData
{
    public class RegionCreatedDataFaker : PrivateFaker<RegionCreatedData>
    {
        public RegionCreatedDataFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.ArabicName, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.EnglishName, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.Code, f => f.Random.AlphaNumeric(10));
        }
    }
}
