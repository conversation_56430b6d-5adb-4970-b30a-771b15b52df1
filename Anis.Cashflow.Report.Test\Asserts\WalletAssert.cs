﻿using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;
using Anis.Cashflow.Report.Domain.Models;

namespace Anis.Cashflow.Report.Test.Asserts
{
    public static class WalletAssert
    {
        public static void AssertEquality(
            Wallet walletFaker,
            Wallet dbWallet,
            long sequence = 1)
        {
            Assert.Equal(walletFaker.Id, dbWallet.Id);
            Assert.Equal(walletFaker.Balance, dbWallet.Balance);
            Assert.Equal(sequence, dbWallet.Sequence);
            Assert.Equal(walletFaker.CurrencyType, dbWallet.CurrencyType);
            Assert.Equal(walletFaker.CurrentUrgent, dbWallet.CurrentUrgent);
            Assert.Equal(walletFaker.CurrentDelayed, dbWallet.CurrentDelayed);
            Assert.Equal(walletFaker.NetBalance, dbWallet.NetBalance);
            Assert.Equal(walletFaker.SubscriptionId, dbWallet.SubscriptionId);
            Assert.Equal(walletFaker.Type, dbWallet.Type);
            Assert.Equal(walletFaker.RegionId, dbWallet.RegionId);
            Assert.Equal(walletFaker.HolderId, dbWallet.HolderId);
        }

        public static void AssertTransactionCreatedEquality(
        Wallet walletFaker,
        Wallet dbWallet,
        MessageBody<TransactionCreatedData> messageBody,
        DateTime? lastDelayRefundDate)
        {
            Assert.NotNull(dbWallet);

            Assert.Equal(walletFaker.Id, dbWallet.Id);
            Assert.Equal(walletFaker.Sequence + 1, dbWallet.Sequence);
            Assert.Equal(walletFaker.CurrencyType, dbWallet.CurrencyType);
            Assert.Equal(walletFaker.SubscriptionId, dbWallet.SubscriptionId);
            Assert.Equal(walletFaker.Type, dbWallet.Type);
            Assert.Equal(walletFaker.RegionId, dbWallet.RegionId);
            Assert.Equal(messageBody.AggregateId, dbWallet.Id);
            Assert.Equal(messageBody.Sequence, dbWallet.Sequence);
            Assert.Equal(messageBody.Version, 2);
            Assert.Equal(walletFaker.HolderId, dbWallet.HolderId);

            if (lastDelayRefundDate != null)
                Assert.Equal(messageBody.DateTime, lastDelayRefundDate);
        }

        public static void AssertEquality(
            Wallet dbWallet,
            MessageBody<TransactionCreatedData> messageBody,
            Wallet walletFaker,
            bool isDebit = false
            )
        {
            AssertTransactionCreatedEquality(walletFaker, dbWallet, messageBody, null);

            Assert.Equal(walletFaker.CurrentUrgent, dbWallet.CurrentUrgent);
            Assert.Equal(walletFaker.CurrentDelayed, dbWallet.CurrentDelayed);

            var data = messageBody.Data;

            if (isDebit)
                Assert.Equal(walletFaker.Balance + data.Debit, dbWallet.Balance);

            else
                Assert.Equal(walletFaker.Balance - data.Credit, dbWallet.Balance);
        }

        public static void AssertUrgentOrDelayedEquality(
            Wallet dbWallet,
            MessageBody<TransactionCreatedData> messageBody,
            Wallet walletFaker,
            bool isDebit = false,
            bool isUrgent = false,
            DateTime? lastDelayRefundDate = null
          )
        {
            AssertTransactionCreatedEquality(walletFaker, dbWallet, messageBody, lastDelayRefundDate);

            var data = messageBody.Data;
            Assert.Equal(dbWallet.Balance - dbWallet.CurrentDelayed - dbWallet.CurrentUrgent, dbWallet.NetBalance);

            if (isDebit)
            {
                Assert.Equal(walletFaker.Balance + data.Debit, dbWallet.Balance);

                if (isUrgent)
                    Assert.Equal(walletFaker.CurrentUrgent + data.Debit, dbWallet.CurrentUrgent);
                else
                    Assert.Equal(walletFaker.CurrentDelayed + data.Debit, dbWallet.CurrentDelayed);

            }
            else
                Assert.Equal(walletFaker.Balance - data.Credit, dbWallet.Balance);
        }

        public static void AssertRefundUrgentOrDelayedEquality(
            Wallet dbWallet,
            MessageBody<TransactionCreatedData> messageBody,
            Wallet walletFaker,
            DateTime? lastDelayRefundDate = null,
            bool isDebit = false,
            bool isUrgent = false
            )
        {
            AssertTransactionCreatedEquality(walletFaker, dbWallet, messageBody, lastDelayRefundDate);

            var data = messageBody.Data;

            Assert.Equal(dbWallet.Balance - dbWallet.CurrentDelayed - dbWallet.CurrentUrgent, dbWallet.NetBalance);

            if (isDebit)
                Assert.Equal(walletFaker.Balance + data.Debit, dbWallet.Balance);

            else
            {
                Assert.Equal(walletFaker.Balance - data.Credit, dbWallet.Balance);

                if (isUrgent)
                    Assert.Equal(walletFaker.CurrentUrgent - data.Credit, dbWallet.CurrentUrgent);
                else
                    Assert.Equal(walletFaker.CurrentDelayed - data.Credit, dbWallet.CurrentDelayed);
            }
        }

        internal static void AssertEquality(
            Wallet dbWallet,
            MessageBody<WalletCreatedData> messageBody)
        {
            Assert.Equal(messageBody.Sequence, dbWallet.Sequence);
            Assert.Equal(messageBody.AggregateId, dbWallet.Id);
            Assert.Equal(messageBody.Data.RegionId, dbWallet.RegionId);
            Assert.Equal(messageBody.Data.SubscriptionId, dbWallet.SubscriptionId);
            Assert.Equal(messageBody.Data.Type, dbWallet.Type);
            Assert.Equal(messageBody.Data.CurrencyType, dbWallet.CurrencyType);
            Assert.Equal(messageBody.Data.HolderId, dbWallet.HolderId);
        }
        public static void AssertEquality(
           Wallet walletFaker,
           Wallet dbWallet,
           long sequence,
           decimal balance,
           decimal CurrentUrgent,
           decimal currentDelayed)
        {
            Assert.Equal(walletFaker.Id, dbWallet.Id);
            Assert.Equal(balance, dbWallet.Balance);
            Assert.Equal(balance - currentDelayed - CurrentUrgent, dbWallet.NetBalance);
            Assert.Equal(sequence, dbWallet.Sequence);
            Assert.Equal(walletFaker.CurrencyType, dbWallet.CurrencyType);
            Assert.Equal(CurrentUrgent, dbWallet.CurrentUrgent);
            Assert.Equal(currentDelayed, dbWallet.CurrentDelayed);
            Assert.Equal(walletFaker.SubscriptionId, dbWallet.SubscriptionId);
            Assert.Equal(walletFaker.Type, dbWallet.Type);
            Assert.Equal(walletFaker.RegionId, dbWallet.RegionId);
            Assert.Equal(dbWallet.Balance - (dbWallet.CurrentDelayed + dbWallet.CurrentUrgent), dbWallet.NetBalance);
            Assert.Equal(walletFaker.HolderId, dbWallet.HolderId);
        }

        internal static void AssertEquality(Wallet dbWallet,
            WalletInputModel model,
            decimal CurrentUrgent,
            decimal currentDelayed)
        {
            Assert.Equal(model.WalletId, dbWallet.Id);
            Assert.Equal(model.Balance, dbWallet.Balance);
            Assert.Equal(model.Sequence, dbWallet.Sequence);
            Assert.Equal(model.CurrencyType, dbWallet.CurrencyType);
            Assert.Equal(model.SubscriptionId, dbWallet.SubscriptionId);
            Assert.Equal(model.Type, dbWallet.Type);
            Assert.Equal(model.RegionId, dbWallet.RegionId);
            Assert.Equal(CurrentUrgent, dbWallet.CurrentUrgent);
            Assert.Equal(currentDelayed, dbWallet.CurrentDelayed);
            Assert.Equal(model.Balance - currentDelayed - CurrentUrgent, dbWallet.NetBalance);
            Assert.Equal(dbWallet.Balance - (dbWallet.CurrentDelayed + dbWallet.CurrentUrgent), dbWallet.NetBalance);
        }
    }
}
