﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.AccountHandlers
{
    public class AccountConfirmedHandler : IRequestHandler<MessageBody<AccountConfirmedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public AccountConfirmedHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> Handle(MessageBody<AccountConfirmedData> message, CancellationToken cancellationToken)
        {
            var account = await _unitOfWork.Accounts.FindAsync(message.AggregateId);

            if (account == null)
            {
                if (!await _unitOfWork.Regions.AnyAsync(message.Data.LocationId))
                    return false;

                account = new Account(message);

                await _unitOfWork.Accounts.AddAsync(account);
            }

            else
            {
                if (account.Sequence != message.Sequence - 1)
                    return account.Sequence >= message.Sequence;

                account.Modify(message);
            }

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}