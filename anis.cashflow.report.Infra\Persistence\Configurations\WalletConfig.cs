﻿using Anis.Cashflow.Report.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Anis.Cashflow.Report.Infra.Persistence.Configurations
{
    public class WalletConfig : IEntityTypeConfiguration<Wallet>
    {
        public void Configure(EntityTypeBuilder<Wallet> builder)
        {
            builder.Property(c => c.Id).HasMaxLength(Config.StringIdLength).IsRequired();

            builder.Property(c => c.SubscriptionId).HasMaxLength(Config.StringIdLength).IsRequired();

            builder.Property(c => c.RegionId).HasMaxLength(Config.StringIdLength).IsRequired();

            builder.Property(c => c.Sequence).IsConcurrencyToken();

            builder.Property(c => c.CurrentUrgent).HasColumnType(Config.DecimalThreeDigit);

            builder.Property(c => c.CurrentDelayed).HasColumnType(Config.DecimalThreeDigit);

            builder.Property(c => c.Balance).HasColumnType(Config.DecimalThreeDigit);

            builder.Property(c => c.NetBalance).HasColumnType(Config.DecimalThreeDigit);

            builder.Property(e => e.CurrencyType).HasConversion<string>();

            builder.Property(e => e.Type).HasConversion<string>();

            builder.Property(e => e.HolderId).HasMaxLength(Config.StringIdLength).IsRequired(false);

            builder.HasOne(i => i.Subscription)
                .WithMany(i => i.Wallets)
                .HasForeignKey(i => i.SubscriptionId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(i => i.Region)
                .WithMany(i => i.Wallets)
                .HasForeignKey(i => i.RegionId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.HasOne(i => i.Holder)
                .WithMany(i => i.Wallets)
                .HasForeignKey(i => i.HolderId)
                .OnDelete(DeleteBehavior.Restrict);
        }
    }
}
