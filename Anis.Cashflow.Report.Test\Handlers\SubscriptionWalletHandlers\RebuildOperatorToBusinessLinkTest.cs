﻿using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Exceptions;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Fakers.SubscriptionWallets;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.SubscriptionWalletHandlers
{
    public class RebuildOperatorToBusinessLinkTest : TestBase
    {
        public RebuildOperatorToBusinessLinkTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task RebuildOperatorToBusinessLink_WhenCalled_ShouldRebuildLinks()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var createWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            await context.Wallets.AddAsync(createWallet);
            await context.Subscriptions.AddAsync(createSubscription);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var expectedLinkDateTime = DateTime.UtcNow;

            // Act: 
            await mediator.Send(new OperatorToBusinessLinkRebuildModel
            {
                SubscriptionId = createSubscription.Id,
                SubscriptionWallets = new List<SubscriptionWalletBuild>
                {
                    new SubscriptionWalletBuild
                    {
                        WalletId = createWallet.Id,
                        LinkDateTime = expectedLinkDateTime
                    }
                },

                StateVersion = 5
            });

            // Assert:
            var subscriptionWallets = await context.SubscriptionWallets
                .Where(sw => sw.SubscriptionId == createSubscription.Id)
                .Include(sw => sw.Subscription)
                .ToListAsync();

            Assert.NotEmpty(subscriptionWallets);
            Assert.All(subscriptionWallets, sw => Assert.Equal(createWallet.Id, sw.WalletId));
            Assert.All(subscriptionWallets, sw => Assert.Equal(expectedLinkDateTime, sw.LinkDateTime!.Value, TimeSpan.FromMinutes(1)));
            Assert.Equal(5, subscriptionWallets[0].Subscription.StateVersion);
        }

        [Fact]
        public async Task RebuildOperatorToBusinessLink_WhenCalledWithInvalidSubscription_ShouldNotRebuildLinks()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            // Act:

            var request = new OperatorToBusinessLinkRebuildModel
            {
                SubscriptionId = Guid.NewGuid().ToString(),
                SubscriptionWallets = new List<SubscriptionWalletBuild>(),
                StateVersion = 5
            };

            var exception = await Assert.ThrowsAsync<AppException>(async () => await mediator.Send(request));

            Assert.NotEmpty(exception.Message);

            Assert.Equal(ExceptionStatusCode.NotFound, exception.StatusCode);

            Assert.Equal($"No subscription found for id: {request.SubscriptionId}", exception.Message);

            // Assert:
            var subscriptionWallets = await context.SubscriptionWallets.ToListAsync();
            Assert.Empty(subscriptionWallets);
        }

        [Fact]
        public async Task RebuildOperatorToBusinessLink_WhenCalledWithEmptyWallets_ShouldNotRebuildLinks()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
              .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            await context.Subscriptions.AddAsync(createSubscription);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            // Act: 
            await mediator.Send(new OperatorToBusinessLinkRebuildModel
            {
                SubscriptionId = createSubscription.Id,
                SubscriptionWallets = new List<SubscriptionWalletBuild>(),
                StateVersion = 5
            });

            // Assert:
            var subscriptionWallets = await context.SubscriptionWallets.ToListAsync();

            Assert.Empty(subscriptionWallets);
            Assert.Equal(0, createSubscription.StateVersion);
        }

        [Fact]
        public async Task RebuildOperatorToBusinessLink_WhenCalledWithSameWallets_ShouldNotDuplicateLinks()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var createWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            await context.Wallets.AddAsync(createWallet);
            await context.Subscriptions.AddAsync(createSubscription);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var expectedLinkDateTime1 = DateTime.UtcNow;
            var expectedLinkDateTime2 = DateTime.UtcNow.AddMinutes(1);

            // Act: 
            await mediator.Send(new OperatorToBusinessLinkRebuildModel
            {
                SubscriptionId = createSubscription.Id,
                SubscriptionWallets = new List<SubscriptionWalletBuild>
                {
                    new SubscriptionWalletBuild
                    {
                        WalletId = createWallet.Id,
                        LinkDateTime = expectedLinkDateTime1
                    }
                },
                StateVersion = 5
            });

            // Act 
            await mediator.Send(new OperatorToBusinessLinkRebuildModel
            {
                SubscriptionId = createSubscription.Id,
                SubscriptionWallets = new List<SubscriptionWalletBuild>
                {
                    new SubscriptionWalletBuild
                    {
                        WalletId = createWallet.Id,
                        LinkDateTime = expectedLinkDateTime2
                    }
                },
                StateVersion = 5
            });

            // Assert:
            var subscriptionWallets = await context.SubscriptionWallets
                .Where(sw => sw.SubscriptionId == createSubscription.Id)
                .Include(c => c.Subscription)
                .ToListAsync();

            Assert.Single(subscriptionWallets);
            Assert.Equal(expectedLinkDateTime2, subscriptionWallets[0].LinkDateTime.Value, precision: TimeSpan.FromMinutes(1));
            Assert.Equal(5, subscriptionWallets[0].Subscription.StateVersion);
        }

        [Fact]
        public async Task RebuildOperatorToBusinessLink_WhenCalledWithMultipleWallets_ShouldRebuildLinksCorrectly()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var createWallet1 = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var createWallet2 = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            await context.Wallets.AddRangeAsync(createWallet1, createWallet2);
            await context.Subscriptions.AddAsync(createSubscription);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var expectedLinkDateTime1 = DateTime.UtcNow;
            var expectedLinkDateTime2 = DateTime.UtcNow.AddMinutes(1);

            // Act: 
            await mediator.Send(new OperatorToBusinessLinkRebuildModel
            {
                SubscriptionId = createSubscription.Id,
                SubscriptionWallets = new List<SubscriptionWalletBuild>
                {
                    new SubscriptionWalletBuild { WalletId = createWallet1.Id, LinkDateTime = expectedLinkDateTime1 },
                    new SubscriptionWalletBuild { WalletId = createWallet2.Id, LinkDateTime = expectedLinkDateTime2 }
                },
                StateVersion = 5
            });

            // Assert:
            var subscriptionWallets = await context.SubscriptionWallets
                .Where(sw => sw.SubscriptionId == createSubscription.Id)
                .Include(c => c.Subscription)
                .ToListAsync();

            Assert.Equal(2, subscriptionWallets.Count);
            Assert.Contains(subscriptionWallets, sw =>
               sw.WalletId == createWallet1.Id &&
               Math.Abs((sw.LinkDateTime!.Value - expectedLinkDateTime1).TotalSeconds) < 60);
            Assert.Contains(subscriptionWallets, sw =>
                sw.WalletId == createWallet2.Id &&
                Math.Abs((sw.LinkDateTime!.Value - expectedLinkDateTime2).TotalSeconds) < 60);
            Assert.Equal(5, subscriptionWallets[0].Subscription.StateVersion);
        }

        [Fact]
        public async Task RebuildOperatorToBusinessLink_WhenCalledWithMultipleWalletsWithExistingSubscriptionWallets_ShouldUpdateExistingLinks()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var createWallet1 = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var createWallet2 = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var subsecriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(f => f.Subscription, createSubscription)
                .RuleFor(f => f.Wallet, createWallet1)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(f => f.Subscription, createSubscription)
                .RuleFor(f => f.Wallet, createWallet2)
                .Generate();

            await context.Wallets.AddRangeAsync(createWallet1, createWallet2);
            await context.Subscriptions.AddAsync(createSubscription);
            await context.SubscriptionWallets.AddRangeAsync(subsecriptionWallet, secondSubscriptionWallet);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            DateTime? expectedLinkDateTime1 = DateTime.UtcNow;
            DateTime? expectedLinkDateTime2 = DateTime.UtcNow.AddDays(1);

            // Act:
            await mediator.Send(new OperatorToBusinessLinkRebuildModel
            {
                SubscriptionId = createSubscription.Id,
                SubscriptionWallets = new List<SubscriptionWalletBuild>
                {
                    new SubscriptionWalletBuild { WalletId = createWallet1.Id, LinkDateTime = expectedLinkDateTime1.Value },
                    new SubscriptionWalletBuild { WalletId = createWallet2.Id, LinkDateTime = expectedLinkDateTime2.Value }
                },
                StateVersion = 5
            });

            // Assert
            var subscriptionWallets = await context.SubscriptionWallets
                .Where(sw => sw.SubscriptionId == createSubscription.Id)
                .Include(sw => sw.Subscription)
                .ToListAsync();

            var subscriptionWallet1 = await context.SubscriptionWallets
                .Where(sw => sw.SubscriptionId == createSubscription.Id
                && sw.WalletId == createWallet1.Id)
                .Include(sw => sw.Subscription)
                .FirstOrDefaultAsync();

            var subscriptionWallet2 = await context.SubscriptionWallets
                .Where(sw => sw.SubscriptionId == createSubscription.Id
                && sw.WalletId == createWallet2.Id)
                .Include(sw => sw.Subscription)
                .FirstOrDefaultAsync();

            Assert.Equal(2, subscriptionWallets.Count);

            Assert.NotNull(subscriptionWallet1);
            Assert.NotNull(subscriptionWallet2);

            Assert.Equal(subscriptionWallet1.LinkDateTime.Value, expectedLinkDateTime1.Value, precision: TimeSpan.FromMinutes(1));

            Assert.Equal(subscriptionWallet2.LinkDateTime.Value, expectedLinkDateTime2.Value, precision: TimeSpan.FromMinutes(1));

            Assert.Equal(5, subscriptionWallets[0].Subscription.StateVersion);
        }

        [Fact]
        public async Task RebuildOperatorToBusinessLink_WhenWalletNotExsit_ThrowNotFoundException()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var createWallet1 = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var createWallet2 = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            await context.Wallets.AddRangeAsync(createWallet1, createWallet2);
            await context.Subscriptions.AddAsync(createSubscription);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            DateTime? expectedLinkDateTime1 = DateTime.UtcNow;
            DateTime? expectedLinkDateTime2 = DateTime.UtcNow.AddDays(1);

            // Act:

            var subscriptionWalletBuild = new SubscriptionWalletBuild { WalletId = Guid.NewGuid().ToString(), LinkDateTime = DateTime.UtcNow };

            var request = new OperatorToBusinessLinkRebuildModel
            {
                SubscriptionId = createSubscription.Id,
                SubscriptionWallets = new List<SubscriptionWalletBuild>
                {
                    new SubscriptionWalletBuild { WalletId = createWallet1.Id, LinkDateTime = expectedLinkDateTime1.Value },
                    new SubscriptionWalletBuild { WalletId = createWallet2.Id, LinkDateTime = expectedLinkDateTime2.Value },
                    subscriptionWalletBuild
                },
                StateVersion = 5
            };

            var exception = await Assert.ThrowsAsync<AppException>(async () => await mediator.Send(request));

            Assert.NotEmpty(exception.Message);

            Assert.Equal(ExceptionStatusCode.NotFound, exception.StatusCode);

            Assert.Equal($"No wallet found for id: {subscriptionWalletBuild.WalletId}", exception.Message);

            // Assert
            var subscriptionWallets = await context.SubscriptionWallets
                .Where(sw => sw.SubscriptionId == createSubscription.Id)
                .Include(sw => sw.Subscription)
                .ToListAsync();

            var subscriptionWallet1 = await context.SubscriptionWallets
                .Where(sw => sw.SubscriptionId == createSubscription.Id
                && sw.WalletId == createWallet1.Id)
                .Include(sw => sw.Subscription)
                .FirstOrDefaultAsync();

            var subscriptionWallet2 = await context.SubscriptionWallets
                .Where(sw => sw.SubscriptionId == createSubscription.Id
                && sw.WalletId == createWallet2.Id)
                .Include(sw => sw.Subscription)
                .FirstOrDefaultAsync();

            Assert.Empty(subscriptionWallets);

            Assert.Null(subscriptionWallet1);
            Assert.Null(subscriptionWallet2);
        }
    }
}
