﻿using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.Enums;

namespace Anis.Cashflow.Report.Test.Fakers.Accounts
{
    public class SubscriptionFaker : PrivateFaker<Subscription>
    {
        public SubscriptionFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.Id, Guid.NewGuid().ToString());
            RuleFor(r => r.Type, SubscriptionType.Business);
            RuleFor(r => r.AccountId, Guid.NewGuid().ToString());
            RuleFor(r => r.Name, f => f.Random.AlphaNumeric(10));
        }
    }
}