﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.Const;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Live.Asserts;
using Anis.Cashflow.Report.Test.Live.Protos.WalltesDemoEvent;
using Google.Protobuf.WellKnownTypes;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.WalletEvents
{
    public class WalletCreatedTest : TestBase
    {
        public WalletCreatedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Create_WalletCreatedWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
             .Generate();

            var createAccount = new AccountFaker()
             .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
             .RuleFor(f => f.AccountId, createAccount.Id)
             .Generate();

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var channel = GrpcChannel.ForAddress(Address.EcomCards);

            var grpcClient = new WalltesDemoEvents.WalltesDemoEventsClient(channel);

            var request = new WalletRequest
            {
                AggregateId = Guid.NewGuid().ToString(),
                DateTime = DateTime.UtcNow.ToTimestamp(),
                RegionId = createRegion.Id,
                CurrencyType = CurrencyType.Lyd,
                HolderId = Guid.NewGuid().ToString(),
                SubscriptionId = createSubscription.Id,
                SubscriptionType = SubscriptionType.Business,
                Type = WalletType.Normal,
            };

            await grpcClient.CreateWalletAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<WalletEventsListener>().CloseProccessorAsync();

            var dbWallet = await context.Wallets.SingleOrDefaultAsync();

            Assert.NotNull(dbWallet);

            DemoEventAsserts.AssertEquality(request, dbWallet);
        }
    }
}
