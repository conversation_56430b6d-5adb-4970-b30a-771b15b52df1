﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.AccountHandlers
{
    public class ExpiryDateChangedTest : TestBase
    {
        public ExpiryDateChangedTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task Update_AccountExpirationChangedWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .RuleFor(f => f.ExpiryAt, f => null)
                .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.Accounts.AddAsync(createAccount);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<ExpiryDateChangedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.ExpiryDateChanged,
                Data = new(DateTime.UtcNow.AddDays(1)),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(messageBody, dbAccount, createAccount);
        }

        [Fact]
        public async Task Update_AccountExpirationChangedWithNonExistedAccount_EventNotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var messageBody = new MessageBody<ExpiryDateChangedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.ExpiryDateChanged,
                Data = new(DateTime.UtcNow.AddDays(1)),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(messageBody);

            Assert.False(isHandled);

            Assert.Empty(await context.Accounts.ToListAsync());
        }

        [Fact]
        public async Task Update_EventArrivedWithNotValidSequence_EventNotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .RuleFor(f => f.ExpiryAt, f => null)
                .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.Accounts.AddAsync(createAccount);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<ExpiryDateChangedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 4,
                Type = EventType.ExpiryDateChanged,
                Data = new(DateTime.UtcNow.AddDays(1)),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccount);
        }

        [Fact]
        public async Task Update_EventArrivedWithOldSequence_EventAlreadyHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .RuleFor(f => f.ExpiryAt, f => null)
                .RuleFor(f => f.Sequence, 4)
                .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<ExpiryDateChangedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.ExpiryDateChanged,
                Data = new(DateTime.UtcNow.AddDays(1)),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccount);
        }
    }
}