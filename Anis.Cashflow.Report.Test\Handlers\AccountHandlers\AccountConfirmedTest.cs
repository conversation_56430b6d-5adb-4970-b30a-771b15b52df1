﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Accounts.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.AccountHandlers
{
    public class AccountConfirmedTest : TestBase
    {
        public AccountConfirmedTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task Create_AccountConfirmedWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var region = new RegionFaker().Generate();

            await context.Regions.AddAsync(region);

            await context.SaveChangesAsync();

            var accountEvent = new AccountConfirmedDataFaker()
                .RuleFor(a => a.LocationId, region.Id)
                .Generate();

            var messageBody = new MessageBody<AccountConfirmedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 1,
                Type = EventType.AccountConfirmed,
                Data = accountEvent,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };
            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            var dbSubscription = await context.Subscriptions.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(messageBody, dbAccount: dbAccount);

            EntityAssert.AssertEquality(messageBody.Data.Subscriptions.Single(), dbSubscription, dbAccount.Id);
        }

        [Fact]
        public async Task Create_AccountConfirmedWithAccountAlredyExists_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var accountEvent = new AccountConfirmedDataFaker()
                     .Generate();

            var messageBody = new MessageBody<AccountConfirmedData>()
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.AccountConfirmed,
                Data = accountEvent,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(messageBody, dbAccount, createAccount);
        }

        [Fact]
        public async Task Create_AccountConfirmedWithNotExsitRegion_ReturnNotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createAccountfaker = new AccountConfirmedDataFaker()
                .RuleFor(f => f.LocationId, Guid.NewGuid().ToString())
                .Generate();

            var messageBody = new MessageBody<AccountConfirmedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.AccountConfirmed,
                Data = createAccountfaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleOrDefaultAsync();

            Assert.False(isHandled);

            Assert.Null(dbAccount);
        }

        [Fact]
        public async Task Create_AccountConfirmedWithOldSequence_ReturnAlreadyHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .RuleFor(f => f.Sequence, 7)
                .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<AccountConfirmedData>()
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.AccountConfirmed,
                Data = new AccountConfirmedDataFaker().Generate(),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccount);
        }

        [Fact]
        public async Task Create_AccountConfirmedWithFarNewerSequence_ReturnNotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<AccountConfirmedData>()
            {
                AggregateId = createAccount.Id,
                Sequence = 6,
                Type = EventType.AccountConfirmed,
                Data = new AccountConfirmedDataFaker().Generate(),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccount);
        }
    }
}
