﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.AccountHandlers
{
    public class SubscriptionRemovedTest : TestBase
    {
        public SubscriptionRemovedTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task Remove_SubscriptionRemovedWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.Accounts.AddAsync(createAccount);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<SubscriptionRemovedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.SubscriptionRemoved,
                Data = new(createSubscription.Id),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccounts = await context.Accounts.SingleAsync();

            var dbSubscription = await context.Subscriptions.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccounts, messageBody.Sequence);

            EntityAssert.AssertEquality(createSubscription, dbSubscription, createAccount.Id);
        }

        [Fact]
        public async Task Remove_SubscriptionRemovedWithNonExistedAccount_NotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var messageBody = new MessageBody<SubscriptionRemovedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.SubscriptionRemoved,
                Data = new(Guid.NewGuid().ToString()),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            Assert.False(isHandled);

            var account = await context.Accounts.SingleOrDefaultAsync();

            var subscription = await context.Subscriptions.SingleOrDefaultAsync();

            Assert.Null(account);

            Assert.Null(subscription);
        }

        [Fact]
        public async Task Update_EventArrivedWithNotValidSequence_NotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.Accounts.AddAsync(createAccount);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<SubscriptionRemovedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 4,
                Type = EventType.SubscriptionRemoved,
                Data = new(Guid.NewGuid().ToString()),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccount, createAccount.Sequence);
        }

        [Fact]
        public async Task Remove_EventArrivedWithOldSequence_EventHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .RuleFor(f => f.Sequence, 4)
                .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<SubscriptionRemovedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.SubscriptionRemoved,
                Data = new(Guid.NewGuid().ToString()),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccount, createAccount.Sequence);
        }
    }
}