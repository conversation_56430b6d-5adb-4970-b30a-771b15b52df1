﻿using Anis.Cashflow.Report.Domain.Models;
using Newtonsoft.Json;

namespace Anis.Cashflow.Report.Application.Extensions;

public static class MessageBodyExtensions
{
    public static MessageBody<T> ToMessageBody<T>(this MessageBody<string> eventMessage)
        => new()
        {
            AggregateId = eventMessage.AggregateId,
            Sequence = eventMessage.Sequence,
            Type = eventMessage.Type,
            Version = eventMessage.Version,
            Data = JsonConvert.DeserializeObject<T>(eventMessage.Data)!,
            DateTime = eventMessage.DateTime,
        };
}