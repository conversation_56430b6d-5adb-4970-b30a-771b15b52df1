﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Holders.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Holders;
using Anis.Cashflow.Report.Test.Fakers.Holders.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.HoldersHandlers
{
    public class HolderCreatedTest : TestBase
    {
        public HolderCreatedTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task HolderCreated_WhenReceivedValidData_SuccessHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderCreatedDataFaker = new HolderCreatedDataFaker().Generate();

            var message = new MessageBody<HolderCreatedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 1,
                Type = EventType.HolderCreated,
                Data = holderCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(message);

            var dbHolders = await context.Holders.ToListAsync();

            //assert
            Assert.True(isHandled);

            HolderAssert.AssertEquality(message, Assert.Single(dbHolders));
        }

        [Fact]
        public async Task HolderCreated_WhenHolderAlreadyExist_ReturnTurnAndIgnoreHandlerMessage()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderFaker = new HolderFaker().Generate();

            await context.Holders.AddAsync(holderFaker);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var holderCreatedDataFaker = new HolderCreatedDataFaker().Generate();

            var message = new MessageBody<HolderCreatedData>
            {
                AggregateId = holderFaker.Id,
                Sequence = 1,
                Type = EventType.HolderCreated,
                Data = holderCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(message);

            var dbHolders = await context.Holders.ToListAsync();

            //assert
            Assert.True(isHandled);

            HolderAssert.AssertEquality(holderFaker, Assert.Single(dbHolders));
        }
    }
}
