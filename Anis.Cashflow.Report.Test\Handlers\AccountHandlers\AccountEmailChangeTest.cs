﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Accounts.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.AccountHandlers
{
    public class AccountEmailChangeTest : TestBase
    {
        public AccountEmailChangeTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task ChangePhoneAccount_ChangePhoneValidAccoun_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var region = new RegionFaker().Generate();

            var account = new AccountFaker()
                        .RuleFor(r => r.LocationId, region.Id)
                        .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddAsync(account);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var newEmail = new AccountEmailChangedDataFaker()
                .RuleFor(a => a.Email, "<EMAIL>")
                .Generate();

            var messageBody = new MessageBody<AccountEmailChangedData>()
            {
                AggregateId = account.Id,
                Sequence = 2,
                Type = EventType.AccountEmailChanged,
                Data = newEmail,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };
            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(messageBody, dbAccount: dbAccount);
        }

        [Fact]
        public async Task ChangePhoneAccount_ChangePhoneNotValidAccoun_NotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var region = new RegionFaker().Generate();

            var account = new AccountFaker()
                        .RuleFor(r => r.LocationId, region.Id)
                        .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddAsync(account);

            await context.SaveChangesAsync();

            var newEmail = new AccountEmailChangedDataFaker()
                  .RuleFor(a => a.Email, "<EMAIL>")
                  .Generate();

            var messageBody = new MessageBody<AccountEmailChangedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.AccountEmailChanged,
                Data = newEmail,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };
            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount: account, dbAccount: dbAccount);
        }

        [Fact]
        public async Task ChangePhoneAccount_ChangePhonValidAccounAndInvalidGapSequence_NotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var region = new RegionFaker().Generate();

            var account = new AccountFaker()
                        .RuleFor(r => r.LocationId, region.Id)
                        .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddAsync(account);

            await context.SaveChangesAsync();

            var newEmail = new AccountEmailChangedDataFaker()
               .RuleFor(a => a.Email, "<EMAIL>")
               .Generate();

            var messageBody = new MessageBody<AccountEmailChangedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 5,
                Type = EventType.AccountEmailChanged,
                Data = newEmail,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };
            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount: account, dbAccount: dbAccount);
        }

        [Fact]
        public async Task ChangePhoneAccount_ChangePhonValidAccounAndInvalidOldSequence_NotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var region = new RegionFaker().Generate();

            var account = new AccountFaker()
                        .RuleFor(r => r.LocationId, region.Id)
                        .RuleFor(r => r.Sequence, 4)
                        .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddAsync(account);

            await context.SaveChangesAsync();

            var newEmail = new AccountEmailChangedDataFaker()
               .RuleFor(a => a.Email, "<EMAIL>")
               .Generate();

            var messageBody = new MessageBody<AccountEmailChangedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.AccountEmailChanged,
                Data = newEmail,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };
            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount: account, dbAccount: dbAccount);
        }
    }
}
