﻿using Anis.Cashflow.Report.Application.Contracts.Services.GrpcService;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Helpers;
using Anis.Cashflow.Report.Infra.Services.GrpcServices.Protos.DailyLocationSales;
using Anis.Cashflow.Report.Infra.Services.GrpcServices.Protos.DailyWalletSales;
using Microsoft.Extensions.Logging;

namespace Anis.Cashflow.Report.Infra.Services.GrpcServices
{
    public class GrpcClientService : IGrpcClientService
    {
        private readonly ILogger<GrpcClientService> _logger;

        private readonly DailyLocationSale.DailyLocationSaleClient _dailyLocationClient;

        private readonly DailyWalletSale.DailyWalletSaleClient _dailyWalletClient;

        public GrpcClientService(
            ILogger<GrpcClientService> logger,
            DailyLocationSale.DailyLocationSaleClient dailyLocationClient,
            DailyWalletSale.DailyWalletSaleClient dailyWalletClient)
        {
            _logger = logger;
            _dailyLocationClient = dailyLocationClient;
            _dailyWalletClient = dailyWalletClient;
        }

        public async Task<decimal> GetAverageSaleLocationAsync(string locationId, int daysCount)
        {
            try
            {
                var response = await _dailyLocationClient.GetSaleAverageAsync(new GetSaleAverageRequest
                {
                    DaysCount = daysCount,
                    LocationId = locationId
                });

                return response.AverageSale.Truncate();

            }
            catch (Exception ex)
            {
                _logger.LogWarning("failed call get average sale location grpc service : {Message}", ex.Message);

                return 0;
            }
        }

        public async Task<List<WalletAverageSaleModel>> GetAverageSaleWalletAsync(List<string> walletId, int daysCount)
        {
            try
            {
                RequestAverageWalletSale request = new()
                {
                    Days = daysCount,
                };

                request.WalletId.Add(walletId);

                var response = await _dailyWalletClient.GetAllWalletAverageSalesAsync(request);

                List<WalletAverageSaleModel> output = response.AverageWalletSales.Select(a =>

                {
                    return new WalletAverageSaleModel
                    {
                        WalletId = a.WalletId,
                        AverageSales = a.Average.Truncate(),
                        TotalSalesLastDay = (decimal)a.LastDaySales
                    };
                }).ToList();

                return output;

            }
            catch (Exception ex)
            {
                _logger.LogWarning("failed call get average sale wallet grpc service : {Message}", ex.Message);

                return new();
            }
        }
    }
}
