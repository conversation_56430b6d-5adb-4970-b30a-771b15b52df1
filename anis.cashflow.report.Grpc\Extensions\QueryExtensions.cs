using Anis.Cashflow.Report.Application.Features.Management.Filter;
using Anis.Cashflow.Report.Grpc.Protos.Managmant;

namespace Anis.Cashflow.Report.Grpc.Extensions
{
    public static class QueryExtensions
    {
        public static FilterQuery ToQuery(this Request request)
            => new
            (
                LocationId: request.LocationId,
                Type: EnumExtensions.ToRpcSubscriptionType(request.Type),
                Currency: EnumExtensions.ToRpcCurrencyType(request.Currency),
                PhoneNumber: request.PhoneNumber,
                DebtOnly: request.DebtOnly,
                WalletType: request.WalletTypeFilter.ToApplicationWalletTypeFilter(),
                CurrentPage: request.CurrentPage != null ? request.CurrentPage.Value : 1,
                PageSize: request.PageSize != null ? request.PageSize.Value : 25,
                CashAndHoldersFilter: (Application.Enums.CashAndHoldersFilter)request.CashAndHoldersFilter,
                HolderId: request.HolderId,
                OperatorId: request.OperatorId,
                ShowStoresOnly: request.ShowStoresOnly
            );

        public static Application.Features.Management.CashflowFilter.CashFlowFilterQuery ToQuery(this CashflowFilterRequest request)
            => new
            (
                LocationId: request.LocationId,
                Type: EnumExtensions.ToRpcSubscriptionType(request.Type),
                Currency: EnumExtensions.ToRpcCurrencyType(request.Currency),
                PhoneNumber: request.PhoneNumber,
                DebtOnly: request.DebtOnly,
                WalletType: request.WalletTypeFilter.ToApplicationWalletTypeFilter(),
                CurrentPage: request.CurrentPage != null ? request.CurrentPage.Value : 1,
                PageSize: request.PageSize != null ? request.PageSize.Value : 25,
                CashAndHoldersFilter: (Application.Enums.CashAndHoldersFilter)request.CashAndHoldersFilter,
                HolderId: request.HolderId,
                ExpirationOnly: request.ExpirationOnly
            );

        public static Application.Features.Consumer.Filter.FilterQuery ToQuery(this Protos.Consumer.Request request)
            => new
            (
                LocationId: request.LocationId,
                Type: EnumExtensions.ToRpcSubscriptionType(request.Type),
                DebtType: EnumExtensions.ToRpcDebtType(request.DebtType),
                PhoneNumber: request.PhoneNumber,
                WalletType: request.WalletTypeFilter.ToApplicationWalletTypeFilter(),
                IsAscending: request.IsAscending,
                CurrentPage: request.CurrentPage != null ? request.CurrentPage.Value : 1,
                PageSize: request.PageSize != null ? request.PageSize.Value : 25,
                OperatorId: request.OperatorId,
                ExpirationOnly: request.ExpirationOnly
            );
    }
}
