﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.Const;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Live.Asserts;
using Anis.Cashflow.Report.Test.Live.Protos.WalltesDemoEvent;
using Google.Protobuf.WellKnownTypes;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.WalletEvents
{
    public class TransactionCreatedTest : TestBase
    {
        public TransactionCreatedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Update_UrgentTopUpTransactionByCreditWithValidData__SuccessHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var channel = GrpcChannel.ForAddress(Address.Base);

            var grpcClient = new WalltesDemoEvents.WalltesDemoEventsClient(channel);

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 1000)
                .RuleFor(f => f.Sequence, 2)
                 .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var request = new TransactionRequest
            {
                AggregateId = creatWallet.Id,
                DateTime = DateTime.UtcNow.ToTimestamp(),
                EventId = Guid.NewGuid().ToString(),
                ReferenceId = creatWallet.Id,
                Debit = 0,
                Credit = 1000,
                Value = -1000,
                Type = TransactionType.UrgentTopUp,
                Sequence = 3,
            };

            await grpcClient.CreateTransactionAsync(request);

            await Task.Delay(4000);

            await Factory.Services.GetRequiredService<WalletEventsListener>().CloseProccessorAsync();

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 

            DemoEventAsserts.AssertUrgentOrDelayedEquality(dbWallet, request, creatWallet, isUrgent: true);

        }

        [Fact]
        public async Task Update_DelayedTopUpTransactionByCreditWithValidData__SuccessHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var channel = GrpcChannel.ForAddress(Address.Base);

            var grpcClient = new WalltesDemoEvents.WalltesDemoEventsClient(channel);

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 1000)
                .RuleFor(f => f.Sequence, 2)
                 .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var request = new TransactionRequest
            {
                AggregateId = creatWallet.Id,
                DateTime = DateTime.UtcNow.ToTimestamp(),
                EventId = Guid.NewGuid().ToString(),
                ReferenceId = creatWallet.Id,
                Debit = 0,
                Credit = 1000,
                Value = -1000,
                Type = TransactionType.DelayedTopUp,
                Sequence = 3,
            };

            await grpcClient.CreateTransactionAsync(request);

            await Task.Delay(4000);

            await Factory.Services.GetRequiredService<WalletEventsListener>().CloseProccessorAsync();

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 

            DemoEventAsserts.AssertUrgentOrDelayedEquality(dbWallet, request, creatWallet, isUrgent: false);

        }

        [Fact]
        public async Task Update_RefundUrgentTopUpTransactionByCreditWithValidData__SuccessHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var channel = GrpcChannel.ForAddress(Address.Base);

            var grpcClient = new WalltesDemoEvents.WalltesDemoEventsClient(channel);

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 1000)
                .RuleFor(f => f.Sequence, 2)
                 .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var request = new TransactionRequest
            {
                AggregateId = creatWallet.Id,
                DateTime = DateTime.UtcNow.ToTimestamp(),
                EventId = Guid.NewGuid().ToString(),
                ReferenceId = creatWallet.Id,
                Debit = 0,
                Credit = 1000,
                Value = -1000,
                Type = TransactionType.RefundUrgentTopUp,
                Sequence = 3,
            };

            await grpcClient.CreateTransactionAsync(request);

            await Task.Delay(4000);

            await Factory.Services.GetRequiredService<WalletEventsListener>().CloseProccessorAsync();

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 

            DemoEventAsserts.AssertRefundUrgentOrDelayedEquality(dbWallet, request, creatWallet, isUrgent: true);

        }

        [Fact]
        public async Task Update_RefundDelayedTopUpTransactionByCreditWithValidData__SuccessHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var channel = GrpcChannel.ForAddress(Address.Base);

            var grpcClient = new WalltesDemoEvents.WalltesDemoEventsClient(channel);

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 1000)
                .RuleFor(f => f.Sequence, 2)
                 .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var request = new TransactionRequest
            {
                AggregateId = creatWallet.Id,
                DateTime = DateTime.UtcNow.ToTimestamp(),
                EventId = Guid.NewGuid().ToString(),
                ReferenceId = creatWallet.Id,
                Debit = 0,
                Credit = 1000,
                Value = -1000,
                Type = TransactionType.RefundDelayedTopUp,
                Sequence = 3,
            };

            await grpcClient.CreateTransactionAsync(request);

            await Task.Delay(4000);

            await Factory.Services.GetRequiredService<WalletEventsListener>().CloseProccessorAsync();

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 

            DemoEventAsserts.AssertRefundUrgentOrDelayedEquality(dbWallet, request, creatWallet, isUrgent: false);

        }

        [Fact]
        public async Task Update_OtherCreateTransactionByCreditWithValidData__SuccessHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var channel = GrpcChannel.ForAddress(Address.Base);

            var grpcClient = new WalltesDemoEvents.WalltesDemoEventsClient(channel);

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 1000)
                .RuleFor(f => f.Sequence, 2)
                 .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var request = new TransactionRequest
            {
                AggregateId = creatWallet.Id,
                DateTime = DateTime.UtcNow.ToTimestamp(),
                EventId = Guid.NewGuid().ToString(),
                ReferenceId = creatWallet.Id,
                Debit = 0,
                Credit = 1000,
                Value = -1000,
                Type = TransactionType.Refund,
                Sequence = 3,
            };

            await grpcClient.CreateTransactionAsync(request);

            await Task.Delay(4000);

            await Factory.Services.GetRequiredService<WalletEventsListener>().CloseProccessorAsync();

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 
            DemoEventAsserts.AssertEquality(dbWallet, request, creatWallet);
        }


    }
}
