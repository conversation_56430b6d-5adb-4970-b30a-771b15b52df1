﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Live.Asserts;
using Anis.Cashflow.Report.Test.Live.Protos;
using Google.Protobuf.WellKnownTypes;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.AccountEvents
{
    public class AccountConfirmedTest : TestBase
    {
        public AccountConfirmedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Create_AccountConfirmedWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var channel = GrpcChannel.ForAddress(Address.EcomCards);

            var grpcClient = new AccountsDemoEvents.AccountsDemoEventsClient(channel);

            var request = new ConfirmeAccountRequest
            {
                AggregateId = Guid.NewGuid().ToString(),
                DateTime = DateTime.UtcNow.ToTimestamp(),
                Sequence = 1,
                LocationId = createRegion.Id,
                OwnerName = "OwnerName",
                Phone = "*********",
                Number = "Number",
            };

            await grpcClient.ConfirmeAccountAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<AccountEventsListener>().CloseProccessorAsync();

            var account = await context.Accounts.SingleOrDefaultAsync();

            DemoEventAsserts.AssertEquality(request, account);
        }
    }
}
