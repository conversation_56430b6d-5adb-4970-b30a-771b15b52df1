﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.EventsData.SubscriptionWallets;
using Anis.Cashflow.Report.Domain.Models.Configs;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Options;
using Azure.Messaging.ServiceBus;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Serilog.Context;
using Serilog.Core.Enrichers;
using System.Text;

namespace Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners
{
    public class SubscriptionWalletListener : IHostedService
    {
        private readonly IServiceProvider _provider;
        private readonly ILogger<SubscriptionWalletListener> _logger;
        private readonly ServiceBusSessionProcessor? _processor;
        private readonly ServiceBusProcessor? _deadLetterProcessor;
        private readonly bool _isDeadLetterEnabled;

        public SubscriptionWalletListener(
            IServiceProvider provider,
            ILogger<SubscriptionWalletListener> logger,
            SubscriptionWalletServiceBusConnection client,
            IOptions<ServiceBusOptions> serviceBusOptions
        )
        {
            _provider = provider;
            _logger = logger;
            _isDeadLetterEnabled = serviceBusOptions.Value.IsDeadLetterEnabled;

            if (!string.IsNullOrWhiteSpace(serviceBusOptions.Value.OperatorBusinessLinkTopic))
            {
                _processor = client.Client.CreateSessionProcessor(serviceBusOptions.Value.OperatorBusinessLinkTopic,
                    serviceBusOptions.Value.OperatorBusinessLinkSubscription, new ServiceBusSessionProcessorOptions()
                    {
                        PrefetchCount = 1,
                        MaxConcurrentCallsPerSession = 1,
                        MaxConcurrentSessions = 1000,
                        AutoCompleteMessages = false,
                    });

                _processor.ProcessMessageAsync += Processor_ProcessMessageAsync;
                _processor.ProcessErrorAsync += Processor_ProcessErrorAsync;

                if (_isDeadLetterEnabled)
                {
                    _deadLetterProcessor = client.Client.CreateProcessor(serviceBusOptions.Value.OperatorBusinessLinkTopic,
                    serviceBusOptions.Value.OperatorBusinessLinkSubscription, new ServiceBusProcessorOptions()
                    {
                        PrefetchCount = 1,
                        AutoCompleteMessages = false,
                        MaxConcurrentCalls = 1000,
                        SubQueue = SubQueue.DeadLetter,
                    });

                    _deadLetterProcessor.ProcessMessageAsync += DeadLetterProcessor_ProcessMessageAsync;
                    _deadLetterProcessor.ProcessErrorAsync += DeadLetterProcessor_ProcessErrorAsync;
                }
            }
        }

        private async Task Processor_ProcessMessageAsync(ProcessSessionMessageEventArgs arg)
        {
            Task<bool> isHandledTask = HandelSubject(arg.Message.Subject, arg.Message);

            var isHandled = await isHandledTask;

            if (isHandled)
            {
                await arg.CompleteMessageAsync(arg.Message);
            }
            else
            {
                await arg.AbandonMessageAsync(arg.Message);
            }
        }

        private async Task DeadLetterProcessor_ProcessMessageAsync(ProcessMessageEventArgs arg)
        {
            var isHandledTask = HandelSubject(arg.Message.Subject, arg.Message);

            var isHandled = await isHandledTask;

            if (isHandled)
            {
                await arg.CompleteMessageAsync(arg.Message);
            }
            else
            {
                await Task.Delay(5000);
                await arg.AbandonMessageAsync(arg.Message);
            }
        }

        private Task Processor_ProcessErrorAsync(ProcessErrorEventArgs arg)
        {
            _logger.LogCritical(arg.Exception, "SubscriptionWalletListener => _processor => Processor_ProcessErrorAsync Message handler encountered an exception," +
                " Error Source:{ErrorSource}," +
                " Entity Path:{EntityPath}",
                arg.ErrorSource.ToString(),
                arg.EntityPath
            );

            return Task.CompletedTask;
        }

        private Task DeadLetterProcessor_ProcessErrorAsync(ProcessErrorEventArgs arg)
        {
            _logger.LogCritical(arg.Exception, "SubscriptionWalletEventsListener DeadLetter Message handler encountered an exception," +
                " Error Source:{ErrorSource}," +
                " Entity Path:{EntityPath}",
                arg.ErrorSource.ToString(),
                arg.EntityPath
            );

            return Task.CompletedTask;
        }

        private Task<bool> HandelSubject(string subject, ServiceBusReceivedMessage message)
        {
            message.ApplicationProperties.TryGetValue("DataVersion", out var version);
            _logger.LogInformation("Event Type: {EventType}, Message: {Message}", subject, message.Body);

            return (subject, version) switch
            {
                (EventType.BusinessAdded, 1) => HandleAsync<BusinessAddedData>(message),
                (EventType.BusinessRemoved, 1) => HandleAsync<BusinessRemovedData>(message),

                _ => Task.FromResult(false),
            };
        }

        private async Task<bool> HandleAsync<T>(ServiceBusReceivedMessage message)
        {
            var eventType = new PropertyEnricher(name: "EventType", message.Subject);
            var sessionId = new PropertyEnricher(name: "SessionId", message.SessionId);
            var messageId = new PropertyEnricher(name: "MessageId", message.MessageId);

            using (LogContext.Push(eventType, sessionId, messageId))
            {
                _logger.LogInformation("Event handling started.");

                var json = Encoding.UTF8.GetString(message.Body);

                var body = JsonConvert.DeserializeObject<NotificationBody<T>>(json) ?? throw new NullReferenceException("Failed deserialize json object");

                using var scope = _provider.CreateScope();

                var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

                var isHandled = await mediator.Send(body);

                _logger.LogInformation("Event handling completed, Result: {Result} Event Body: {Body}  ", isHandled, body);

                return isHandled;
            }
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _processor?.StartProcessingAsync(cancellationToken);

            if (_isDeadLetterEnabled)
                _deadLetterProcessor?.StartProcessingAsync(cancellationToken);

            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _processor?.CloseAsync(cancellationToken);

            if (_isDeadLetterEnabled)
                _deadLetterProcessor?.CloseAsync(cancellationToken);

            return Task.CompletedTask;
        }

        public Task CloseProccessorAsync()
        {
            _processor?.CloseAsync();

            _deadLetterProcessor?.CloseAsync();

            return Task.CompletedTask;
        }
    }
}
