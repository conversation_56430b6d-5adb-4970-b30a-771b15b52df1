﻿using Anis.Cashflow.Report.Application.Features.Management.GetHolders;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Grpc.Protos.Managmant;
using Anis.Cashflow.Report.Infra.Helpers;
using Google.Protobuf.Collections;
using Google.Protobuf.WellKnownTypes;

namespace Anis.Cashflow.Report.Grpc.Extensions
{
    public static class ModelExtensions
    {
        public static WalletDto ToOutput(this Application.Features.Management.Filter.WalletDto Dto, WalletAverageSaleModel model)
        {
            return new WalletDto
            {
                Id = Dto.Id,
                Owner = Dto.Owner,
                Credit = (double)Dto.Credit,
                Debit = (double)Dto.Debit,
                NetBalance = (double)Dto.NetBalance,
                Delay = (double)Dto.Delay,
                Urgent = (double)Dto.Urgent,
                Email = Dto.Email,
                Location = Dto.Location,
                PhoneNumber = Dto.PhoneNumber,
                SubscriptionName = Dto.SubscriptionName,
                State = Dto.State,
                WalletIdentifier = Dto.WalletIdentifier,
                IsNormal = Dto.IsNormal,
                WalletRegion = Dto.WalletRegion,
                AverageSales = (double)(model == null ? 0 : model.AverageSales),
                PositveBalanceRate = GetAverageSalesValue(model, Dto.NetBalance),
                Code = Dto.Code,
                HolderName = Dto.HolderName,
                ConfirmedAt = Dto.ConfirmedAt.HasValue ? DateTime.SpecifyKind(Dto.ConfirmedAt.Value, DateTimeKind.Utc).ToTimestamp() : null,
                LastDelayRefundDate = Dto.LastDelayRefundDate.HasValue ? DateTime.SpecifyKind(Dto.LastDelayRefundDate.Value, DateTimeKind.Utc).ToTimestamp() : null,
                ArabicLocationName = Dto.ArabicLocationName,
                EnglishLocationName = Dto.EnglishLocationName,
                TotalSalesLastDay = (double)(model == null ? 0 : model.TotalSalesLastDay),
                OperatorName = Dto.OperatorName,
            };
        }

        public static void ToOutputs(this RepeatedField<WalletDto> Repeated, IEnumerable<Application.Features.Management.Filter.WalletDto> Dtos, List<WalletAverageSaleModel> walletSaleAverage)
        {
            Repeated.AddRange(Dtos.Select(w => w.ToOutput(walletSaleAverage.Where(s => s.WalletId == w.Id).FirstOrDefault())));
        }

        public static CashflowFilterWalletDto ToOutput(this Application.Features.Management.CashflowFilter.WalletDto Dto, WalletAverageSaleModel model)
        {
            return new CashflowFilterWalletDto
            {
                Id = Dto.Id,
                Owner = Dto.Owner,
                Credit = (double)Dto.Credit,
                Debit = (double)Dto.Debit,
                NetBalance = (double)Dto.NetBalance,
                Delay = (double)Dto.Delay,
                Urgent = (double)Dto.Urgent,
                Email = Dto.Email,
                Location = Dto.Location,
                PhoneNumber = Dto.PhoneNumber,
                SubscriptionName = Dto.SubscriptionName,
                State = Dto.State,
                WalletIdentifier = Dto.WalletIdentifier,
                IsNormal = Dto.IsNormal,
                WalletRegion = Dto.WalletRegion,
                AverageSales = (double)(model == null ? 0 : model.AverageSales),
                PositveBalanceRate = GetAverageSalesValue(model, Dto.NetBalance),
                Code = Dto.Code,
                HolderName = Dto.HolderName,
                ConfirmedAt = Dto.ConfirmedAt.HasValue ? DateTime.SpecifyKind(Dto.ConfirmedAt.Value, DateTimeKind.Utc).ToTimestamp() : null,
                LastDelayRefundDate = Dto.LastDelayRefundDate.HasValue ? DateTime.SpecifyKind(Dto.LastDelayRefundDate.Value, DateTimeKind.Utc).ToTimestamp() : null,
                ArabicLocationName = Dto.ArabicLocationName,
                EnglishLocationName = Dto.EnglishLocationName,
                TotalSalesLastDay = (double)(model == null ? 0 : model.TotalSalesLastDay),
                RemainingTime = Dto.RemainingTime,
            };
        }

        public static void ToOutputs(this RepeatedField<CashflowFilterWalletDto> Repeated, IEnumerable<Application.Features.Management.CashflowFilter.WalletDto> Dtos, List<WalletAverageSaleModel> walletSaleAverage)
        {
            Repeated.AddRange(Dtos.Select(w => w.ToOutput(walletSaleAverage.Where(s => s.WalletId == w.Id).FirstOrDefault())));
        }

        private static double GetAverageSalesValue(WalletAverageSaleModel model, decimal netBalance)
        {
            return (double)(model != null ? (model.AverageSales == 0 ? 0 : (netBalance / model.AverageSales)).Truncate() : 0);
        }

        public static void ToOutputs(this RepeatedField<Protos.Consumer.WalletDto> Repeated, IEnumerable<Application.Features.Consumer.Filter.WalletDto> Dtos, List<WalletAverageSaleModel> walletSaleAverage)
        {
            Repeated.AddRange(Dtos.Select(w => w.ToOutput(walletSaleAverage.Where(s => s.WalletId == w.Id).FirstOrDefault())));
        }

        public static Protos.Consumer.WalletDto ToOutput(this Application.Features.Consumer.Filter.WalletDto Dto, WalletAverageSaleModel model)
        {
            return new Protos.Consumer.WalletDto
            {
                Id = Dto.Id,
                SubscriptionName = Dto.SubscriptionName,
                PhoneNumber = Dto.PhoneNumber,
                AccountLocation = Dto.AccountLocation,
                Balance = (double)Dto.Balance,
                Delay = (double)Dto.Delay,
                Urgent = (double)Dto.Urgent,
                WalletRegion = Dto.WalletRegion,
                AverageSales = (double)(model == null ? 0 : model.AverageSales),
                PositiveBalanceRate = GetAverageSalesValue(model, Dto.NetBalance),
                ConfirmedAt = Dto.ConfirmedAt.HasValue ? DateTime.SpecifyKind(Dto.ConfirmedAt.Value, DateTimeKind.Utc).ToTimestamp() : null,
                LastDelayRefundDate = Dto.LastDelayRefundDate.HasValue ? DateTime.SpecifyKind(Dto.LastDelayRefundDate.Value, DateTimeKind.Utc).ToTimestamp() : null,
                TotalSalesLastDay = (double)(model == null ? 0 : model.TotalSalesLastDay),
                OperatorName = Dto.OperatorName,
                RemainingTime = Dto.RemainingTime
            };
        }

        public static GetHoldersResponse ToOutput(this List<GetHoldersDTO> dto)
        => new()
        {
            Holders =
            {
                dto.Select(h => new HolderOption
                {
                  Id = h.Id,
                  Name = h.Name
                }).ToList()
            }
        };
    }
}
