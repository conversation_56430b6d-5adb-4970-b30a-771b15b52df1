﻿using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;

namespace Anis.Cashflow.Report.Test.Fakers.Accounts.EventsData
{
    public sealed class SubscriptionCreatedDataFaker : PrivateFaker<SubscriptionCreatedData>
    {
        public SubscriptionCreatedDataFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.Id, Guid.NewGuid().ToString());
            RuleFor(r => r.Type, SubscriptionType.Business);
            RuleFor(r => r.Name, f => f.Random.AlphaNumeric(10));
        }
    }
}