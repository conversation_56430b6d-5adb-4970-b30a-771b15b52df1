﻿namespace Anis.Cashflow.Report.Domain.Models.Configs
{
    public class ServiceBusOptions
    {
        public const string Context = "ServiceBus";
        public string AccountTopic { get; set; }
        public string AccountSubscription { get; set; }
        public string WalletTopic { get; set; }
        public string WalletSubscription { get; set; }
        public string RegionTopic { get; set; }
        public string RegionSubscription { get; set; }
        public string HolderTopic { get; set; }
        public string HolderSubscription { get; set; }
        public string OperatorBusinessLinkTopic { get; set; }
        public string OperatorBusinessLinkSubscription { get; set; }
        public bool IsDeadLetterEnabled { get; set; }
    }
}
