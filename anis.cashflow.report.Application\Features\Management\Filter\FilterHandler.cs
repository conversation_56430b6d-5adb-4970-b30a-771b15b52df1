﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.Features.Management.Filter
{
    public class FilterHandler : IRequestHandler<FilterQuery, FilterResponse>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly BlackListSubscriptionsModel _blackListSubscriptionsModel;

        public FilterHandler(IUnitOfWork unitOfWork, BlackListSubscriptionsModel blackListSubscriptionsModel)
        {
            _unitOfWork = unitOfWork;
            _blackListSubscriptionsModel = blackListSubscriptionsModel;
        }

        public async Task<FilterResponse> Handle(FilterQuery request, CancellationToken cancellationToken)
        {
            return await _unitOfWork.SubscriptionWallets.FilterAsync(request, _blackListSubscriptionsModel, cancellationToken);
        }
    }
}
