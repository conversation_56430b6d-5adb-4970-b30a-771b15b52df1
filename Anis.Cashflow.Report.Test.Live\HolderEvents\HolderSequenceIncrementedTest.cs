﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Fakers.Holders;
using Anis.Cashflow.Report.Test.Live.Asserts;
using Anis.Cashflow.Report.Test.Live.Protos.Holder;
using Google.Protobuf.WellKnownTypes;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.HolderEvents
{
    public class HolderSequenceIncrementedTest : TestBase
    {
        public HolderSequenceIncrementedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task HolderSequenceIncremented_WhenReceivedValidData_ReturnTrueAndHandleMessage()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var channel = GrpcChannel.ForAddress(Address.Base);

            var grpcClient = new DemoEvents.DemoEventsClient(channel);

            var holderFaker = new HolderFaker().Generate();

            await context.Holders.AddAsync(holderFaker);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var request = new DeleteRequest
            {
                Id = holderFaker.Id,
                UserId = Guid.NewGuid().ToString(),
                Sequence = 2,
                DateTime = DateTime.UtcNow.ToTimestamp()
            };

            await grpcClient.DeleteAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<HolderEventsListener>().CloseProccessorAsync();

            var dbHolder = await context.Holders.SingleOrDefaultAsync();

            DemoEventAsserts.AssertEquality(holderFaker, dbHolder);
        }
    }
}
