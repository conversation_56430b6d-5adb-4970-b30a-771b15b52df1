﻿using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.AccountHandlers
{
    public class AccountRefreshTest : TestBase
    {
        public AccountRefreshTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task Update_WhenAccountExist_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var firstCreateRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secCreateRegion = new RegionFaker()
                .RuleFor(r => r.Code, "02")
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, firstCreateRegion.Id)
                .Generate();

            await context.Regions.AddRangeAsync(firstCreateRegion, secCreateRegion);

            await context.Accounts.AddAsync(createAccount);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var model = new AccountRefreshModel
            {
                AccountId = createAccount.Id,
                ConfirmedAt = DateTime.UtcNow
            };

            var isHandled = await mediator.Send(model);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.Equal(dbAccount.ConfirmedAt, model.ConfirmedAt);
        }

        [Fact]
        public async Task Update_WhenAccountIsNotExist_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var firstCreateRegion = new RegionFaker()
                .RuleFor(r => r.Code, "01")
                .Generate();

            var secCreateRegion = new RegionFaker()
                .RuleFor(r => r.Code, "02")
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, firstCreateRegion.Id)
                .Generate();

            await context.Regions.AddRangeAsync(firstCreateRegion, secCreateRegion);

            await context.Accounts.AddAsync(createAccount);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var model = new AccountRefreshModel
            {
                AccountId = Guid.NewGuid().ToString(),
                ConfirmedAt = DateTime.UtcNow
            };

            var isHandled = await mediator.Send(model);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.Equal(createAccount.ConfirmedAt, dbAccount.ConfirmedAt);
        }
    }
}
