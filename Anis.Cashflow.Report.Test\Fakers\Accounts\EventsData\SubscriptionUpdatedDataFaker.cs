﻿using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;

namespace Anis.Cashflow.Report.Test.Fakers.Accounts.EventsData
{
    public sealed class SubscriptionUpdatedDataFaker : PrivateFaker<SubscriptionUpdatedData>
    {
        public SubscriptionUpdatedDataFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.Id, Guid.NewGuid().ToString());
            RuleFor(r => r.Name, f => f.Random.AlphaNumeric(10));
        }
    }
}