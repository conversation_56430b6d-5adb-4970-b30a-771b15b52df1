﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Anis.Cashflow.Report.Application.MessageHandlers.WalletHandlers
{
    public class OtherTransactionHandler : IRequestHandler<MessageBody<object>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OtherTransactionHandler> _logger;

        public OtherTransactionHandler(IUnitOfWork unitOfWork, ILogger<OtherTransactionHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<bool> Handle(MessageBody<object> message, CancellationToken cancellationToken)
        {
            var wallet = await _unitOfWork.Wallets.FindAsync(message.AggregateId);

            if (wallet is null) return false;

            if (wallet.Sequence != message.Sequence - 1)
                return wallet.Sequence >= message.Sequence;

            wallet.IncrementSequence();

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}
