﻿namespace Anis.Cashflow.Report.Application.Const
{
    public class TransactionType
    {
        public const string CardsPurchase = "CardsPurchase";
        public const string Refund = "Refund";
        public const string Transfer = "Transfer";
        public const string UndoRefund = "UndoRefund";
        public const string CardsReturn = "CardsReturn";
        public const string BlackFridayGift = "BlackFridayGift";
        public const string RefundUrgentTopUp = "RefundUrgentTopUp";
        public const string DelayedTopUp = "DelayedTopUp";
        public const string RefundDelayedTopUp = "RefundDelayedTopUp";
        public const string DebtRecovery = "DebtRecovery";
        public const string UrgentTopUp = "UrgentTopUp";
    }
}
