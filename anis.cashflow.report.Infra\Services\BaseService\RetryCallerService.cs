﻿using Anis.Cashflow.Report.Application.Contracts.Services.BaseService;
using Anis.Cashflow.Report.Domain.Exceptions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Anis.Cashflow.Report.Infra.Services.BaseService
{
    public class RetryCallerService : IRetryCallerService
    {
        private readonly ILogger<RetryCallerService> _logger;

        public RetryCallerService(ILogger<RetryCallerService> logger)
        {
            _logger = logger;
        }

        public async Task<T> CallAsync<T>(Func<Task<T>> operation, int retryCount = 5, int millisecondsDelay = 250)
        {
            var count = retryCount + 1;

            while (true)
            {
                count--;

                try
                {
                    return await operation();
                }
                catch (DbUpdateException e)
                {
                    _logger.LogWarning(e, "Call failed with {attempts} left", count);

                    if (count == 0)
                        throw new AppException(ExceptionStatusCode.AlreadyExists, e.Message);
                }

                await Task.Delay(millisecondsDelay);
            }
        }

    }
}
