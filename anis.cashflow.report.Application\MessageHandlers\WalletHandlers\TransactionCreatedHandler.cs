﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Anis.Cashflow.Report.Application.MessageHandlers.WalletHandlers
{
    public class TransactionCreatedHandler : IRequestHandler<MessageBody<TransactionCreatedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<TransactionCreatedHandler> _logger;

        public TransactionCreatedHandler(IUnitOfWork unitOfWork, ILogger<TransactionCreatedHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<bool> Handle(MessageBody<TransactionCreatedData> request, CancellationToken cancellationToken)
        {
            var wallet = await _unitOfWork.Wallets.FindAsync(request.AggregateId);

            if (wallet == null)
            {
                _logger.LogWarning("wallet not found with id {WalletId} and sequence {Sequence}", request.AggregateId, request.Sequence);

                return false;
            }

            var task = request.Data.Type switch
            {
                TransactionType.UrgentTopUp => HandleUrgentTopUpTransactionAsync(request, wallet),
                TransactionType.RefundUrgentTopUp => HandleRefundUrgentTopUpTransactionAsync(request, wallet),
                TransactionType.DelayedTopUp => HandleDelayedTopUpTransactionAsync(request, wallet),
                TransactionType.RefundDelayedTopUp => HandleRefundDelayedTopUpTransactionAsync(request, wallet),
                _ => HandleOtherTransactionAsync(request, wallet),
            };

            return await task;
        }

        private async Task<bool> HandleUrgentTopUpTransactionAsync(MessageBody<TransactionCreatedData> message, Wallet wallet)
        {
            if (wallet.Sequence != message.Sequence - 1)
                return wallet.Sequence >= message.Sequence;

            if (message.Data.Credit > 0)
                wallet.UpdateCreditBalance(message.Data.Credit);

            else if (message.Data.Debit > 0)
            {
                wallet.UpdateDebitCurrentUrgent(message.Data.Debit);
                wallet.UpdateDebitBalance(message.Data.Debit);
            }

            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        private async Task<bool> HandleRefundUrgentTopUpTransactionAsync(MessageBody<TransactionCreatedData> message, Wallet wallet)
        {
            if (wallet.Sequence != message.Sequence - 1)
                return wallet.Sequence >= message.Sequence;

            if (message.Data.Credit > 0)
            {
                wallet.UpdateCreditCurrentUrgent(message.Data.Credit);
                wallet.UpdateCreditBalance(message.Data.Credit);
            }
            else if (message.Data.Debit > 0)
            {
                wallet.UpdateDebitBalance(message.Data.Debit);
            }

            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        private async Task<bool> HandleDelayedTopUpTransactionAsync(MessageBody<TransactionCreatedData> message, Wallet wallet)
        {
            if (wallet.Sequence != message.Sequence - 1)
                return wallet.Sequence >= message.Sequence;

            if (message.Data.Debit > 0)
            {
                wallet.UpdateDebitCurrentDelayed(message.Data.Debit);
                wallet.UpdateDebitBalance(message.Data.Debit);
            }
            else if (message.Data.Credit > 0)
                wallet.UpdateCreditBalance(message.Data.Credit);

            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        private async Task<bool> HandleRefundDelayedTopUpTransactionAsync(MessageBody<TransactionCreatedData> message, Wallet wallet)
        {
            if (wallet.Sequence != message.Sequence - 1)
                return wallet.Sequence >= message.Sequence;

            if (message.Data.Debit > 0)
                wallet.UpdateDebitBalance(message.Data.Debit);

            else if (message.Data.Credit > 0)
            {
                wallet.UpdateCreditCurrentDelayed(message.Data.Credit, message.DateTime);
                wallet.UpdateCreditBalance(message.Data.Credit);
            }

            await _unitOfWork.SaveChangesAsync();

            return true;
        }

        private async Task<bool> HandleOtherTransactionAsync(MessageBody<TransactionCreatedData> message, Wallet wallet)
        {
            if (wallet.Sequence != message.Sequence - 1)
                return wallet.Sequence >= message.Sequence;

            if (message.Data.Debit > 0)
                wallet.UpdateDebitBalance(message.Data.Debit);

            if (message.Data.Credit > 0)
                wallet.UpdateCreditBalance(message.Data.Credit);

            await _unitOfWork.SaveChangesAsync();

            return true;
        }

    }
}
