syntax = "proto3";

package Cashflow.Report.V3;

import "google/protobuf/empty.proto";
import "google/protobuf/wrappers.proto";

option csharp_namespace = "Anis.Cashflow.Report.Grpc.Protos.Rebuild.v3";

service CashflowRebuildV3 {
  rpc RebuildAccounts (google.protobuf.Empty) returns (google.protobuf.Empty);
  rpc RebuildWallets (google.protobuf.Empty) returns (google.protobuf.Empty);
  rpc RebuildGateWayRegions (google.protobuf.Empty) returns (google.protobuf.Empty);
  rpc RebuildTransactions (RebuildTransactionsRequest) returns (google.protobuf.Empty);
  rpc RebuildSnapshotWallets (RebuildWalletsRequest) returns (google.protobuf.Empty);
  rpc RefresheAccounts (google.protobuf.Empty) returns (google.protobuf.Empty);
  rpc RebuildHolders (RebuildHoldersRequest) returns (google.protobuf.Empty);
  rpc RebuildOperatorToBusinessLink (RebuildHoldersRequest) returns (google.protobuf.Empty);
 } 
 
  message RebuildWalletsRequest{
	google.protobuf.Int32Value result_number = 1;

} message RebuildTransactionsRequest{
	int32 page_size = 1;
	int32 total_events = 2;
	int32 percentage = 3;
}

message RebuildHoldersRequest{
  int32 current_page = 1;
  int32 page_size = 2;
}