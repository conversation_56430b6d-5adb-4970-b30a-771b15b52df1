﻿using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Grpc.Protos.Client.SpecialQueries;
using Anis.Cashflow.Report.Grpc.Protos.Clients;
using Google.Protobuf.Collections;
using Newtonsoft.Json;

namespace Anis.Cashflow.Report.Grpc.Extensions
{
    public static class RebuildExtension
    {
        public static MessageBody<T> ToMessageBody<T>(this EventMessage eventMessage)
          => new()
          {
              AggregateId = eventMessage.AggregateId,
              Sequence = eventMessage.Sequence,
              Type = eventMessage.Type,
              Version = eventMessage.Version,
              Data = JsonConvert.DeserializeObject<T>(eventMessage.Data),
              DateTime = eventMessage.DateTime.ToDateTime(),
          };

        public static MessageBody<string> ToMessageBody(this EventMessage eventMessage)
          => new()
          {
              AggregateId = eventMessage.AggregateId,
              Sequence = eventMessage.Sequence,
              Type = eventMessage.Type,
              Version = eventMessage.Version,
              Data = eventMessage.Data,
              DateTime = eventMessage.DateTime.ToDateTime(),
          };

        public static MessageBody<string> ToMessageBody(this Protos.Clients.Holders.EventMessage eventMessage)
          => new()
          {
              AggregateId = eventMessage.AggregateId,
              Sequence = eventMessage.Sequence,
              Type = eventMessage.Type,
              Version = eventMessage.Version,
              Data = eventMessage.Data,
              DateTime = eventMessage.DateTime.ToDateTime(),
          };

        public static OperatorToBusinessLinkRebuildModel ToRebuildModel(this Protos.Client.NotificationMessage notificationMessage)
        {
            return new OperatorToBusinessLinkRebuildModel()
            {
                SubscriptionId = notificationMessage.OperatorSubscriptionId,
                StateVersion = notificationMessage.StateVersion,
                DataVersion = notificationMessage.DataVersion,
                BuildVersion = notificationMessage.BuildVersion,
                SubscriptionWallets = notificationMessage.Transfers.Select(w => new SubscriptionWalletBuild()
                {
                    WalletId = w.BusinessWalletId,
                    LinkDateTime = w.LinkDateTime.ToDateTime()

                }).ToList()
            };
        }

        public static WalletInputModel ToModel(this GetWalletsSnapshotResponse input)
        {
            return new WalletInputModel()
            {
                WalletId = input.WalletId,
                Sequence = (int)input.Sequence,
                SubscriptionType = input.SubscriptionType,
                Balance = (decimal)input.Balance,
                CurrencyType = (CurrencyType)input.CurrencyType,
                RegionId = input.RegionId,
                SubscriptionId = input.SubscriptionId,
                Type = input.Type.ToDomain(),
                HolderId = input.HolderId,
                DetailedSummations = input.Summations.ToModels()
            };
        }

        public static DetailedSummationModel ToModel(this DetailedSummation input)
        {
            return new DetailedSummationModel()
            {
                Source = input.Source,
                TotalCredit = (decimal)input.TotalCredit,
                TotalDebit = (decimal)input.TotalDebit,
                Type = input.Type,
            };
        }

        public static IEnumerable<DetailedSummationModel> ToModels(this RepeatedField<DetailedSummation> Repeateds)
        => Repeateds.Select(w => w.ToModel());

        public static AccountRefreshModel ToRefreshModel(this AccountSnapshotResponse response)
            => new()
            {
                AccountId = response.AccountId,
                ConfirmedAt = response.ConfirmedAt.ToDateTime()
            };
    }
}