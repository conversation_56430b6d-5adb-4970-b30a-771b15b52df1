﻿using Anis.Cashflow.Report.Domain.EventsData.SubscriptionWallets;

namespace Anis.Cashflow.Report.Test.Fakers.OperatorBusinessLinks.NotificationData
{
    public sealed class BusinessAddedDataFaker : PrivateFaker<BusinessAddedData>
    {
        public BusinessAddedDataFaker()
        {
            UsePrivateConstructor();
            RuleFor(r => r.BusinessWalletId, f => f.Random.String());
            RuleFor(r => r.BusinessSubscriptionId, f => f.Random.String());
            RuleFor(r => r.OperatorWalletId, f => f.Random.String());
            RuleFor(r => r.LinkDateTime, f => f.Date.PastOffset(1).DateTime);
        }
    }
}
