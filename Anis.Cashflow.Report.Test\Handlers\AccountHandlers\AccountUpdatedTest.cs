﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.AccountHandlers
{
    public class AccountUpdatedTest : TestBase
    {
        public AccountUpdatedTest(ITestOutputHelper output) : base(output)
        {
        }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task Update_UpdateAccountWithValidData_ReturnValid(bool sendLocationId)
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var secondCreateRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            await context.Regions.AddRangeAsync(createRegion, secondCreateRegion);

            await context.Accounts.AddAsync(createAccount);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var accountUpdatedData = new AccountUpdatedData(
                Number: "Number",
                LocationId: sendLocationId ? secondCreateRegion.Id : null);

            var messageBody = new MessageBody<AccountUpdatedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.AccountUpdated,
                Data = accountUpdatedData,
                DateTime = DateTime.Now,
                Version = 1,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccounts = await context.Accounts.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(messageBody, dbAccounts, createAccount);
        }

        [Fact]
        public async Task Update_UpdateAccountWithNonExistedAccount_EventNotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var messageBody = new MessageBody<AccountUpdatedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.AccountUpdated,
                Data = new(Guid.NewGuid().ToString(), Guid.NewGuid().ToString()),
                DateTime = DateTime.Now,
                Version = 1,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleOrDefaultAsync();

            Assert.False(isHandled);

            Assert.Null(dbAccount);
        }

        [Fact]
        public async Task Update_UpdateAccountWithRegionNotExist_EventNotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.Accounts.AddAsync(createAccount);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var accountUpdatedData = new AccountUpdatedData(
                Number: "Number",
                LocationId: Guid.NewGuid().ToString());

            var messageBody = new MessageBody<AccountUpdatedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.AccountUpdated,
                Data = accountUpdatedData,
                DateTime = DateTime.Now,
                Version = 1,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccounts = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(dbAccounts, createAccount);
        }

        [Fact]
        public async Task Update_EventArrivedWithNotValidSequence_EventNotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<AccountUpdatedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 4,
                Type = EventType.AccountUpdated,
                Data = new(Number: "Number", createRegion.Id),
                DateTime = DateTime.Now,
                Version = 1,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccount);
        }

        [Fact]
        public async Task Update_EventArrivedWithOldSequence_EventHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .RuleFor(f => f.Sequence, 5)
                .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<AccountUpdatedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.AccountUpdated,
                Data = new("123456", createRegion.Id),
                DateTime = DateTime.Now,
                Version = 1,
            };

            var isHandled = await mediator.Send(messageBody);

            Assert.True(isHandled);

            var dbAccounts = await context.Accounts.SingleAsync();

            EntityAssert.AssertEquality(createAccount, dbAccounts);
        }
    }
}