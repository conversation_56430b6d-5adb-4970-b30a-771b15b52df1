﻿using Anis.Cashflow.Report.Domain.EventsData.SubscriptionWallets;
using Anis.Cashflow.Report.Domain.Models;

namespace Anis.Cashflow.Report.Domain.Entities
{
    public class SubscriptionWallet
    {
        private SubscriptionWallet() { }

        public SubscriptionWallet(INotificationBody<BusinessAddedData> notification)
        {
            SubscriptionId = notification.OperatorSubscriptionId;
            WalletId = notification.Data.BusinessWalletId;
            LinkDateTime = notification.Data.LinkDateTime;
        }

        public SubscriptionWallet(string subscriptionId, string walletId, DateTime linkDateTime)
        {
            SubscriptionId = subscriptionId;
            WalletId = walletId;
            LinkDateTime = linkDateTime;
        }

        public int Id { get; private set; }

        public string SubscriptionId { get; private set; }
        public Subscription Subscription { get; private set; }

        public string WalletId { get; private set; }
        public Wallet Wallet { get; private set; }

        public DateTime? LinkDateTime { get; private set; }
    }
}
