﻿using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Grpc.Resources;
using Anis.Cashflow.Report.Infra.Helpers;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Resources;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Holders;
using Anis.Cashflow.Report.Test.Fakers.SubscriptionWallets;
using Anis.Cashflow.Report.Test.Grpc.Protos.Managmant;
using Calzolari.Grpc.Net.Client.Validation;
using Grpc.Core;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;
using SubscriptionType = Anis.Cashflow.Report.Test.Grpc.Protos.Managmant.SubscriptionType;

namespace Anis.Cashflow.Report.Test.GrpcTest
{
    public class CashflowFilterTest : TestBase
    {
        public CashflowFilterTest(ITestOutputHelper output) : base(output) { }

        [Theory]
        [InlineData(true)]
        [InlineData(false)]
        public async Task Index_ValidData_ReturnValid(bool isConfirmedAtNull)
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(r => r.ConfirmedAt, f => isConfirmedAtNull ? null : DateTime.UtcNow)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(r => r.ConfirmedAt, f => isConfirmedAtNull ? null : DateTime.UtcNow)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_AddAverageSales_ReturnValid()
        {
            var firstAverageSale = 111.8m;
            var secAverageSale = 222.3m;

            var firstTotalSalesLastDay = 1000m;
            var secTotalSalesLastDay = 2000m;

            var averageSaleLocation = 50;

            var firstWalletNetBalance = 1000;
            var secondWalletNetBalance = 2000;

            var firstWalletId = Guid.NewGuid().ToString();
            var secondWalletId = Guid.NewGuid().ToString();

            var walletAverageSales = new List<WalletAverageSaleModel>()
            {
                new WalletAverageSaleModel
                {
                    WalletId =firstWalletId,
                    AverageSales=firstAverageSale,
                     TotalSalesLastDay = firstTotalSalesLastDay,
                },

                new WalletAverageSaleModel
                {
                    WalletId =secondWalletId,
                    AverageSales=secAverageSale,
                    TotalSalesLastDay =secTotalSalesLastDay
                }
            };

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: walletAverageSales);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Id, firstWalletId)
                .RuleFor(s => s.NetBalance, firstWalletNetBalance)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Id, secondWalletId)
                .RuleFor(s => s.NetBalance, secondWalletNetBalance)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest() { CurrentPage = 1, PageSize = 25, LocationId = region.Id });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            EntityAssert.AssertPositiveBalanceRateEquality(resFirstWallet: resFirstWallet, averageSale: firstAverageSale);

            EntityAssert.AssertPositiveBalanceRateEquality(resFirstWallet: resSecondWallet, averageSale: secAverageSale);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name, averageSale: firstAverageSale, totalSalesLastDay: firstTotalSalesLastDay);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name, averageSale: secAverageSale, totalSalesLastDay: secTotalSalesLastDay);
        }

        [Fact]
        public async Task Index_ValidData_ReturnValidPositiveAverageSalesRate()
        {
            var positiveFirstBalanceRate = 10;
            var positiveSecondBalanceRate = 20;

            var averageSale = 100;
            var averageSaleLocation = 50;

            var firstWalletNetBalance = 1000;
            var secondWalletNetBalance = 2000;

            var firstWalletId = Guid.NewGuid().ToString();
            var secondWalletId = Guid.NewGuid().ToString();

            var walletAverageSales = new List<WalletAverageSaleModel>()
            {
                new WalletAverageSaleModel
                {
                    WalletId =firstWalletId,
                    AverageSales=averageSale
                },

                new WalletAverageSaleModel
                {
                    WalletId =secondWalletId,
                    AverageSales=averageSale
                }
            };

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: walletAverageSales);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Id, firstWalletId)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.NetBalance, firstWalletNetBalance)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Id, secondWalletId)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.NetBalance, secondWalletNetBalance)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest() { CurrentPage = 1, PageSize = 25, LocationId = region.Id });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            Assert.Equal(positiveFirstBalanceRate, resFirstWallet.PositveBalanceRate);

            Assert.Equal(positiveSecondBalanceRate, resSecondWallet.PositveBalanceRate);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name, averageSale: averageSale);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name, averageSale: averageSale);
        }

        [Fact]
        public async Task Index_ValidData_ReturnValidWithZeroPositiveAverageRate()
        {
            var averageSale = 0;

            var averageSalesLocation = 100;

            var firstWalletNetBalance = 1000;
            var secondWalletNetBalance = 4000;

            var firstWalletId = Guid.NewGuid().ToString();
            var secondWalletId = Guid.NewGuid().ToString();

            var walletAverageSales = new List<WalletAverageSaleModel>()
            {
                   new WalletAverageSaleModel
                   {
                       WalletId =firstWalletId,
                       AverageSales=averageSale
                    },
                  new WalletAverageSaleModel
                  {
                       WalletId =secondWalletId,
                       AverageSales=averageSale
                  }
            };

            Initialize(averageSaleLocation: averageSalesLocation, salesWalletAverage: walletAverageSales);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Id, firstWalletId)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.NetBalance, firstWalletNetBalance)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Id, secondWalletId)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.NetBalance, secondWalletNetBalance)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            var totalNetBalance = await context.Wallets.SumAsync(x => x.NetBalance);

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest() { CurrentPage = 1, PageSize = 25, LocationId = region.Id });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(averageSalesLocation, (decimal)response.AverageSalesLocation);

            Assert.Equal((totalNetBalance / averageSalesLocation), (decimal)response.AverageSalesLocationRate);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            Assert.Equal(0, resFirstWallet.PositveBalanceRate);

            Assert.Equal(0, resSecondWallet.PositveBalanceRate);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name, averageSale: averageSale);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name, averageSale: averageSale);
        }

        [Fact]
        public async Task Index_ValidData_ReturnValidAverageSalesLocationRate()
        {
            var positiveFirstBalanceRate = 10;
            var positiveSecondBalanceRate = 20;

            var averageSalesLocation = 500;
            var averageSales = 100;

            var firstWalletNetBalance = 1000;
            var secondWalletNetBalance = 2000;

            var firstWalletId = Guid.NewGuid().ToString();
            var secondWalletId = Guid.NewGuid().ToString();

            var walletAverageSales = new List<WalletAverageSaleModel>()
            {
                new WalletAverageSaleModel
                {
                    WalletId =firstWalletId,
                    AverageSales=averageSales
                },

                new WalletAverageSaleModel
                {
                    WalletId =secondWalletId,
                    AverageSales=averageSales
                }
            };

            Initialize(averageSaleLocation: averageSalesLocation, salesWalletAverage: walletAverageSales);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Id, firstWalletId)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.NetBalance, firstWalletNetBalance)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Id, secondWalletId)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.NetBalance, secondWalletNetBalance)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            var totalNetBalance = await context.Wallets.SumAsync(x => x.NetBalance);

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest() { CurrentPage = 1, PageSize = 25, LocationId = region.Id });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(averageSalesLocation, (decimal)response.AverageSalesLocation);

            Assert.Equal((totalNetBalance / averageSalesLocation), (decimal)response.AverageSalesLocationRate);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            Assert.Equal(positiveFirstBalanceRate, resFirstWallet.PositveBalanceRate);

            Assert.Equal(positiveSecondBalanceRate, resSecondWallet.PositveBalanceRate);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name, averageSale: averageSales);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name, averageSale: averageSales);
        }

        [Fact]
        public async Task Index_ValidDatal_ReturnValidWithZeroAverageSalesLocationRate()
        {
            var AverageSalesLocation = 0;

            var firstAverageSale = 200;
            var secAverageSale = 100;

            var firstWalletNetBalance = 1000;
            var secondWalletNetBalance = 3000;

            var firstWalletId = Guid.NewGuid().ToString();
            var secondWalletId = Guid.NewGuid().ToString();

            var walletAverageSales = new List<WalletAverageSaleModel>()
            {
                    new WalletAverageSaleModel
                    {
                         WalletId =firstWalletId,
                         AverageSales=firstAverageSale
                    },
                    new WalletAverageSaleModel
                    {
                         WalletId =secondWalletId,
                         AverageSales=secAverageSale
                    }
            };

            Initialize(averageSaleLocation: AverageSalesLocation, salesWalletAverage: walletAverageSales);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Id, firstWalletId)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.NetBalance, firstWalletNetBalance)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Id, secondWalletId)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.NetBalance, secondWalletNetBalance)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest() { CurrentPage = 1, PageSize = 25, LocationId = region.Id });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(AverageSalesLocation, (decimal)response.AverageSalesLocation);

            Assert.Equal(0, (decimal)response.AverageSalesLocationRate);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            Assert.Equal(5, resFirstWallet.PositveBalanceRate);

            Assert.Equal(30, resSecondWallet.PositveBalanceRate);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name, averageSale: firstAverageSale);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name, averageSale: secAverageSale);
        }

        [Fact]
        public async Task Index_Credit_ValidData_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(e => e.ExpiryAt, DateTime.UtcNow.AddHours(-2))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Balance, 500)
                .RuleFor(s => s.NetBalance, 500)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Balance, 200)
                .RuleFor(s => s.NetBalance, 200)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest() { CurrentPage = 1, PageSize = 25, LocationId = region.Id });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            Assert.Equal(firstWalletDb.NetBalance + secondWalletDb.NetBalance, (decimal)response.TotalNetBalance);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);
            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_Debit_ValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(e => e.ExpiryAt, DateTime.UtcNow.AddDays(-1))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest() { CurrentPage = 1, PageSize = 25, LocationId = region.Id });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(firstWalletDb.NetBalance + secondWalletDb.NetBalance, (decimal)response.TotalNetBalance);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);
            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidData_Filters_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(p => p.Phone, "**********").Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(s => s.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(s => s.CurrencyType, CurrencyType.Usd)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                LocationId = region.Id,
                Type = SubscriptionType.Business,
                Currency = Currency.Usd,
                DebtOnly = true,
                PhoneNumber = "**********",
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            Assert.Equal(firstWalletDb.NetBalance + secondWalletDb.NetBalance, (decimal)response.TotalNetBalance);

            var resWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var accountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            EntityAssert.AssertEquality(resWallet, secondWalletDb, accountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidData_SubscriptionTypeFilter_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(e => e.ExpiryAt, DateTime.UtcNow.AddDays(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(p => p.Phone, "**********").Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var fourthWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            var fourthSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, fourthWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet, fourthWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet, fourthSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                LocationId = region.Id,
                Type = SubscriptionType.Business,
                CurrentPage = 1,
                PageSize = 25
            });

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            var fourthWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == fourthWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(firstWallet.NetBalance + thirdWallet.NetBalance + fourthWalletDb.NetBalance + secondWalletDb.NetBalance, (decimal)response.TotalNetBalance);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == fourthWalletDb.Id);

            var PositiveRate = resFirstWallet.PositveBalanceRate;

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);
            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, fourthWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidData_CurrencyTypeFilter_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(a => a.LocationId, region.Id)
                .Generate();
            var secondAccount = new AccountFaker()
                .RuleFor(a => a.LocationId, region.Id)
                .RuleFor(e => e.ExpiryAt, DateTime.UtcNow.AddDays(-3))
                .RuleFor(p => p.Phone, "**********").Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.CurrencyType, CurrencyType.Usd)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.CurrencyType, value: CurrencyType.Lyd)
                .Generate();

            var fourthWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.CurrencyType, CurrencyType.Usd)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            var fourthSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, fourthWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet, fourthWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet, fourthSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                LocationId = region.Id,
                Currency = Currency.Usd,
                CurrentPage = 1,
                PageSize = 25
            });

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            var fourthWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == fourthWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(firstWallet.NetBalance + thirdWallet.NetBalance + fourthWalletDb.NetBalance + secondWalletDb.NetBalance, (decimal)response.TotalNetBalance);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == fourthWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);
            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, fourthWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidData_DebtOnlyFilter_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(e => e.ExpiryAt, DateTime.UtcNow.AddDays(-2))
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(p => p.Phone, "**********").Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(r => r.CurrentDelayed, 0)
                .RuleFor(r => r.CurrentUrgent, 0)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(r => r.CurrentDelayed, 0)
                .RuleFor(r => r.CurrentUrgent, 0)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(r => r.CurrentDelayed, 0)
                .RuleFor(r => r.CurrentUrgent, 0)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.CurrencyType, CurrencyType.Lyd)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var fourthWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(r => r.CurrentDelayed, 0)
                .RuleFor(r => r.CurrentUrgent, 0)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.CurrencyType, CurrencyType.Usd)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var fifthWallet = new WalletFaker()
                .RuleFor(r => r.Balance, 150)
                .RuleFor(r => r.CurrentDelayed, 120)
                .RuleFor(r => r.CurrentUrgent, 0)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.CurrencyType, CurrencyType.Usd)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var sixthWallet = new WalletFaker()
                .RuleFor(r => r.Balance, 350)
                .RuleFor(r => r.CurrentDelayed, 0)
                .RuleFor(r => r.CurrentUrgent, 55)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.CurrencyType, CurrencyType.Usd)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            var fourthSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, fourthWallet)
                .Generate();

            var fifthSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, fifthWallet)
                .Generate();

            var sixthSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, sixthWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet, fourthWallet, fifthWallet, sixthWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet,
                fourthSubscriptionWallet, fifthSubscriptionWallet, sixthSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                LocationId = region.Id,
                DebtOnly = true,
                CurrentPage = 1,
                PageSize = 25
            });

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            var fourthWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == fourthWallet.Id);

            var fifthWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == fifthWallet.Id);

            var sixthWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == sixthWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(4, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(4, response.Total);

            Assert.Equal(firstWallet.NetBalance + thirdWallet.NetBalance + fourthWalletDb.NetBalance + secondWalletDb.NetBalance +
                fifthWalletDb.NetBalance + sixthWalletDb.NetBalance, (decimal)response.TotalNetBalance);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == secondWallet.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == fourthWallet.Id);

            var resThirdWallet = response.WalletDtos.Single(i => i.Id == fifthWallet.Id);

            var resFourthWallet = response.WalletDtos.Single(i => i.Id == sixthWallet.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);
            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, fourthWalletDb, secondAccountState, secondSubscription.Name);
            EntityAssert.AssertEquality(resThirdWallet, fifthWallet, secondAccountState, secondSubscription.Name);
            EntityAssert.AssertEquality(resFourthWallet, sixthWallet, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidData_PhoneNumberFilter_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(e => e.ExpiryAt, DateTime.UtcNow.AddDays(-2))
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(p => p.Phone, "**********").Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.CurrencyType, CurrencyType.Lyd)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var fourthWallet = new WalletFaker()
                .RuleFor(r => r.Balance, f => decimal.Round(-f.Random.Decimal(), 3))
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.CurrencyType, CurrencyType.Usd)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            var fourthSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, fourthWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet, fourthWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet, fourthSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                LocationId = region.Id,
                PhoneNumber = "**********",
                CurrentPage = 1,
                PageSize = 25
            });

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            var fourthWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == fourthWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(firstWallet.NetBalance + thirdWallet.NetBalance + fourthWalletDb.NetBalance + secondWalletDb.NetBalance, (decimal)response.TotalNetBalance);

            var resFirstWallet = response.WalletDtos.First(i => i.Id == secondWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Last(i => i.Id == fourthWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);
            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, fourthWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidData_RegionFilter_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var firstRegion = new RegionFaker().RuleFor(c => c.Code, "01").Generate();

            var secondRegion = new RegionFaker().RuleFor(c => c.Code, "0102").Generate();

            var thirdRegion = new RegionFaker().RuleFor(c => c.Code, "010203").Generate();

            var fourthRegion = new RegionFaker().RuleFor(c => c.Code, "02").Generate();

            var fifthRegion = new RegionFaker().RuleFor(c => c.Code, "0201").Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, thirdRegion.Id)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var fourthAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, fourthRegion.Id)
                .RuleFor(e => e.ExpiryAt, DateTime.UtcNow.AddHours(-2))
                .Generate();

            var fourthSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, fourthAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var fifthAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, fifthRegion.Id)
                .Generate();

            var fifthSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, fifthAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, fourthRegion.Id)
                .RuleFor(s => s.SubscriptionId, fourthSubscription.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion, thirdRegion, fourthRegion, fifthRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount, fourthAccount, fifthAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription, fourthSubscription, fifthSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                LocationId = secondRegion.Id,
                CurrentPage = 1,
                PageSize = 25
            });

            var firstWalletDb = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .ThenInclude(l => l.Location)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .ThenInclude(l => l.Location)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.First(i => i.Id == firstWalletDb.Id);
            var resSecondWallet = response.WalletDtos.First(i => i.Id == secondWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);
            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, firstAccountState, secondSubscription.Name);
        }
        //Todo remove
        //[Theory]
        //[InlineData(" ", nameof(Request.LocationId))]
        //[InlineData(null, nameof(Request.LocationId))]
        //public async Task Index_InValidData_RegionFilter_ReturnValid(string locationId, string error)
        //{
        //    using var scope = Factory.Services.CreateScope();

        //    var client = new CashflowReportQueries.CashflowReportQueriesClient(Channel);

        //    var request = new Request() { CurrentPage = 1, PageSize = 25, LocationId = locationId };

        //    var exception = await Assert.ThrowsAsync<RpcException>(async () => await client.IndexAsync(request));

        //    Assert.NotEmpty(exception.Status.Detail);

        //    Assert.Equal(StatusCode.InvalidArgument, exception.StatusCode);

        //    Assert.Contains(exception.GetValidationErrors(), e => e.PropertyName.EndsWith(error));
        //}

        [Fact]
        public async Task Index_GetWalletsWithValidOrderBy_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var firstRegion = new RegionFaker().RuleFor(c => c.Code, "01").Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(a => a.ExpiryAt, DateTime.UtcNow.AddDays(-1))// Expired .
                .RuleFor(a => a.LocationId, firstRegion.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(a => a.ExpiryAt, a => null)// Not expired .
                .RuleFor(a => a.LocationId, firstRegion.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(a => a.ExpiryAt, a => DateTime.UtcNow.AddDays(2)) // Has value and not expired .
                .RuleFor(a => a.LocationId, firstRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(r => r.CurrentDelayed, f => 0)
                .RuleFor(r => r.CurrentUrgent, f => 0)
                .RuleFor(r => r.Balance, f => 125)
                .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(r => r.CurrentDelayed, f => 500)
                .RuleFor(r => r.CurrentUrgent, f => 0)
                .RuleFor(r => r.Balance, f => 200)
                .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var thirdWallet = new WalletFaker()
               .RuleFor(r => r.RegionId, firstRegion.Id)
               .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
               .RuleFor(r => r.CurrentDelayed, f => 1000)
               .RuleFor(r => r.CurrentUrgent, f => 0)
               .RuleFor(r => r.Balance, f => 90)
               .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
               .Generate();

            var fourthWallet = new WalletFaker()
               .RuleFor(r => r.RegionId, firstRegion.Id)
               .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
               .RuleFor(r => r.CurrentDelayed, f => 0)
               .RuleFor(r => r.CurrentUrgent, f => 10)
               .RuleFor(r => r.Balance, f => 140)
               .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
               .Generate();

            var fifthWallet = new WalletFaker()
               .RuleFor(r => r.RegionId, firstRegion.Id)
               .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
               .RuleFor(r => r.CurrentDelayed, f => 40)
               .RuleFor(r => r.CurrentUrgent, f => 135)
               .RuleFor(r => r.Balance, f => 145)
               .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
               .Generate();

            var sixthWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
                .RuleFor(r => r.CurrentDelayed, f => 0)
                .RuleFor(r => r.CurrentUrgent, f => 0)
                .RuleFor(r => r.Balance, f => 50)
                .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var seventhWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
                .RuleFor(r => r.CurrentDelayed, f => 0)
                .RuleFor(r => r.CurrentUrgent, f => 0)
                .RuleFor(r => r.Balance, f => 550)
                .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var eighthWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
                .RuleFor(r => r.CurrentDelayed, f => 0)
                .RuleFor(r => r.CurrentUrgent, f => 0)
                .RuleFor(r => r.Balance, f => -425)
                .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            var fourthSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, fourthWallet)
                .Generate();

            var fifthSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, fifthWallet)
                .Generate();

            var sixthSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, sixthWallet)
                .Generate();

            var seventhSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, seventhWallet)
                .Generate();

            var eighthSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, thirdSubscription)
                .RuleFor(s => s.Wallet, eighthWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet, fourthWallet, fifthWallet, sixthWallet, seventhWallet, eighthWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet, fourthSubscriptionWallet,
                fifthSubscriptionWallet, sixthSubscriptionWallet, seventhSubscriptionWallet, eighthSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                LocationId = firstRegion.Id,
                PhoneNumber = " ",
                Type = SubscriptionType.AllOptions,
                Currency = Currency.All,
                CurrentPage = 1,
                PageSize = 25
            });

            Assert.NotNull(response);

            Assert.Equal(8, response.WalletDtos.Count);

            Assert.Equal(8, response.Total);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            EntityAssert.AssertEquality(response.WalletDtos[0], firstWallet, CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState), firstSubscription.Name);
            EntityAssert.AssertEquality(response.WalletDtos[1], thirdWallet, CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState), secondSubscription.Name);
            EntityAssert.AssertEquality(response.WalletDtos[2], secondWallet, CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState), secondSubscription.Name);
            EntityAssert.AssertEquality(response.WalletDtos[3], fifthWallet, CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState), secondSubscription.Name);
            EntityAssert.AssertEquality(response.WalletDtos[4], fourthWallet, CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState), secondSubscription.Name);
            EntityAssert.AssertEquality(response.WalletDtos[5], eighthWallet, CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState), thirdSubscription.Name);
            EntityAssert.AssertEquality(response.WalletDtos[6], sixthWallet, CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState), thirdSubscription.Name);
            EntityAssert.AssertEquality(response.WalletDtos[7], seventhWallet, CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState), thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_BlackListSubscriptionsModel_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            var firstId = "B565A818-8C58-4E5E-2B3E-08DA3675F384";

            var blackListSubscriptions = new List<string>()
            {
                 firstId
            };

            Initialize(averageSaleLocation: averageSaleLocation,
                  blackListSubscriptions: blackListSubscriptions);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(e => e.ExpiryAt, DateTime.UtcNow.AddHours(-2))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.Id, firstId)
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Balance, 500)
                .RuleFor(s => s.NetBalance, 500)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Balance, 200)
                .RuleFor(s => s.NetBalance, 200)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest() { CurrentPage = 1, PageSize = 25, LocationId = region.Id });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            Assert.Equal(firstWalletDb.NetBalance, (decimal)response.TotalNetBalance);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);
            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidData_ReturnValidAverageSalesLocationRateOnSpecificDays()
        {
            var positiveFirstBalanceRate = 10;
            var positiveSecondBalanceRate = 20;

            var averageSalesLocation = 500;
            var averageSales = 100;

            var firstWalletNetBalance = 1000;
            var secondWalletNetBalance = 2000;

            var firstWalletId = Guid.NewGuid().ToString();
            var secondWalletId = Guid.NewGuid().ToString();

            var walletAverageSales = new List<WalletAverageSaleModel>()
               {
                   new WalletAverageSaleModel
                   {
                       WalletId =firstWalletId,
                       AverageSales=averageSales
                   },

                   new WalletAverageSaleModel
                   {
                       WalletId =secondWalletId,
                       AverageSales=averageSales
                   }
               };

            Initialize(averageSaleLocation: averageSalesLocation, salesWalletAverage: walletAverageSales);

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.Id, firstWalletId)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.NetBalance, firstWalletNetBalance)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.Id, secondWalletId)
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.NetBalance, secondWalletNetBalance)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            var totalNetBalance = await context.Wallets.SumAsync(x => x.NetBalance);

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest() { CurrentPage = 1, PageSize = 25, LocationId = region.Id, DaysCount = 3 });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(averageSalesLocation, (decimal)response.AverageSalesLocation);

            Assert.Equal((totalNetBalance / averageSalesLocation), (decimal)response.AverageSalesLocationRate);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            Assert.Equal(positiveFirstBalanceRate, resFirstWallet.PositveBalanceRate);

            Assert.Equal(positiveSecondBalanceRate, resSecondWallet.PositveBalanceRate);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name, averageSale: averageSales);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name, averageSale: averageSales);
        }

        [Fact]
        public async Task Index_ValidData_RegionFilterAccountHaveMultiWalletOnDeffRegion_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var firstRegion = new RegionFaker().RuleFor(c => c.Code, "01").Generate();

            var secondRegion = new RegionFaker().RuleFor(c => c.Code, "0102").Generate();

            var thirdRegion = new RegionFaker().RuleFor(c => c.Code, "010203").Generate();

            var fourthRegion = new RegionFaker().RuleFor(c => c.Code, "02").Generate();

            var fifthRegion = new RegionFaker().RuleFor(c => c.Code, "0201").Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, thirdRegion.Id)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var fourthAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, fourthRegion.Id)
                .RuleFor(e => e.ExpiryAt, DateTime.UtcNow.AddHours(-2))
                .Generate();

            var fourthSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, fourthAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var fifthAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, fifthRegion.Id)
                .Generate();

            var fifthSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, fifthAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, fourthRegion.Id)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion, thirdRegion, fourthRegion, fifthRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount, fourthAccount, fifthAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription, fourthSubscription, fifthSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                LocationId = secondRegion.Id,
                CurrentPage = 1,
                PageSize = 25,
            });

            var firstWalletDb = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .ThenInclude(l => l.Location)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .ThenInclude(l => l.Location)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            var resFirstWallet = response.WalletDtos.First(i => i.Id == firstWalletDb.Id);
            var resSecondWallet = response.WalletDtos.First(i => i.Id == secondWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, firstAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidData_RegionFilterWithNullLocationId_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var firstRegion = new RegionFaker().RuleFor(c => c.Code, "01").Generate();

            var secondRegion = new RegionFaker().RuleFor(c => c.Code, "0102").Generate();

            var thirdRegion = new RegionFaker().RuleFor(c => c.Code, "010203").Generate();

            var fourthRegion = new RegionFaker().RuleFor(c => c.Code, "02").Generate();

            var fifthRegion = new RegionFaker().RuleFor(c => c.Code, "0201").Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, firstRegion.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, secondRegion.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, thirdRegion.Id)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var fourthAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, fourthRegion.Id)
                .RuleFor(e => e.ExpiryAt, DateTime.UtcNow.AddHours(-2))
                .Generate();

            var fourthSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, fourthAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Business)
                .Generate();

            var fifthAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, fifthRegion.Id)
                .Generate();

            var fifthSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, fifthAccount.Id)
                .RuleFor(s => s.Type, Domain.Enums.SubscriptionType.Normal)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, fourthRegion.Id)
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(r => r.RegionId, firstRegion.Id)
                .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddRangeAsync(firstRegion, secondRegion, thirdRegion, fourthRegion, fifthRegion);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount, fourthAccount, fifthAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription, fourthSubscription, fifthSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                LocationId = null,
                CurrentPage = 1,
                PageSize = 25
            });

            var dbFirstWallet = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .ThenInclude(l => l.Location)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == firstWallet.Id);

            var dbSconedWallet = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .ThenInclude(l => l.Location)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == secondWallet.Id);

            var dbThirdWallet = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .ThenInclude(l => l.Location)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(3, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(3, response.Total);

            var resFirstWallet = response.WalletDtos.First(i => i.Id == dbFirstWallet.Id);
            var resSecondWallet = response.WalletDtos.First(i => i.Id == dbSconedWallet.Id);
            var resThirdWallet = response.WalletDtos.First(i => i.Id == thirdWallet.Id);

            var accountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            EntityAssert.AssertEquality(resFirstWallet, dbFirstWallet, accountState, firstSubscription.Name);
            EntityAssert.AssertEquality(resSecondWallet, dbSconedWallet, accountState, secondSubscription.Name);
            EntityAssert.AssertEquality(resThirdWallet, dbThirdWallet, accountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataAndNormalOnlyWallet_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
               .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
               .RuleFor(s => s.Subscription, thirdSubscription)
               .RuleFor(s => s.Type, WalletType.Normal)
               .RuleFor(r => r.RegionId, region.Id)
               .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.WalletId, firstWallet.Id)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.WalletId, secondWallet.Id)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
                .RuleFor(s => s.WalletId, thirdWallet.Id)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                WalletTypeFilter = WalletTypeFilter.NormalWallets
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).
                Include(s => s.Region)
               .SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var thirdAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, thirdAccountState, thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataAndProfitWallet_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
               .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
               .RuleFor(s => s.Subscription, thirdSubscription)
               .RuleFor(s => s.Type, WalletType.Normal)
               .RuleFor(r => r.RegionId, region.Id)
               .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.WalletId, firstWallet.Id)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.WalletId, secondWallet.Id)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
                .RuleFor(s => s.WalletId, thirdWallet.Id)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                WalletTypeFilter = WalletTypeFilter.ProfitWallets
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWallet.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWallet.Id);

            var thirdAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, thirdAccountState, firstSubscription.Name);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, thirdAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_ValidDataAndNotNormalOnlyWallet_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var thirdAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var thirdSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, thirdAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Type, WalletType.Profits)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
               .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
               .RuleFor(s => s.Subscription, thirdSubscription)
               .RuleFor(s => s.Type, WalletType.Normal)
               .RuleFor(r => r.RegionId, region.Id)
               .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.WalletId, firstWallet.Id)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.WalletId, secondWallet.Id)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.SubscriptionId, thirdSubscription.Id)
                .RuleFor(s => s.WalletId, thirdWallet.Id)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount, thirdAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription, thirdSubscription);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                WalletTypeFilter = WalletTypeFilter.AllWallets
            });

            var firstWalletDb = await context.Wallets
               .Include(s => s.Subscription)
               .ThenInclude(a => a.Account).
                Include(s => s.Region)
               .Include(w => w.Holder)
               .SingleAsync(i => i.Id == firstWallet.Id);

            var secondWalletDb = await context.Wallets
               .Include(s => s.Subscription)
               .ThenInclude(a => a.Account)
               .Include(s => s.Region)
               .Include(w => w.Holder)
               .SingleAsync(i => i.Id == secondWallet.Id);

            var thirdWalletDb = await context.Wallets
               .Include(s => s.Subscription)
               .ThenInclude(a => a.Account)
               .Include(s => s.Region)
               .Include(w => w.Holder)
               .SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(3, response.WalletDtos.Count());

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(3, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var resThirdWallet = response.WalletDtos.Single(i => i.Id == thirdWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var thirdAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, thirdAccountState, firstSubscription.Name);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);

            EntityAssert.AssertEquality(resThirdWallet, thirdWalletDb, thirdAccountState, thirdSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenGivenAllCashAndHolders_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var holderFaker = new HolderFaker().Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, holderFaker.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Holders.AddAsync(holderFaker);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                CashAndHoldersFilter = CashAndHoldersFilter.AllCashAndHolders
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);
            var thirdWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == thirdWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(3, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(3, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var resThirdWallet = response.WalletDtos.Single(i => i.Id == thirdWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            var thirdAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);

            EntityAssert.AssertEquality(resThirdWallet, thirdWalletDb, thirdAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenGivenCashOnly_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var holderFaker = new HolderFaker().Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, holderFaker.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Holders.AddAsync(holderFaker);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                CashAndHoldersFilter = CashAndHoldersFilter.CashOnly
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenGivenCashOnlyAndWalletType_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.Type, WalletType.Profits)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.Type, WalletType.Normal)
                .Generate();

            var holderFaker = new HolderFaker().Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, holderFaker.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Holders.AddAsync(holderFaker);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                CashAndHoldersFilter = CashAndHoldersFilter.CashOnly,
                WalletTypeFilter = WalletTypeFilter.NormalWallets
            });

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenGivenCashOnlyAndCurrencyType_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.CurrencyType, CurrencyType.Usd)
                .Generate();

            var holderFaker = new HolderFaker().Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, holderFaker.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Holders.AddAsync(holderFaker);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                CashAndHoldersFilter = CashAndHoldersFilter.CashOnly,
                Currency = Currency.Usd
            });

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenGivenHoldersOnly_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var holderFaker = new HolderFaker().Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, holderFaker.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, holderFaker.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Holders.AddAsync(holderFaker);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                CashAndHoldersFilter = CashAndHoldersFilter.HoldersOnly
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Equal(2, response.WalletDtos.Count);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(2, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resFirstWallet = response.WalletDtos.Single(i => i.Id == firstWalletDb.Id);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var firstAccountState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resFirstWallet, firstWalletDb, firstAccountState, firstSubscription.Name);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenGivenHoldersOnlyAndWalletType_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var holderFaker = new HolderFaker().Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, holderFaker.Id)
                .RuleFor(r => r.Type, WalletType.Normal)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, holderFaker.Id)
                .RuleFor(r => r.Type, WalletType.Profits)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Holders.AddAsync(holderFaker);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                CashAndHoldersFilter = CashAndHoldersFilter.HoldersOnly,
                WalletTypeFilter = WalletTypeFilter.ProfitWallets
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenGivenHoldersOnlyAndCurrencyType_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var holderFaker = new HolderFaker().Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, holderFaker.Id)
                .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, holderFaker.Id)
                .RuleFor(r => r.CurrencyType, CurrencyType.Usd)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Holders.AddAsync(holderFaker);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                CashAndHoldersFilter = CashAndHoldersFilter.HoldersOnly,
                Currency = Currency.Usd
            });

            var firstWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == firstWallet.Id);
            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Theory]
        [InlineData(null!)]
        [InlineData("********-0000-0000-0000-********0000")]
        [InlineData("6f7ae902-08bf-43fe-bb66-9c11a6b75d1")]
        public async Task Index_WhenGivenInvalidSpecificHolders_ThrowInvalidArgumentException(string holderId)
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var exception = await Assert.ThrowsAsync<RpcException>(async () => await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = Guid.NewGuid().ToString(),
                CashAndHoldersFilter = CashAndHoldersFilter.SpecificHolder,
                HolderId = holderId
            }));

            Assert.NotEmpty(exception.Status.Detail);

            Assert.Equal(StatusCode.InvalidArgument, exception.StatusCode);

            Assert.Contains(exception.GetValidationErrors(), e => e.PropertyName.EndsWith(Titles.HoldersId));

        }

        [Fact]
        public async Task Index_WhenGivenSpecificHolders_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstHolderFaker = new HolderFaker().Generate();

            var secondHolderFaker = new HolderFaker().Generate();

            var thirdHolderFaker = new HolderFaker().Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, firstHolderFaker.Id)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, secondHolderFaker.Id)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, thirdHolderFaker.Id)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Holders.AddRangeAsync(firstHolderFaker, secondHolderFaker, thirdHolderFaker);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                CashAndHoldersFilter = CashAndHoldersFilter.SpecificHolder,
                HolderId = secondWallet.HolderId
            });

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenGivenSpecificHoldersAndWalletType_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstHolderFaker = new HolderFaker().Generate();

            var secondHolderFaker = new HolderFaker().Generate();

            var thirdHolderFaker = new HolderFaker().Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, secondHolderFaker.Id)
                .RuleFor(r => r.Type, WalletType.Profits)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, secondHolderFaker.Id)
                .RuleFor(r => r.Type, WalletType.Normal)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, secondHolderFaker.Id)
                .RuleFor(r => r.Type, WalletType.Profits)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            var thirdSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, thirdWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Holders.AddRangeAsync(firstHolderFaker, secondHolderFaker, thirdHolderFaker);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet, thirdSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                CashAndHoldersFilter = CashAndHoldersFilter.SpecificHolder,
                HolderId = secondWallet.HolderId,
                WalletTypeFilter = WalletTypeFilter.NormalWallets
            });

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenGivenSpecificHoldersAndCurrencyType_ReturnValid()
        {
            var averageSaleLocation = 520.5m;

            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var firstAccount = new AccountFaker().RuleFor(r => r.LocationId, region.Id).Generate();

            var secondAccount = new AccountFaker()
                .RuleFor(d => d.ExpiryAt, DateTime.UtcNow.AddHours(-3))
                .RuleFor(r => r.LocationId, region.Id)
                .Generate();

            var firstSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, firstAccount.Id)
                .Generate();

            var secondSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, secondAccount.Id)
                .Generate();

            var firstHolderFaker = new HolderFaker().Generate();

            var secondHolderFaker = new HolderFaker().Generate();

            var thirdHolderFaker = new HolderFaker().Generate();

            var firstWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, firstSubscription.Id)
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, secondHolderFaker.Id)
                .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var secondWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, secondHolderFaker.Id)
                .RuleFor(r => r.CurrencyType, CurrencyType.Usd)
                .Generate();

            var thirdWallet = new WalletFaker()
                .RuleFor(s => s.SubscriptionId, secondSubscription.Id)
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .RuleFor(r => r.HolderId, secondHolderFaker.Id)
                .RuleFor(r => r.CurrencyType, CurrencyType.Lyd)
                .Generate();

            var firstSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, firstSubscription)
                .RuleFor(s => s.Wallet, firstWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(s => s.Subscription, secondSubscription)
                .RuleFor(s => s.Wallet, secondWallet)
                .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddRangeAsync(firstAccount, secondAccount);

            await context.Subscriptions.AddRangeAsync(firstSubscription, secondSubscription);

            await context.Holders.AddRangeAsync(firstHolderFaker, secondHolderFaker, thirdHolderFaker);

            await context.Wallets.AddRangeAsync(firstWallet, secondWallet, thirdWallet);

            await context.SubscriptionWallets.AddRangeAsync(firstSubscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                CashAndHoldersFilter = CashAndHoldersFilter.SpecificHolder,
                HolderId = secondWallet.HolderId,
                Currency = Currency.Usd
            });

            var secondWalletDb = await context.Wallets.Include(s => s.Subscription).ThenInclude(a => a.Account).Include(s => s.Region).Include(w => w.Holder).SingleAsync(i => i.Id == secondWallet.Id);

            Assert.NotNull(response);

            Assert.Single(response.WalletDtos);

            Assert.Equal(25, response.PageSize);

            Assert.Equal(1, response.CurrentPage);

            Assert.Equal(1, response.Total);

            Assert.Equal(averageSaleLocation, (decimal)response.AverageSalesLocation);

            var resSecondWallet = response.WalletDtos.Single(i => i.Id == secondWalletDb.Id);

            var secondAccountState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(resSecondWallet, secondWalletDb, secondAccountState, secondSubscription.Name);
        }

        [Fact]
        public async Task Index_WhenExpirationOnlyIsTrueReturnOnlyExpiredAccounts_ReturnValid()
        {
            var averageSaleLocation = 520.5m;
            
            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();
            
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            
            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker()
                .Generate();

            var expiredAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(r => r.ExpiryAt, DateTime.UtcNow.AddDays(-1))
                .Generate();

            var activeAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(r => r.ExpiryAt, DateTime.UtcNow.AddDays(1))
                .Generate();

            var nullExpiryAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(r => r.ExpiryAt, f => null)
                .Generate();

            var expiredSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, expiredAccount.Id)
                .Generate();

            var activeSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, activeAccount.Id)
                .Generate();

            var nullExpirySubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, nullExpiryAccount.Id)
                .Generate();

            var expiredWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, expiredSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var activeWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, activeSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var nullExpiryWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, nullExpirySubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            await context.Regions.AddAsync(region);
            
            await context.Accounts.AddRangeAsync(expiredAccount, activeAccount, nullExpiryAccount);
            
            await context.Subscriptions.AddRangeAsync(expiredSubscription, activeSubscription, nullExpirySubscription);
            
            await context.Wallets.AddRangeAsync(expiredWallet, activeWallet, nullExpiryWallet);
            
            await context.SaveChangesAsync();
            
            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                ExpirationOnly = true
            });

            Assert.NotNull(response);
           
            Assert.Single(response.WalletDtos); // Should only return the expired wallet

            var returnedWallet = response.WalletDtos[0];
            
            var expiredWalletDb = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == expiredWallet.Id);

            var expiredState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            EntityAssert.AssertEquality(returnedWallet, expiredWalletDb, expiredState, expiredSubscription.Name);
            
            Assert.Null(returnedWallet.RemainingTime);
        }

        [Fact]
        public async Task Index_WhenExpirationOnlyIsFalseReturnAllAccountsWithRemainingTime_ReturnValid()
        {
            var averageSaleLocation = 520.5m;
            
            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();
            
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            
            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var expiredAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(r => r.ExpiryAt, DateTime.UtcNow.AddDays(-1))
                .Generate();

            var activeAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(r => r.ExpiryAt, DateTime.UtcNow.AddDays(1))
                .Generate();

            var expiredSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, expiredAccount.Id)
                .Generate();

            var activeSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, activeAccount.Id)
                .Generate();

            var expiredWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, expiredSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            var activeWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, activeSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            await context.Regions.AddAsync(region);
            
            await context.Accounts.AddRangeAsync(expiredAccount, activeAccount);
            
            await context.Subscriptions.AddRangeAsync(expiredSubscription, activeSubscription);
            
            await context.Wallets.AddRangeAsync(expiredWallet, activeWallet);
            
            await context.SaveChangesAsync();
            
            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                ExpirationOnly = false
            });

            Assert.NotNull(response);
            
            Assert.Equal(2, response.WalletDtos.Count);

            var activeWalletDb = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == activeWallet.Id);

            var expiredWalletDb = await context.Wallets
                .Include(s => s.Subscription)
                .ThenInclude(a => a.Account)
                .Include(s => s.Region)
                .SingleAsync(i => i.Id == expiredWallet.Id);

            var activeState = CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState);
            
            var expiredState = CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState);

            var activeResult = response.WalletDtos.Single(w => w.Id == activeWallet.Id);
            
            var expiredResult = response.WalletDtos.Single(w => w.Id == expiredWallet.Id);

            EntityAssert.AssertEquality(activeResult, activeWalletDb, activeState, activeSubscription.Name);
            
            EntityAssert.AssertEquality(expiredResult, expiredWalletDb, expiredState, expiredSubscription.Name);

            Assert.NotNull(activeResult.RemainingTime);
            
            Assert.Null(expiredResult.RemainingTime);
        }

        [Fact]
        public async Task Index_WhenExpirationOnlyIsTrueWithNoExpiredAccountsShouldBeReturnEmptyList_ReturnValid()
        {
            var averageSaleLocation = 520.5m;
            
            Initialize(averageSaleLocation: averageSaleLocation, salesWalletAverage: new());

            using var scope = Factory.Services.CreateScope();
            
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            
            var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

            var region = new RegionFaker().Generate();

            var activeAccount = new AccountFaker()
                .RuleFor(r => r.LocationId, region.Id)
                .RuleFor(r => r.ExpiryAt, DateTime.UtcNow.AddDays(1))
                .Generate();

            var activeSubscription = new SubscriptionFaker()
                .RuleFor(a => a.AccountId, activeAccount.Id)
                .Generate();

            var activeWallet = new WalletFaker()
                .RuleFor(s => s.Subscription, activeSubscription)
                .RuleFor(r => r.RegionId, region.Id)
                .Generate();

            await context.Regions.AddAsync(region);
            
            await context.Accounts.AddAsync(activeAccount);
            
            await context.Subscriptions.AddAsync(activeSubscription);
            
            await context.Wallets.AddAsync(activeWallet);
            
            await context.SaveChangesAsync();
            
            context.ChangeTracker.Clear();

            var response = await client.CashflowFilterAsync(new CashflowFilterRequest()
            {
                CurrentPage = 1,
                PageSize = 25,
                LocationId = region.Id,
                ExpirationOnly = true
            });

            Assert.NotNull(response);
            
            Assert.Empty(response.WalletDtos);
            
            Assert.Equal(0, response.Total);
        }
    }
}