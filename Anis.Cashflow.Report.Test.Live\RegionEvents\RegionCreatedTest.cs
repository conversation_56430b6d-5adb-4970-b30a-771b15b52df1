﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Live.Asserts;
using Ecom.Cards.Grpc.Demo.V2.Regions;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.RegionEvents
{
    public class RegionCreatedTest : TestBase
    {
        public RegionCreatedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Add_RegionAdded_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var channel = GrpcChannel.ForAddress(Address.Base);

            var grpcClient = new RegionsDemoEvents.RegionsDemoEventsClient(channel);

            var request = new CreateRequest()
            {
                AggregateId = Guid.NewGuid().ToString(),
                ArabicName = "ArabicName",
                EnglishName = "EnglishName",
                Code = "Code"
            };

            await grpcClient.CreateAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<RegionEventsListener>().CloseProccessorAsync();

            var region = await context.Regions.SingleOrDefaultAsync();

            Assert.NotNull(region);

            DemoEventAsserts.AssertEquality(request, region);
        }
    }
}
