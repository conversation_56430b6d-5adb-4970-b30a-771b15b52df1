﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Live.Protos;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.AccountEvents
{
    public class ExpiryDateRemovedTest : TestBase
    {
        public ExpiryDateRemovedTest(ITestOutputHelper output) : base(output) { }

        //ToDo RemoveExpiryDateRequest not Found
        //[Fact]
        public async Task Update_ExpiryDateRemovedWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var accountFaker = new AccountFaker()
                .RuleFor(f => f.Sequence, 2)
                .RuleFor(f => f.ExpiryAt, DateTime.UtcNow)
                .Generate();

            await context.Accounts.AddAsync(accountFaker);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var channel = GrpcChannel.ForAddress(Address.Base);

            var grpcClient = new AccountsDemoEvents.AccountsDemoEventsClient(channel);

            //var request = new RemoveExpiryDateRequest
            //{
            //    UserId = Guid.NewGuid().ToString(),
            //    AccountId = accountFaker.Id.ToString(),
            //    Sequence = 3,
            //};

            //await grpcClient.RemoveExpiryDateAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<AccountEventsListener>().CloseProccessorAsync();

            var dbAccount = await context.Accounts
                .SingleOrDefaultAsync();

            Assert.Equal(accountFaker.Id, dbAccount.Id);
            Assert.Equal(3, dbAccount.Sequence);
            Assert.Null(dbAccount.ExpiryAt);
        }
    }
}