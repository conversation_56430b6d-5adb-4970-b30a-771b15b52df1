﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Helpers;
using Anis.Cashflow.Report.Test.Grpc.Protos.Managmant;

namespace Anis.Cashflow.Report.Test.Asserts
{
    public static class EntityAssert
    {
        public static void AssertEquality(
        Account createAccount,
        Account dbAccount)
        {
            Assert.Equal(createAccount.Id, dbAccount.Id);
            Assert.Equal(createAccount.Sequence, dbAccount.Sequence);
            Assert.Equal(createAccount.Number, dbAccount.Number);
            Assert.Equal(createAccount.Email, dbAccount.Email);
            Assert.Equal(createAccount.Phone, dbAccount.Phone);
            Assert.Equal(createAccount.OwnerName, dbAccount.OwnerName);
            Assert.Equal(createAccount.LocationId, dbAccount.LocationId);
            Assert.Equal(createAccount.ExpiryAt, dbAccount.ExpiryAt);
            Assert.Equal(createAccount.ConfirmedAt, dbAccount.ConfirmedAt);
        }

        public static void AssertEquality(
            Account createAccount,
            Account dbAccount,
            long sequence)
        {
            Assert.Equal(createAccount.Id, dbAccount.Id);
            Assert.Equal(sequence, dbAccount.Sequence);
            Assert.Equal(createAccount.Number, dbAccount.Number);
            Assert.Equal(createAccount.Email, dbAccount.Email);
            Assert.Equal(createAccount.Phone, dbAccount.Phone);
            Assert.Equal(createAccount.OwnerName, dbAccount.OwnerName);
            Assert.Equal(createAccount.LocationId, dbAccount.LocationId);
            Assert.Equal(createAccount.ExpiryAt, dbAccount.ExpiryAt);
            Assert.Equal(createAccount.ConfirmedAt, dbAccount.ConfirmedAt);
        }

        public static void AssertEquality(
            MessageBody<AccountConfirmedData> messageBody,
            Account dbAccount)
        {
            Assert.Equal(messageBody.AggregateId, dbAccount.Id);
            Assert.Equal(messageBody.Sequence, dbAccount.Sequence);
            Assert.Equal(messageBody.Data.Number, dbAccount.Number);
            Assert.Equal(messageBody.Data.LocationId, dbAccount.LocationId);
            Assert.Equal(messageBody.Data.Email, dbAccount.Email);
            Assert.Equal(messageBody.Data.Phone, dbAccount.Phone);
            Assert.Equal(messageBody.Data.OwnerName, dbAccount.OwnerName);
            Assert.Equal(messageBody.Data.ConfirmedAt, dbAccount.ConfirmedAt);
        }

        public static void AssertEquality(
             MessageBody<PhoneChangedEventStoreData> messageBody,
             Account dbAccount)
        {
            Assert.Equal(messageBody.AggregateId, dbAccount.Id);
            Assert.Equal(messageBody.Sequence, dbAccount.Sequence);
            Assert.Equal(messageBody.Data.NewPhone, dbAccount.Phone);
        }

        public static void AssertEquality(
             MessageBody<AccountPhoneNumberChangedData> messageBody,
             Account dbAccount)
        {
            Assert.Equal(messageBody.AggregateId, dbAccount.Id);
            Assert.Equal(messageBody.Sequence, dbAccount.Sequence);
            Assert.Equal(messageBody.Data.Phone, dbAccount.Phone);
            Assert.Equal(EventType.AccountPhoneNumberChanged, messageBody.Type);

        }

        public static void AssertEquality(
             MessageBody<AccountEmailChangedData> messageBody,
             Account dbAccount)
        {
            Assert.Equal(messageBody.AggregateId, dbAccount.Id);
            Assert.Equal(messageBody.Sequence, dbAccount.Sequence);
            Assert.Equal(messageBody.Data.Email, dbAccount.Email);
            Assert.Equal(EventType.AccountEmailChanged, messageBody.Type);

        }

        public static void AssertEquality(
            MessageBody<AccountUpdatedData> messageBody,
            Account dbAccount,
            Account createAccount)
        {
            Assert.Equal(messageBody.AggregateId, dbAccount.Id);
            Assert.Equal(messageBody.Sequence, dbAccount.Sequence);
            Assert.Equal(messageBody.Data.Number, dbAccount.Number);

            if (!string.IsNullOrWhiteSpace(messageBody.Data.LocationId))
            {
                Assert.Equal(messageBody.Data.LocationId, dbAccount.LocationId);
            }
            else
            {
                Assert.Equal(createAccount.LocationId, dbAccount.LocationId);
            }

            Assert.Equal(createAccount.Email, dbAccount.Email);
            Assert.Equal(createAccount.Phone, dbAccount.Phone);
            Assert.Equal(createAccount.ExpiryAt, dbAccount.ExpiryAt);
            Assert.Equal(createAccount.OwnerName, dbAccount.OwnerName);
            Assert.Equal(createAccount.ConfirmedAt, dbAccount.ConfirmedAt);
        }

        public static void AssertEquality(
            MessageBody<ExpiryDateChangedData> messageBody,
            Account dbAccount,
            Account createAccount)
        {
            Assert.Equal(messageBody.AggregateId, dbAccount.Id);
            Assert.Equal(messageBody.Sequence, dbAccount.Sequence);
            Assert.Equal(messageBody.Data.ExpireAt, dbAccount.ExpiryAt);
            Assert.Equal(createAccount.Number, dbAccount.Number);
            Assert.Equal(createAccount.LocationId, dbAccount.LocationId);
            Assert.Equal(createAccount.Email, dbAccount.Email);
            Assert.Equal(createAccount.Phone, dbAccount.Phone);
            Assert.Equal(createAccount.OwnerName, dbAccount.OwnerName);
            Assert.Equal(createAccount.ConfirmedAt, dbAccount.ConfirmedAt);
        }

        public static void AssertEquality(
            MessageBody<ExpiryDateRemovedData> messageBody,
            Account dbAccount,
            Account createAccount)
        {
            Assert.Equal(messageBody.AggregateId, dbAccount.Id);
            Assert.Equal(messageBody.Sequence, dbAccount.Sequence);
            Assert.Equal(createAccount.Number, dbAccount.Number);
            Assert.Equal(createAccount.LocationId, dbAccount.LocationId);
            Assert.Equal(createAccount.Email, dbAccount.Email);
            Assert.Equal(createAccount.Phone, dbAccount.Phone);
            Assert.Equal(createAccount.OwnerName, dbAccount.OwnerName);
            Assert.Equal(createAccount.ConfirmedAt, dbAccount.ConfirmedAt);
        }

        public static void AssertEquality(
            Subscription createSubscription,
            Subscription dbSubscription,
            string accountId)
        {
            Assert.Equal(createSubscription.Id, dbSubscription.Id);
            Assert.Equal(createSubscription.Name, dbSubscription.Name);
            Assert.Equal(createSubscription.Type, dbSubscription.Type);
            Assert.Equal(accountId, dbSubscription.AccountId);
        }

        public static void AssertEquality(
            SubscriptionModel subscriptionData,
            Subscription dbSubscription,
            string accountId)
        {
            Assert.Equal(subscriptionData.Id, dbSubscription.Id);
            Assert.Equal(subscriptionData.Name, dbSubscription.Name);
            Assert.Equal(subscriptionData.Type, dbSubscription.Type);
            Assert.Equal(accountId, dbSubscription.AccountId);
        }

        public static void AssertEquality(
            MessageBody<SequenceIncrementedData> messageBody,
            Account dbAccount,
            Account createAccount)
        {
            Assert.Equal(messageBody.AggregateId, dbAccount.Id);
            Assert.Equal(messageBody.Sequence, dbAccount.Sequence);
            Assert.Equal(createAccount.Number, dbAccount.Number);
            Assert.Equal(createAccount.LocationId, dbAccount.LocationId);
            Assert.Equal(createAccount.Email, dbAccount.Email);
            Assert.Equal(createAccount.Phone, dbAccount.Phone);
            Assert.Equal(createAccount.OwnerName, dbAccount.OwnerName);
            Assert.Equal(createAccount.ExpiryAt, dbAccount.ExpiryAt);
            Assert.Equal(createAccount.ConfirmedAt, dbAccount.ConfirmedAt);
        }

        public static void AssertEquality(
            MessageBody<SubscriptionUpdatedData> messageBody,
            Subscription dbSubscription,
            Account dbAccount)
        {
            Assert.Equal(messageBody.Data.Id, dbSubscription.Id);
            Assert.Equal(messageBody.Data.Name, dbSubscription.Name);
            Assert.Equal(0, dbSubscription.StateVersion);
            Assert.Equal(messageBody.AggregateId, dbSubscription.AccountId);
            Assert.Equal(messageBody.AggregateId, dbAccount.Id);
            Assert.Equal(messageBody.Sequence, dbAccount.Sequence);
        }

        public static void AssertEquality(
            MessageBody<SubscriptionCreatedData> messageBody,
            Subscription dbSubscription,
            Account dbAccount)
        {
            Assert.Equal(messageBody.Data.Id, dbSubscription.Id);
            Assert.Equal(messageBody.Data.Name, dbSubscription.Name);
            Assert.Equal(messageBody.Data.Type, dbSubscription.Type);
            Assert.Equal(messageBody.AggregateId, dbSubscription.AccountId);
            Assert.Equal(messageBody.AggregateId, dbAccount.Id);
            Assert.Equal(messageBody.Sequence, dbAccount.Sequence);
        }

        public static void AssertEquality(WalletDto walletDto,
            Wallet wallet,
            string state,
            string operatorName = "",
            decimal averageSale = default,
            decimal totalSalesLastDay = default)
        {
            Assert.Equal(walletDto.Id, wallet.Id);
            Assert.Equal(walletDto.SubscriptionName, wallet.Subscription.Name);
            Assert.Equal(operatorName, walletDto.OperatorName);
            Assert.Equal(walletDto.State, state);
            Assert.Equal((decimal)walletDto.NetBalance, wallet.NetBalance);
            Assert.Equal((decimal)walletDto.Credit, wallet.Balance >= 0 ? wallet.Balance : 0);
            Assert.Equal((decimal)walletDto.Debit, wallet.Balance < 0 ? wallet.Balance : 0);
            Assert.Equal((decimal)walletDto.Urgent, wallet.CurrentUrgent);
            Assert.Equal((decimal)walletDto.Delay, wallet.CurrentDelayed);
            Assert.Equal(walletDto.Location, CultureHelper.Read(wallet.Subscription.Account.Location.ArabicName, wallet.Subscription.Account.Location.EnglishName));
            Assert.Equal(walletDto.ArabicLocationName, wallet.Subscription.Account.Location.ArabicName);
            Assert.Equal(walletDto.EnglishLocationName, wallet.Subscription.Account.Location.EnglishName);
            Assert.Equal(walletDto.Owner, wallet.Subscription.Account.OwnerName);
            Assert.Equal(walletDto.Email, wallet.Subscription.Account.Email);
            Assert.Equal(walletDto.PhoneNumber, wallet.Subscription.Account.Phone);
            Assert.Equal(walletDto.WalletIdentifier, WalletHelper.CreateWalletIdentifier(wallet, wallet.Subscription.Account));
            Assert.Equal(walletDto.IsNormal, wallet.NetBalance >= 0);
            Assert.Equal(walletDto.WalletRegion, wallet.Region.Code);
            Assert.Equal(walletDto.Code, wallet.Subscription.Account.Location.Code);
            Assert.Equal(state, walletDto.State);
            Assert.Equal(walletDto.AverageSales, (double)averageSale);
            Assert.Equal(walletDto.TotalSalesLastDay, (double)totalSalesLastDay);
            Assert.Equal(wallet.Subscription.Account.ConfirmedAt, walletDto.ConfirmedAt?.ToDateTime());
            Assert.Equal(wallet.LastDelayRefundDate, walletDto.LastDelayRefundDate?.ToDateTime());

            var holderName = wallet.Holder is null ? null : CultureHelper.Read(wallet.Holder.ArabicName, wallet.Holder.EnglishName);

            Assert.Equal(holderName, walletDto.HolderName);

        }

        public static void AssertEquality(CashflowFilterWalletDto walletDto,
            Wallet wallet,
            string state,
            string operatorName = "",
            decimal averageSale = default,
            decimal totalSalesLastDay = default)
        {
            Assert.Equal(walletDto.Id, wallet.Id);
            Assert.Equal(walletDto.SubscriptionName, wallet.Subscription.Name);
            Assert.Equal(walletDto.State, state);
            Assert.Equal((decimal)walletDto.NetBalance, wallet.NetBalance);
            Assert.Equal((decimal)walletDto.Credit, wallet.Balance >= 0 ? wallet.Balance : 0);
            Assert.Equal((decimal)walletDto.Debit, wallet.Balance < 0 ? wallet.Balance : 0);
            Assert.Equal((decimal)walletDto.Urgent, wallet.CurrentUrgent);
            Assert.Equal((decimal)walletDto.Delay, wallet.CurrentDelayed);
            Assert.Equal(walletDto.Location, CultureHelper.Read(wallet.Subscription.Account.Location.ArabicName, wallet.Subscription.Account.Location.EnglishName));
            Assert.Equal(walletDto.ArabicLocationName, wallet.Subscription.Account.Location.ArabicName);
            Assert.Equal(walletDto.EnglishLocationName, wallet.Subscription.Account.Location.EnglishName);
            Assert.Equal(walletDto.Owner, wallet.Subscription.Account.OwnerName);
            Assert.Equal(walletDto.Email, wallet.Subscription.Account.Email);
            Assert.Equal(walletDto.PhoneNumber, wallet.Subscription.Account.Phone);
            Assert.Equal(walletDto.WalletIdentifier, WalletHelper.CreateWalletIdentifier(wallet, wallet.Subscription.Account));
            Assert.Equal(walletDto.IsNormal, wallet.NetBalance >= 0);
            Assert.Equal(walletDto.WalletRegion, wallet.Region.Code);
            Assert.Equal(walletDto.Code, wallet.Subscription.Account.Location.Code);
            Assert.Equal(state, walletDto.State);
            Assert.Equal(walletDto.AverageSales, (double)averageSale);
            Assert.Equal(walletDto.TotalSalesLastDay, (double)totalSalesLastDay);
            Assert.Equal(wallet.Subscription.Account.ConfirmedAt, walletDto.ConfirmedAt?.ToDateTime());
            Assert.Equal(wallet.LastDelayRefundDate, walletDto.LastDelayRefundDate?.ToDateTime());

            var holderName = wallet.Holder is null ? null : CultureHelper.Read(wallet.Holder.ArabicName, wallet.Holder.EnglishName);

            Assert.Equal(holderName, walletDto.HolderName);

            if (wallet.Subscription.Account.ExpiryAt.HasValue && wallet.Subscription.Account.ExpiryAt.Value > DateTime.UtcNow)
            {
                var remaining = wallet.Subscription.Account.ExpiryAt.Value - DateTime.UtcNow;
                var expected = $"{(int)remaining.TotalHours:D2}h {remaining.Minutes:D2}m";
                Assert.Equal(expected, walletDto.RemainingTime);
            }
            else
            {
                Assert.Null(walletDto.RemainingTime);
            }
        }

        public static void AssertEquality(Grpc.Protos.Consumer.WalletDto walletDto,
                                          Wallet dbWallet,
                                          string operatorName = "",
                                          decimal averageSale = 0,
                                          decimal positiveBalanceRate = 0,
                                          decimal totalSalesLastDay = 0)
        {
            Assert.Equal(dbWallet.Id, walletDto.Id);
            Assert.Equal(dbWallet.Subscription.Name, walletDto.SubscriptionName);
            Assert.Equal(dbWallet.CurrentUrgent, (decimal)walletDto.Urgent);
            Assert.Equal(dbWallet.CurrentDelayed, (decimal)walletDto.Delay);
            Assert.Equal(dbWallet.Subscription.Account.Phone, walletDto.PhoneNumber);

            Assert.Equal(dbWallet.Subscription.Account.Location.EnglishName, walletDto.AccountLocation);
            Assert.Equal(operatorName, walletDto.OperatorName);
            Assert.Equal(dbWallet.Balance, (decimal)walletDto.Balance);
            Assert.Equal(dbWallet.CurrentDelayed, (decimal)walletDto.Delay);
            Assert.Equal(dbWallet.CurrentUrgent, (decimal)walletDto.Urgent);
            Assert.Equal(dbWallet.Region.EnglishName, walletDto.WalletRegion);
            Assert.Equal(averageSale, (decimal)walletDto.AverageSales);
            Assert.Equal(positiveBalanceRate, (decimal)walletDto.PositiveBalanceRate);
            Assert.Equal(dbWallet.Subscription.Account.ConfirmedAt, walletDto.ConfirmedAt?.ToDateTime());
            Assert.Equal(dbWallet.LastDelayRefundDate, walletDto.LastDelayRefundDate?.ToDateTime());
            Assert.Equal(totalSalesLastDay, (decimal)walletDto.TotalSalesLastDay);

            if (dbWallet.Subscription.Account.ExpiryAt.HasValue && dbWallet.Subscription.Account.ExpiryAt.Value > DateTime.UtcNow)
            {
                var remaining = dbWallet.Subscription.Account.ExpiryAt.Value - DateTime.UtcNow;
                var expected = $"{(int)remaining.TotalHours:D2}h {remaining.Minutes:D2}m";
                Assert.Equal(expected, walletDto.RemainingTime);
            }
            else
            {
                Assert.Null(walletDto.RemainingTime);
            }
        }

        public static void AssertEquality(
            MessageBody<AccountConfirmedData> messageBody,
            Account dbAccount,
            Account createdAccount)
        {
            Assert.Equal(messageBody.AggregateId, dbAccount.Id);
            Assert.Equal(createdAccount.Id, dbAccount.Id);
            Assert.Equal(messageBody.Sequence, dbAccount.Sequence);
            Assert.Equal(messageBody.Data.Number, dbAccount.Number);
            Assert.Equal(createdAccount.Email, dbAccount.Email);
            Assert.Equal(messageBody.Data.Phone, dbAccount.Phone);
            Assert.Equal(createdAccount.LocationId, dbAccount.LocationId);
            Assert.Equal(messageBody.Data.ConfirmedAt, dbAccount.ConfirmedAt);
        }

        internal static void AssertPositiveBalanceRateEquality(WalletDto resFirstWallet, decimal averageSale)
        {
            var positivesBalanceRate = (Math.Truncate(1000 * ((decimal)resFirstWallet.NetBalance / averageSale)) / 1000);

            Assert.Equal(positivesBalanceRate, (decimal)resFirstWallet.PositveBalanceRate);
        }

        internal static void AssertPositiveBalanceRateEquality(CashflowFilterWalletDto resFirstWallet, decimal averageSale)
        {
            var positivesBalanceRate = (Math.Truncate(1000 * ((decimal)resFirstWallet.NetBalance / averageSale)) / 1000);

            Assert.Equal(positivesBalanceRate, (decimal)resFirstWallet.PositveBalanceRate);
        }
    }
}
