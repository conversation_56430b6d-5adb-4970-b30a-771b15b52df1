﻿using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.Models
{
    public class NotificationBody<T> : INotificationBody<T>, IRequest<bool>
    {
        public string OperatorSubscriptionId { get; set; }
        public int StateVersion { get; set; }
        public T Data { get; set; }
        public NotificationType Type { get; set; }
        public int DataVersion { get; set; }
        public DateTime DateTime { get; set; }
        public int BuildVersion { get; set; }
    }
}
