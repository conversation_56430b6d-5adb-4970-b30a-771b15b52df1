﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using MediatR;

namespace Anis.Cashflow.Report.Application.Features.Management.GetHolders;

public class GetHoldersHandler : IRequestHandler<GetHoldersQuery, List<GetHoldersDTO>>
{
    private readonly IUnitOfWork _unitOfWork;

    public GetHoldersHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<List<GetHoldersDTO>> Handle(GetHoldersQuery request, CancellationToken cancellationToken)
    {
        return await _unitOfWork.Holders.GetHoldersDTOAsync(cancellationToken);
    }
}
