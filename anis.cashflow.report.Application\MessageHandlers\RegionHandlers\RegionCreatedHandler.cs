﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.RegionHandlers
{
    public class RegionCreatedHandler : IRequestHandler<MessageBody<RegionCreatedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public RegionCreatedHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> Handle(MessageBody<RegionCreatedData> request, CancellationToken cancellationToken)
        {
            if (await _unitOfWork.Regions.AnyAsync(request.AggregateId))
                return true;

            var newRegion = new Region(request);

            await _unitOfWork.Regions.AddAsync(newRegion);

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}