﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Contracts.Services.BaseService;
using Anis.Cashflow.Report.Application.Contracts.Services.GrpcService;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Models.Configs;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Persistence.Repositories;
using Anis.Cashflow.Report.Infra.Services.BaseService;
using Anis.Cashflow.Report.Infra.Services.GrpcServices;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Options;
using Azure.Messaging.ServiceBus;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Anis.Cashflow.Report.Infra;

public static class InfraContainer
{
    public static IServiceCollection AddInfraServices(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddConfigurations(configuration);

        services.RunDatabase(configuration);

        services.AddServiceBus(configuration);

        services.AddBaseServices();

        services.ListenToEvents();

        return services;
    }

    private static void AddConfigurations(this IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<ServiceBusOptions>(configuration.GetSection(ServiceBusOptions.Context));

        var configModel = new ConfigModel();

        configuration.Bind(configModel.Context, configModel);

        services.AddSingleton(configModel);
    }

    private static void RunDatabase(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddDbContext<AppDbContext>(options =>
            options.UseSqlServer(configuration.GetConnectionString("AppConnectionString")));

        services.AddHostedService<DatabaseBuilder>();

        services.AddScoped<IUnitOfWork, UnitOfWork>();
    }

    private static void AddBaseServices(this IServiceCollection services)
    {
        services.AddScoped<IRetryCallerService, RetryCallerService>();

        services.AddScoped<IGrpcClientService, GrpcClientService>();
    }

    private static void AddServiceBus(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton(s => new AccountsServiceBusConnection(new ServiceBusClient(configuration["ServiceBus:AccountsServiceBusConnectionString"])));

        services.AddSingleton(s => new RegionsServiceBusConnection(new ServiceBusClient(configuration["ServiceBus:RegionsServiceBusConnectionString"])));

        services.AddSingleton(s => new WalletsServiceBusConnection(new ServiceBusClient(configuration["ServiceBus:WalletsServiceBusConnectionString"])));

        services.AddSingleton(s => new HoldersServiceBusConnection(new ServiceBusClient(configuration["ServiceBus:HoldersServiceBusConnectionString"])));

        services.AddSingleton(s => new SubscriptionWalletServiceBusConnection(new ServiceBusClient(configuration["ServiceBus:SubscriptionWalletServiceBusConnectionString"])));
    }

    private static void ListenToEvents(this IServiceCollection services)
    {
        services.AddSingleton<RegionEventsListener>();

        services.AddHostedService(p => p.GetRequiredService<RegionEventsListener>());

        services.AddSingleton<AccountEventsListener>();

        services.AddHostedService(p => p.GetRequiredService<AccountEventsListener>());

        services.AddSingleton<WalletEventsListener>();

        services.AddHostedService(p => p.GetRequiredService<WalletEventsListener>());

        services.AddSingleton<HolderEventsListener>();

        services.AddHostedService(p => p.GetRequiredService<HolderEventsListener>());

        services.AddSingleton<SubscriptionWalletListener>();

        services.AddHostedService(p => p.GetRequiredService<SubscriptionWalletListener>());
    }
}
