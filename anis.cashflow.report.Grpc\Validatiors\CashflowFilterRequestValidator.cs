﻿using Anis.Cashflow.Report.Grpc.Protos.Managmant;
using Anis.Cashflow.Report.Grpc.Resources;
using FluentValidation;

namespace Anis.Cashflow.Report.Grpc.Validatiors
{
    public class CashflowFilterRequestValidator : AbstractValidator<CashflowFilterRequest>
    {
        public CashflowFilterRequestValidator()
        {
            RuleFor(r => r).Must(ValidateSpecificHolder)
                                    .WithName(Titles.HoldersId)
                                    .WithMessage(Phrases.HolderMustBeSelected);
        }

        private bool ValidateSpecificHolder(CashflowFilterRequest request)
        {
            if (request.CashAndHoldersFilter != CashAndHoldersFilter.SpecificHolder)
                return true;

            return Guid.TryParse(request.HolderId, out _) && request.HolderId != Guid.Empty.ToString();
        }
    }
}
