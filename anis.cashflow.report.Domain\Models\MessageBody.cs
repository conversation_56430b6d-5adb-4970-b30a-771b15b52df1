﻿using MediatR;

namespace Anis.Cashflow.Report.Domain.Models
{
    public class MessageBody<T> : IRequest<bool>
    {
        public string AggregateId { get; set; }
        public long Sequence { get; set; }
        public string UserId { get; set; }
        public T Data { get; set; }
        public string Type { get; set; }
        public DateTime DateTime { get; set; }
        public int? Version { get; set; }
    }
}
