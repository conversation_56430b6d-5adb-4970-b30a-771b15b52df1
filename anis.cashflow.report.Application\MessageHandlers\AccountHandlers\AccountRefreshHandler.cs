﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.AccountHandlers;

public class AccountRefreshHandler : IRequestHandler<AccountRefreshModel, Unit>
{
    private readonly IUnitOfWork _unitOfWork;

    public AccountRefreshHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    public async Task<Unit> Handle(AccountRefreshModel model, CancellationToken cancellationToken)
    {
        var account = await _unitOfWork.Accounts.FindAsync(model.AccountId);

        if (account is not null)
        {
            account.Modify(model.ConfirmedAt);
        }

        await _unitOfWork.SaveChangesAsync();

        return Unit.Value;
    }
}
