﻿using Anis.Cashflow.Report.Domain.Entities;

namespace Anis.Cashflow.Report.Test.Fakers.Regions
{
    public class RegionFaker : PrivateFaker<Region>
    {
        public RegionFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.Id, f => f.Random.Guid().ToString());
            RuleFor(r => r.ArabicName, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.EnglishName, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.Code, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.Sequence, 1);
        }
    }
}
