﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Contracts.Services.BaseService;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Grpc.Extensions;
using Anis.Cashflow.Report.Grpc.Protos.Client.SpecialQueries;
using Anis.Cashflow.Report.Grpc.Protos.Clients;
using Anis.Cashflow.Report.Grpc.Protos.Rebuild;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using MediatR;

namespace Anis.Cashflow.Report.Grpc.Services;

public class RebuildService : CashflowRebuild.CashflowRebuildBase
{
    private readonly EventsHistory.EventsHistoryClient _eventHistoryClient;
    private readonly SpecialQueries.SpecialQueriesClient _specialQueriesClient;
    private readonly IMediator _mediator;
    private readonly ILogger<RebuildService> _logger;
    private readonly IServiceProvider _provider;
    private readonly IRetryCallerService _retryCallerService;

    public RebuildService(
        EventsHistory.EventsHistoryClient ecomClient,
        SpecialQueries.SpecialQueriesClient specialQueriesClient,
        IMediator mediator,
        ILogger<RebuildService> logger,
        IServiceProvider provider,
        IRetryCallerService retryCallerService
)
    {
        _eventHistoryClient = ecomClient;
        _specialQueriesClient = specialQueriesClient;
        _mediator = mediator;
        _logger = logger;
        _provider = provider;
        _retryCallerService = retryCallerService;
    }

    public override async Task<Empty> RebuildAccounts(Empty request, ServerCallContext context)
    {
        _logger.LogCritical("-------------- Account Rebuild Started -------------------");

        try
        {
            for (var i = 1; i > 0; i++)
            {
                var response = await _eventHistoryClient.GetAllAccountsAsync(new Request()
                {
                    Page = i,
                    Size = 1000
                });

                if (response.Messages.Count > 0)
                {
                    var groupedAccountEvents = response.Messages.GroupBy(c => c.AggregateId);

                    await Task.WhenAll(groupedAccountEvents.Select(x => HandelAccountResponseAsync(x)));
                }
                else
                    break;

                _logger.LogWarning("Rebuild Accounts size {size} ,with page {page}", response.Size, i);
            }

            _logger.LogCritical("------------- Done Rebuilding Accounts -------------------");
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "Account rebuild failed ...");
        }

        return new Empty();
    }

    private async Task HandelAccountResponseAsync(IEnumerable<EventMessage> messages)
    {
        using var scope = _provider.CreateScope();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var messageBodies = messages.Select(x => x.ToMessageBody()).ToList();

        await mediator.Send(new AccountRebuildModel(Messages: messageBodies));
    }

    public async override Task<Empty> RebuildWallets(Empty request, ServerCallContext context)
    {
        for (var i = 1; i > 0; i++)
        {
            var response = await _eventHistoryClient.GetWalletsAsync(new Request()
            {
                Page = i,
                Size = 100
            });

            if (response.Messages.Count > 0)
            {
                await HandelWalletResponseAsync(response);
            }
            else
                break;
        }

        return new Empty();
    }

    private async Task HandelWalletResponseAsync(Response response)
    {
        foreach (var @event in response.Messages)
        {
            switch (@event.Type)
            {
                case EventType.WalletCreated:
                    await _mediator.Send(@event.ToMessageBody<WalletCreatedData>());
                    break;
            }
        }
    }

    public async override Task<Empty> RebuildGateWayRegions(Empty request, ServerCallContext context)
    {
        _logger.LogWarning("------------------- Rebuild regions started --------------------");

        for (var i = 1; i > 0; i++)
        {
            var response = await _eventHistoryClient.GetAllRegionsAsync(new Request()
            {
                Page = i,
                Size = 100
            });

            if (response.Messages.Count > 0)
            {
                await HandelResponseAsync(response);
            }
            else
                break;
        }

        _logger.LogWarning("------------------- Rebuild regions end --------------------");

        return new Empty();
    }

    private async Task HandelResponseAsync(Response response)
    {
        foreach (var @event in response.Messages)
        {
            switch (@event.Type)
            {
                case EventType.RegionCreated:
                    await _mediator.Send(@event.ToMessageBody<RegionCreatedData>());
                    break;
                case EventType.RegionUpdated:
                    await _mediator.Send(@event.ToMessageBody<RegionUpdatedData>());
                    break;
                case EventType.RegionDeleted:
                    await _mediator.Send(@event.ToMessageBody<RegionDeletedData>());
                    break;
                case EventType.RegionCodeChanged:
                    await _mediator.Send(@event.ToMessageBody<RegionCodeChangedData>());
                    break;
                default:
                    throw new ArgumentOutOfRangeException($"{@event.Type}", $"Event type out of range in rebuild service => {@event}");
            }
        }
    }

    public async override Task<Empty> RebuildTransactions(RebuildTransactionsRequest request, ServerCallContext context)
    {
        _logger.LogWarning("------------------- Rebuild transations started --------------------");

        await RebuildTransactionsAsync(1, request.PageSize);

        _logger.LogWarning("------------------- Rebuild transations end --------------------");

        return new Empty();
    }

    private async Task RebuildTransactionsAsync(int currentPage, int pageSize = 1000)
    {
        using var scope = _provider.CreateScope();

        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        for (var i = currentPage; i > 0; i++)
        {
            _logger.LogWarning("excute rebuild transaction , with  page {startPage}", i);

            var response = await _eventHistoryClient.GetTransactionsAsync(new Request()
            {
                Page = i,
                Size = pageSize,
            });

            if (response.Messages.Count == 0)
                continue;

            var walletEvents = new List<MessageBody<string>>();

            foreach (var message in response.Messages)
            {
                var @event = message.ToMessageBody();

                walletEvents.Add(@event);
            }

            var walletGroups = walletEvents.GroupBy(c => c.AggregateId).ToList();

            await Task.WhenAll(walletGroups.Select(g => HandleGroup(g)));
        }

        return;
    }

    private async Task HandleGroup(IEnumerable<MessageBody<string>> rebuildModeles)
    {
        await _retryCallerService.CallAsync(async () =>
        {
            using var scope = _provider.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            return await mediator.Send(new TransactionRebuildModel<string> { Models = rebuildModeles });
        });
    }

    public async override Task<Empty> RebuildSnapshotWallets(RebuildWalletsRequest request, ServerCallContext context)
    {
        _logger.LogWarning("------------------- Rebuild snapshot wallets started --------------------");

        var walletRequest = new GetWalletsSnapshotRequest
        {
            ResultNumber = request.ResultNumber,
            DateTime = DateTime.UtcNow.AddDays(-1).ToTimestamp(),
            IncludeDetailedSummation = true
        };

        using (var call = _specialQueriesClient.GetWalletsSnapshot(walletRequest))
        {
            await foreach (var response in call.ResponseStream.ReadAllAsync())
            {
                var model = response.ToModel();

                await _mediator.Send(model);

                _logger.LogCritical("Result number : {ResultNumber}", response.ResultNumber);
            }
        }

        _logger.LogWarning("------------------- Rebuild snapshot wallets end --------------------");

        return new Empty();
    }

    public async override Task<Empty> RefresheAccounts(Empty request, ServerCallContext context)
    {
        using (var call = _specialQueriesClient.GetAccountSnapshot(new AccountSnapshotRequest { ResultNumber = 0 }))
        {
            await foreach (var response in call.ResponseStream.ReadAllAsync())
            {
                var model = response.ToRefreshModel();

                await _mediator.Send(model);
            }
        }

        return new Empty();
    }
}