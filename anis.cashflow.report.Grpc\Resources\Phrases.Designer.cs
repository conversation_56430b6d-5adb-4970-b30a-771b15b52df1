﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Anis.Cashflow.Report.Grpc.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Phrases {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Phrases() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Anis.Cashflow.Report.Grpc.Resources.Phrases", typeof(Phrases).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب أن يكون عدد الأيام يساوي الصفر أو أكبر.
        /// </summary>
        public static string DaysCountMustBeEqualZeroOrGreater {
            get {
                return ResourceManager.GetString("DaysCountMustBeEqualZeroOrGreater", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to يجب اختيار هولدر.
        /// </summary>
        public static string HolderMustBeSelected {
            get {
                return ResourceManager.GetString("HolderMustBeSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to معرف المحفظة خاطئ.
        /// </summary>
        public static string InvalidWalledId {
            get {
                return ResourceManager.GetString("InvalidWalledId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to لم يتم ارسال معرف المنطقة.
        /// </summary>
        public static string LocationIdNotSend {
            get {
                return ResourceManager.GetString("LocationIdNotSend", resourceCulture);
            }
        }
    }
}
