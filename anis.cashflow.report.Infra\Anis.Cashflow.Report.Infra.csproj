﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Services\GrpcServices\Protos\daily_Location_sales.proto" />
    <None Remove="Services\GrpcServices\Protos\daily_wallet_sales.proto" />
    <None Remove="Services\GrpcServices\Protos\queries.proto" />
  </ItemGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Messaging.ServiceBus" Version="7.11.0" />
		<PackageReference Include="Google.Protobuf" Version="3.21.8" />
		<PackageReference Include="Grpc.Core.Api" Version="2.49.0" />
		<PackageReference Include="Grpc.Net.Client" Version="2.49.0" />
		<PackageReference Include="Grpc.Tools" Version="2.50.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.10" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
		<PackageReference Include="Serilog" Version="2.12.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="6.0.1" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="3.4.0" />
		<PackageReference Include="Serilog.Sinks.Seq" Version="5.2.1" />
	</ItemGroup>

	<ItemGroup>
	  <ProjectReference Include="..\anis.cashflow.report.Application\Anis.Cashflow.Report.Application.csproj" />
	</ItemGroup>

	<ItemGroup>
	  <Protobuf Include="Services\GrpcServices\Protos\daily_Location_sales.proto" GrpcServices="Client" />
	  <Protobuf Include="Services\GrpcServices\Protos\daily_wallet_sales.proto" GrpcServices="Client" />
	</ItemGroup>

</Project>
