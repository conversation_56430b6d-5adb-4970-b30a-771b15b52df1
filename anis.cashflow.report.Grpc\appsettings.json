{"Serilog": {"MinimumLevel": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}, "SeqUrl": "", "AppName": "Anis.CashflowReport", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}]}, "ClientUrls": {"GateWayEventsHistoryClient": "http://localhost:5004", "DailyLocationSaleClient": "http://localhost:5164", "DailyWalletSaleClient": "http://localhost:5001", "HoldersClient": "http://localhost:5001", "NotificationOperatorBusinessLinkClient": "http://localhost:5001"}, "ServiceBus": {"AccountsServiceBusConnectionString": "secret", "RegionsServiceBusConnectionString": "secret", "WalletsServiceBusConnectionString": "secret", "HoldersServiceBusConnectionString": "secret", "SubscriptionWalletServiceBusConnectionString": "secret", "AccountTopic": "ecom-cards-accounts-development", "AccountSubscription": "anis-cashflow-report-v2-development", "WalletTopic": "ecom-cards-wallets-development", "WalletSubscription": "anis-cashflow-report-v2-development", "RegionTopic": "ecom-cards-development", "RegionSubscription": "anis-cashflow-report-v2-development", "HolderTopic": "holder-development", "HolderSubscription": "holder-cashflow-report-development", "IsDeadLetterEnabled": false}, "AllowedHosts": "*", "Kestrel": {"EndpointDefaults": {"Protocols": "Http2"}}, "Config": {"DaysCount": "7", "ResetDataBase": "True"}}