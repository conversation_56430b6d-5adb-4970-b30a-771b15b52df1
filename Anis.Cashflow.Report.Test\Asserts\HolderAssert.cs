﻿using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Holders.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Helpers;
using Anis.Cashflow.Report.Test.Grpc.Protos.Managmant;

namespace Anis.Cashflow.Report.Test.Asserts
{
    public static class HolderAssert
    {
        public static void AssertEquality(MessageBody<HolderCreatedData> message, Holder dbHolder)
        {
            Assert.NotNull(dbHolder);

            Assert.Equal(message.AggregateId, dbHolder.Id);
            Assert.Equal(message.Sequence, dbHolder.Sequence);
            Assert.Equal(message.Data.ArabicName, dbHolder.ArabicName);
            Assert.Equal(message.Data.EnglishName, dbHolder.EnglishName);
        }

        public static void AssertEquality(MessageBody<NameChangedData> message, Holder dbHolder, int? sequence = null)
        {
            Assert.NotNull(dbHolder);

            Assert.Equal(message.AggregateId, dbHolder.Id);
            Assert.Equal(sequence?? message.Sequence, dbHolder.Sequence);
            Assert.Equal(message.Data.ArabicName, dbHolder.ArabicName);
            Assert.Equal(message.Data.EnglishName, dbHolder.EnglishName);
        }

        public static void AssertEquality(Holder holderFaker, Holder dbHolder,
                                          int? sequence = null)
        {
            Assert.NotNull(dbHolder);

            Assert.Equal(holderFaker.Id, dbHolder.Id);
            Assert.Equal(sequence ?? holderFaker.Sequence, dbHolder.Sequence);
            Assert.Equal(holderFaker.ArabicName, dbHolder.ArabicName);
            Assert.Equal(holderFaker.EnglishName, dbHolder.EnglishName);
        }

        public static void AssertEquality(GetHoldersResponse response, List<Holder> dbHolders)
        {
            Assert.NotNull(response);

            Assert.NotEmpty(response.Holders);

            Assert.Equal(dbHolders.Count, response.Holders.Count);

            foreach (var holder in response.Holders)
            {
                var dbHolder = dbHolders.Single(h => h.Id == holder.Id);

                Assert.Equal(dbHolder.Id, holder.Id);

                Assert.Equal(CultureHelper.Read(dbHolder.ArabicName, dbHolder.EnglishName), holder.Name);
            }
        }
    }
}
