﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Fakers.SubscriptionWallets;
using Anis.Cashflow.Report.Test.Live.OperatorBusinessLinkDemo;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.OperatorBusinessLinkNotifications
{
    public class BusinessRemovedTest : TestBase
    {
        public BusinessRemovedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task BusinessRemoved_WhenReceivedValidData_ReturnTrueAndHandleNotification()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var channel = GrpcChannel.ForAddress(Address.OperatorBusinessLink);

            var grpcClient = new OperatorToBusinessLinkDemoNotifications.OperatorToBusinessLinkDemoNotificationsClient(channel);

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createOperatorAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var createOperatorSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createOperatorAccount)
                .RuleFor(f => f.StateVersion, 3)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var createOperatorWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createOperatorSubscription)
                .Generate();

            var subscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(f => f.Subscription, createOperatorSubscription)
                .RuleFor(f => f.Wallet, creatWallet)
                .Generate();

            await context.SubscriptionWallets.AddAsync(subscriptionWallet);
            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var request = new NotifyRequest
            {
                BusinessSubscriptionId = createSubscription.Id,
                BusinessWalletId = creatWallet.Id,
                NotificationType = NotificationType.BusinessRemoved,
                OperatorWalletId = createOperatorWallet.Id,
                OperatorSubscriptionId = createOperatorSubscription.Id,
                StateVersion = 4,
            };

            await grpcClient.NotifyAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<SubscriptionWalletListener>().CloseProccessorAsync();

            var dbSubscriptionWallet = await context.SubscriptionWallets.SingleOrDefaultAsync();

            Assert.Null(dbSubscriptionWallet);
        }
    }
}
