﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Wallets.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Newtonsoft.Json;
using Xunit.Abstractions;
using TransactionType = Anis.Cashflow.Report.Infra.Services.Const.TransactionType;

namespace Anis.Cashflow.Report.Test.Handlers.WalletHandlers
{
    public class RebuildCreateTransactionTest : TestBase
    {
        public RebuildCreateTransactionTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task Update_RebuildCreateTransactionWithWalletCreated_SuccessHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
             .Generate();

            var createAccount = new AccountFaker()
             .RuleFor(f => f.LocationId, createRegion.Id)
             .Generate();

            var createSubscription = new SubscriptionFaker()
             .RuleFor(f => f.AccountId, createAccount.Id)
             .Generate();

            var creatWallet = new WalletFaker()
            .RuleFor(f => f.SubscriptionId, createSubscription.Id)
            .RuleFor(f => f.RegionId, createRegion.Id)
            .RuleFor(f => f.CurrentDelayed, 0)
            .RuleFor(f => f.CurrentUrgent, 0)
            .RuleFor(f => f.NetBalance, 0)
            .RuleFor(f => f.Balance, 0)
            .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var messages = new List<MessageBody<string>>();

            var firstTransactionCreated = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Debit, 1000)
             .RuleFor(f => f.Value, 1000)
             .RuleFor(f => f.Type, TransactionType.Transfer)
             .Generate();

            var secTransactionCreated = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Debit, 500)
             .RuleFor(f => f.Value, 500)
             .RuleFor(f => f.Type, TransactionType.UrgentTopUp)
             .Generate();

            var thirdTransactionCreated = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Credit, 100)
             .RuleFor(f => f.Value, -100)
             .RuleFor(f => f.Type, TransactionType.RefundUrgentTopUp)
             .Generate();

            var fifthTransactionCreated = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Debit, 300)
             .RuleFor(f => f.Value, 300)
             .RuleFor(f => f.Type, TransactionType.DelayedTopUp)
             .Generate();

            var sixthTransactionCreated = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Credit, 200)
             .RuleFor(f => f.Value, -200)
             .RuleFor(f => f.Type, TransactionType.RefundDelayedTopUp)
             .Generate();

            var firstTransactionData = JsonConvert.SerializeObject(firstTransactionCreated);
            var firstMessageBody = new MessageBody<string>
            {
                AggregateId = creatWallet.Id,
                Sequence = 2,
                Type = EventType.TransactionCreated,
                Data = firstTransactionData,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var secTransactionData = JsonConvert.SerializeObject(secTransactionCreated);
            var secMessageBody = new MessageBody<string>
            {
                AggregateId = creatWallet.Id,
                Sequence = 3,
                Type = EventType.TransactionCreated,
                Data = secTransactionData,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var thirdTransactionData = JsonConvert.SerializeObject(thirdTransactionCreated);
            var thirdMessageBody = new MessageBody<string>
            {
                AggregateId = creatWallet.Id,
                Sequence = 4,
                Type = EventType.TransactionCreated,
                Data = thirdTransactionData,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var fourthMessageBody = new MessageBody<string>
            {
                AggregateId = creatWallet.Id,
                Sequence = 5,
                Type = EventType.AllowedDebtDisabled,
                Data = "",
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var fifthTransactionData = JsonConvert.SerializeObject(fifthTransactionCreated);
            var fifthMessageBody = new MessageBody<string>
            {
                AggregateId = creatWallet.Id,
                Sequence = 6,
                Type = EventType.TransactionCreated,
                Data = fifthTransactionData,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var sixthTransactionData = JsonConvert.SerializeObject(sixthTransactionCreated);
            var sixthMessageBody = new MessageBody<string>
            {
                AggregateId = creatWallet.Id,
                Sequence = 7,
                Type = EventType.TransactionCreated,
                Data = sixthTransactionData,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            messages.Add(firstMessageBody);
            messages.Add(secMessageBody);
            messages.Add(thirdMessageBody);
            messages.Add(fourthMessageBody);
            messages.Add(fifthMessageBody);
            messages.Add(sixthMessageBody);

            var model = new TransactionRebuildModel<string>
            {
                Models = messages
            };

            var isHandled = await mediator.Send(model);

            var dbWallet = await context.Wallets.SingleAsync();

            //assert
            Assert.NotNull(dbWallet);

            WalletAssert.AssertEquality(creatWallet,
                                        dbWallet,
                                        sequence: 7,
                                        balance: 1500m,
                                        CurrentUrgent: 400m,
                                        currentDelayed: 100m);
        }

        [Fact]
        public async Task Update_RebuildCreateTransactionWithWalletCreatedWithValues_SuccessHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
             .Generate();

            var createAccount = new AccountFaker()
             .RuleFor(f => f.LocationId, createRegion.Id)
             .Generate();

            var createSubscription = new SubscriptionFaker()
             .RuleFor(f => f.AccountId, createAccount.Id)
             .Generate();

            var creatWallet = new WalletFaker()
            .RuleFor(f => f.SubscriptionId, createSubscription.Id)
            .RuleFor(f => f.RegionId, createRegion.Id)
            .RuleFor(f => f.CurrentDelayed, 100)
            .RuleFor(f => f.CurrentUrgent, 200)
            .RuleFor(f => f.NetBalance, 500)
            .RuleFor(f => f.Balance, 800)
            .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var messages = new List<MessageBody<string>>();

            var firstTransactionCreated = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Debit, 1000)
             .RuleFor(f => f.Value, 1000)
             .RuleFor(f => f.Type, TransactionType.Transfer)
             .Generate();

            var secTransactionCreated = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Debit, 500)
             .RuleFor(f => f.Value, 500)
             .RuleFor(f => f.Type, TransactionType.UrgentTopUp)
             .Generate();

            var thirdTransactionCreated = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Credit, 100)
             .RuleFor(f => f.Value, -100)
             .RuleFor(f => f.Type, TransactionType.RefundUrgentTopUp)
             .Generate();

            var fifthTransactionCreated = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Debit, 300)
             .RuleFor(f => f.Value, 300)
             .RuleFor(f => f.Type, TransactionType.DelayedTopUp)
             .Generate();

            var sixthTransactionCreated = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Credit, 200)
             .RuleFor(f => f.Value, -200)
             .RuleFor(f => f.Type, TransactionType.RefundDelayedTopUp)
             .Generate();

            var firstTransactionData = JsonConvert.SerializeObject(firstTransactionCreated);
            var firstMessageBody = new MessageBody<string>
            {
                AggregateId = creatWallet.Id,
                Sequence = 2,
                Type = EventType.TransactionCreated,
                Data = firstTransactionData,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var secTransactionData = JsonConvert.SerializeObject(secTransactionCreated);
            var secMessageBody = new MessageBody<string>
            {
                AggregateId = creatWallet.Id,
                Sequence = 3,
                Type = EventType.TransactionCreated,
                Data = secTransactionData,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var thirdTransactionData = JsonConvert.SerializeObject(thirdTransactionCreated);
            var thirdMessageBody = new MessageBody<string>
            {
                AggregateId = creatWallet.Id,
                Sequence = 4,
                Type = EventType.TransactionCreated,
                Data = thirdTransactionData,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var fourthMessageBody = new MessageBody<string>
            {
                AggregateId = creatWallet.Id,
                Sequence = 5,
                Type = EventType.AllowedDebtDisabled,
                Data = "",
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var fifthTransactionData = JsonConvert.SerializeObject(fifthTransactionCreated);
            var fifthMessageBody = new MessageBody<string>
            {
                AggregateId = creatWallet.Id,
                Sequence = 6,
                Type = EventType.TransactionCreated,
                Data = fifthTransactionData,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            messages.Add(firstMessageBody);
            messages.Add(secMessageBody);
            messages.Add(thirdMessageBody);
            messages.Add(fourthMessageBody);
            messages.Add(fifthMessageBody);

            var model = new TransactionRebuildModel<string>
            {
                Models = messages
            };

            var isHandled = await mediator.Send(model);

            var dbWallet = await context.Wallets.SingleAsync();

            //assert
            Assert.NotNull(dbWallet);

            WalletAssert.AssertEquality(creatWallet,
                                        dbWallet,
                                        sequence: 6,
                                        balance: 2500m,
                                        CurrentUrgent: 600m,
                                        currentDelayed: 400m);
        }
    }
}
