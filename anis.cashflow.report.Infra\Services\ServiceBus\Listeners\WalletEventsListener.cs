﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Domain.Models.Configs;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Options;
using Azure.Messaging.ServiceBus;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Serilog.Context;
using Serilog.Core.Enrichers;
using System.Text;

namespace Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners
{
    public class WalletEventsListener : IHostedService
    {
        private readonly IServiceProvider _provider;
        private readonly ILogger<WalletEventsListener> _logger;
        private readonly ServiceBusSessionProcessor? _processor;
        private readonly ServiceBusProcessor? _deadLetterProcessor;
        private readonly bool _isDeadLetterEnabled;

        public WalletEventsListener(
            IServiceProvider provider,
            ILogger<WalletEventsListener> logger,
            WalletsServiceBusConnection client,
            IOptions<ServiceBusOptions> serviceBusOptions
        )
        {
            _provider = provider;
            _logger = logger;

            _isDeadLetterEnabled = serviceBusOptions.Value.IsDeadLetterEnabled;

            if (!string.IsNullOrWhiteSpace(serviceBusOptions.Value.WalletTopic))
            {
                _processor = client.Client.CreateSessionProcessor(serviceBusOptions.Value.WalletTopic, serviceBusOptions.Value.WalletSubscription, new ServiceBusSessionProcessorOptions()
                {
                    PrefetchCount = 1,
                    MaxConcurrentCallsPerSession = 1,
                    MaxConcurrentSessions = 1000,
                    AutoCompleteMessages = false,
                });

                _processor.ProcessMessageAsync += Processor_ProcessMessageAsync;
                _processor.ProcessErrorAsync += Processor_ProcessErrorAsync;

                //if (serviceBusOptions.Value.IsDeadLetterEnabled)
                //{

                _deadLetterProcessor = client.Client.CreateProcessor(serviceBusOptions.Value.WalletTopic,
                    serviceBusOptions.Value.WalletSubscription, new ServiceBusProcessorOptions()
                    {
                        PrefetchCount = 1,
                        AutoCompleteMessages = false,
                        MaxConcurrentCalls = 1000,
                        SubQueue = SubQueue.DeadLetter,
                    });

                _deadLetterProcessor.ProcessMessageAsync += DeadLetterProcessor_ProcessMessageAsync;
                _deadLetterProcessor.ProcessErrorAsync += DeadLetterProcessor_ProcessErrorAsync;
                //}
            }
        }

        private async Task Processor_ProcessMessageAsync(ProcessSessionMessageEventArgs arg)
        {
            Task<bool> isHandledTask = HandelSubject(arg.Message.Subject, arg.Message);

            var isHandled = await isHandledTask;

            if (isHandled)
            {
                await arg.CompleteMessageAsync(arg.Message);
            }
            else
            {
                await arg.AbandonMessageAsync(arg.Message);
            }
        }

        private async Task DeadLetterProcessor_ProcessMessageAsync(ProcessMessageEventArgs arg)
        {
            var isHandledTask = HandelSubject(arg.Message.Subject, arg.Message);

            var isHandled = await isHandledTask;

            if (isHandled)
            {
                await arg.CompleteMessageAsync(arg.Message);
            }
            else
            {
                await arg.AbandonMessageAsync(arg.Message);
            }
        }

        private Task Processor_ProcessErrorAsync(ProcessErrorEventArgs arg)
        {
            _logger.LogCritical(arg.Exception, "WalletEventsListener => _processor => Processor_ProcessErrorAsync Message handler encountered an exception," +
                " Error Source:{ErrorSource}," +
                " Entity Path:{EntityPath}",
                arg.ErrorSource.ToString(),
                arg.EntityPath
            );

            return Task.CompletedTask;
        }

        private Task DeadLetterProcessor_ProcessErrorAsync(ProcessErrorEventArgs arg)
        {
            _logger.LogCritical(arg.Exception, "WalletEventsListener DeadLetter Message handler encountered an exception," +
                " Error Source:{ErrorSource}," +
                " Entity Path:{EntityPath}",
                arg.ErrorSource.ToString(),
                arg.EntityPath
            );

            return Task.CompletedTask;
        }

        private Task<bool> HandelSubject(string subject, ServiceBusReceivedMessage message)
        {
            message.ApplicationProperties.TryGetValue("Version", out var version);

            return (subject, version) switch
            {
                (EventType.WalletCreated, 2) => HandleAsync<WalletCreatedData>(message),
                (EventType.TransactionCreated, 2) => HandleAsync<TransactionCreatedData>(message),

                _ => HandleAsync<object>(message),
            };
        }

        private async Task<bool> HandleAsync<T>(ServiceBusReceivedMessage message)
        {
            var eventType = new PropertyEnricher(name: "EventType", message.Subject);
            var sessionId = new PropertyEnricher(name: "SessionId", message.SessionId);
            var messageId = new PropertyEnricher(name: "MessageId", message.MessageId);

            using (LogContext.Push(eventType, sessionId, messageId))
            {
                _logger.LogInformation("Event handling started.");

                var json = Encoding.UTF8.GetString(message.Body);

                var body = JsonConvert.DeserializeObject<MessageBody<T>>(json) ?? throw new NullReferenceException("Failed deserialize json object");

                using var scope = _provider.CreateScope();

                var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

                var isHandled = await mediator.Send(body);

                _logger.LogInformation("Event handling completed, Result: {Result} Event Body: {Body}  ", isHandled, body);

                return isHandled;
            }
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _processor?.StartProcessingAsync(cancellationToken);

            if (_isDeadLetterEnabled)
                _deadLetterProcessor?.StartProcessingAsync(cancellationToken);

            return Task.CompletedTask;
        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            _processor?.CloseAsync(cancellationToken);

            if (_isDeadLetterEnabled)
                _deadLetterProcessor?.CloseAsync(cancellationToken);

            return Task.CompletedTask;
        }

        public Task CloseProccessorAsync()
        {
            _processor?.CloseAsync();

            _deadLetterProcessor?.CloseAsync();

            return Task.CompletedTask;
        }
    }
}
