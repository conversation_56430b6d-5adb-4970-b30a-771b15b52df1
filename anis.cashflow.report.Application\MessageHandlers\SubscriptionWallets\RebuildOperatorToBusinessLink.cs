﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.Exceptions;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Anis.Cashflow.Report.Application.MessageHandlers.SubscriptionWallets
{
    public class RebuildOperatorToBusinessLink : IRequestHandler<OperatorToBusinessLinkRebuildModel, Unit>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<RebuildOperatorToBusinessLink> _logger;

        public RebuildOperatorToBusinessLink(IUnitOfWork unitOfWork, ILogger<RebuildOperatorToBusinessLink> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<Unit> Handle(OperatorToBusinessLinkRebuildModel request, CancellationToken cancellationToken)
        {
            var subscription = await _unitOfWork.Subscriptions.FindAsync(request.SubscriptionId, includeRelated: true);
            if (subscription is null)
                throw new AppException(ExceptionStatusCode.NotFound, $"No subscription found for id: {request.SubscriptionId}");

            HashSet<SubscriptionWallet> subscriptionWallets = new HashSet<SubscriptionWallet>();

            foreach (var wallet in request.SubscriptionWallets)
            {
                var isWalletExsit = await _unitOfWork.Wallets.AnyAsync(wallet.WalletId);

                if (!isWalletExsit)
                    throw new AppException(ExceptionStatusCode.NotFound, $"No wallet found for id: {wallet.WalletId}");

                subscriptionWallets.Add(new SubscriptionWallet(
                                            subscriptionId: subscription.Id,
                                            walletId: wallet.WalletId,
                                            linkDateTime: wallet.LinkDateTime));
            }

            subscription.ReplaceSubscriptionWallets(subscriptionWallets, request.StateVersion);

            await _unitOfWork.SaveChangesAsync();

            return Unit.Value;
        }
    }
}
