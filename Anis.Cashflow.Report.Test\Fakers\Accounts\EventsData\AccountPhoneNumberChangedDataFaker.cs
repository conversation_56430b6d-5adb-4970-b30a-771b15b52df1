﻿using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;

namespace Anis.Cashflow.Report.Test.Fakers.Accounts.EventsData
{
    internal class AccountPhoneNumberChangedDataFaker : PrivateFaker<AccountPhoneNumberChangedData>
    {
        public AccountPhoneNumberChangedDataFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.Phone, f => f.Random.AlphaNumeric(10));
        }
    }

}
