﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Anis.Cashflow.Report.Infra.Migrations
{
    public partial class AddConfirmedAtPropertyIntoAccountEntity : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<DateTime>(
                name: "ConfirmedAt",
                table: "Accounts",
                type: "datetime2",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ConfirmedAt",
                table: "Accounts");
        }
    }
}
