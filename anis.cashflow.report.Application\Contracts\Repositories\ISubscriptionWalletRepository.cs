﻿using Anis.Cashflow.Report.Application.Features.Consumer.Filter;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Entities;

namespace Anis.Cashflow.Report.Application.Contracts.Repositories
{
    public interface ISubscriptionWalletRepository : IAsyncRepository<SubscriptionWallet>
    {
        Task<Features.Management.Filter.FilterResponse> FilterAsync(Features.Management.Filter.FilterQuery request, BlackListSubscriptionsModel model, CancellationToken cancellationToken);
        Task<FilterResponse> FilterConsumerAsync(FilterQuery request, BlackListSubscriptionsModel model, CancellationToken cancellationToken);
        Task<SubscriptionWallet?> GetBySubscriptionIdIdAndWalletIdAsync(string subscriptionId, string walletId);
    }
}
