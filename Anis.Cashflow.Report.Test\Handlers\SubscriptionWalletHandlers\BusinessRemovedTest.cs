﻿using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.EventsData.SubscriptionWallets;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Fakers.SubscriptionWallets;
using Anis.Cashflow.Report.Test.Fakers.SubscriptionWallets.NotificationData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.OperatorBusinessLinkHandlers
{
    public class BusinessRemovedTest : TestBase
    {
        public BusinessRemovedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task WalltRemoved_ShouldRemoveBusinessWalletFromSubscription_ResultTrue()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var subscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(f => f.Subscription, createSubscription)
                .RuleFor(f => f.Wallet, creatWallet)
                .Generate();

            await context.SubscriptionWallets.AddAsync(subscriptionWallet);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessRemovedDataFaker()
                .RuleFor(f => f.BusinessWalletId, creatWallet.Id)
                .Generate();

            var notificationBody = new NotificationBody<BusinessRemovedData>
            {
                OperatorSubscriptionId = createSubscription.Id,
                StateVersion = createSubscription.StateVersion + 1,
                Data = businessAddedData,
                Type = NotificationType.BusinessRemoved,
                DataVersion = 1
            };

            var result = await mediator.Send(notificationBody);

            var dbSubscription = await context.Subscriptions.FindAsync(createSubscription.Id);
            var dbSubscriptionWallet = await context.SubscriptionWallets.FindAsync(subscriptionWallet.Id);

            Assert.True(result);
            Assert.NotNull(dbSubscription);
            Assert.Null(dbSubscriptionWallet);
            Assert.Equal(notificationBody.StateVersion, dbSubscription.StateVersion);
        }

        [Fact]
        public async Task WalltRemoved_RemovdWalletWithInvalidSubscription_ShouldReturnFalse()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var createSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(f => f.Subscription, createSubscription)
                .RuleFor(f => f.Wallet, creatWallet)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);
            await context.SubscriptionWallets.AddAsync(createSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessRemovedDataFaker()
                .RuleFor(f => f.BusinessWalletId, creatWallet.Id)
                .Generate();

            var notificationBody = new NotificationBody<BusinessRemovedData>
            {
                OperatorSubscriptionId = "invalid-subscription-id",
                StateVersion = createSubscription.StateVersion + 1,
                Data = businessAddedData,
                Type = NotificationType.BusinessRemoved,
                DataVersion = 1
            };

            var result = await mediator.Send(notificationBody);

            var dbSubscription = await context.Subscriptions.FindAsync(createSubscription.Id);
            var dbSubscriptionWallet = await context.SubscriptionWallets.FirstOrDefaultAsync(c => c.WalletId == creatWallet.Id);

            Assert.False(result);
            Assert.NotNull(dbSubscription);
            Assert.NotNull(dbSubscriptionWallet);
            Assert.Equal(createSubscription.StateVersion, dbSubscription.StateVersion);
        }

        [Fact]
        public async Task WalltRemoved_RemovdWalletWithInvalidStateVersion_ShouldReturnFalse()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var creatSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(f => f.Subscription, createSubscription)
                .RuleFor(f => f.Wallet, creatWallet)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);
            await context.SubscriptionWallets.AddAsync(creatSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessRemovedDataFaker()
                .RuleFor(f => f.BusinessWalletId, creatWallet.Id)
                .Generate();

            var notificationBody = new NotificationBody<BusinessRemovedData>
            {
                OperatorSubscriptionId = createSubscription.Id,
                StateVersion = createSubscription.StateVersion + 2, // Invalid state version
                Data = businessAddedData,
                Type = NotificationType.BusinessRemoved,
                DataVersion = 1
            };

            var result = await mediator.Send(notificationBody);

            var dbSubscription = await context.SubscriptionWallets
                .Include(c => c.Subscription)
                .FirstOrDefaultAsync(c => c.Id == creatSubscriptionWallet.Id);

            var dbOtherSubscription = await context.SubscriptionWallets.FirstOrDefaultAsync(i => i.Id != creatSubscriptionWallet.Id);

            Assert.False(result);
            Assert.NotNull(dbSubscription);
            Assert.Null(dbOtherSubscription);
            Assert.Equal(createSubscription.StateVersion, dbSubscription.Subscription.StateVersion);
        }

        [Fact]
        public async Task WalltRemoved_RemovdWalletWithInvalidStateVersion_ShouldReturnTrue()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .RuleFor(f => f.StateVersion, 2)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var creatSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(f => f.Subscription, createSubscription)
                .RuleFor(f => f.Wallet, creatWallet)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);
            await context.SubscriptionWallets.AddAsync(creatSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessRemovedDataFaker()
                .RuleFor(f => f.BusinessWalletId, creatWallet.Id)
                .Generate();

            var notificationBody = new NotificationBody<BusinessRemovedData>
            {
                OperatorSubscriptionId = createSubscription.Id,
                StateVersion = 1,
                Data = businessAddedData,
                Type = NotificationType.BusinessRemoved,
                DataVersion = 1
            };

            var result = await mediator.Send(notificationBody);

            var dbSubscription = await context.SubscriptionWallets.FindAsync(creatSubscriptionWallet.Id);
            var dbOtherSubscription = await context.SubscriptionWallets.FirstOrDefaultAsync(i => i.Id != creatSubscriptionWallet.Id);

            Assert.True(result);
            Assert.NotNull(dbSubscription);
            Assert.Null(dbOtherSubscription);
        }

        [Fact]
        public async Task WalltRemoved_ShouldRemoveWalletWithExistingSubscriptionWallet_ResultTrue()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var secondCreatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var subscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(f => f.Subscription, createSubscription)
                .RuleFor(f => f.Wallet, creatWallet)
                .Generate();

            var secondSubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(f => f.Subscription, createSubscription)
                .RuleFor(f => f.Wallet, secondCreatWallet)
                .Generate();

            await context.SubscriptionWallets.AddRangeAsync(subscriptionWallet, secondSubscriptionWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessRemovedDataFaker()
                .RuleFor(f => f.BusinessWalletId, creatWallet.Id)
                .Generate();

            var notificationBody = new NotificationBody<BusinessRemovedData>
            {
                OperatorSubscriptionId = createSubscription.Id,
                StateVersion = createSubscription.StateVersion + 1,
                Data = businessAddedData,
                Type = NotificationType.BusinessRemoved,
                DataVersion = 1
            };

            var result = await mediator.Send(notificationBody);

            var dbSubscription = await context.Subscriptions.FindAsync(createSubscription.Id);
            var dbSubscriptionWallet = await context.SubscriptionWallets.FindAsync(subscriptionWallet.Id);
            var dbSecondSubscriptionWallet = await context.SubscriptionWallets.FindAsync(secondSubscriptionWallet.Id);

            Assert.True(result);
            Assert.NotNull(dbSubscription);
            Assert.NotNull(dbSecondSubscriptionWallet);
            Assert.Null(dbSubscriptionWallet);
            Assert.Equal(notificationBody.StateVersion, dbSubscription.StateVersion);
        }
    }
}
