﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Extensions;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Holders.DataTypes;
using Anis.Cashflow.Report.Domain.Exceptions;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.HolderHandlers;

public class HoldresRebuildHandler : IRequestHandler<HoldersRebuildModel, Unit>
{
    private readonly IUnitOfWork _unitOfWork;

    public HoldresRebuildHandler(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;

    }

    public async Task<Unit> Handle(HoldersRebuildModel model, CancellationToken cancellationToken)
    {
        var aggregateId = model.Messages.First().AggregateId;

        var holder = await _unitOfWork.Holders.FindAsync(aggregateId);

        if (holder == null)
        {
            var firstMessage = model.Messages.SingleOrDefault(m => m.Sequence == 1);

            if (firstMessage == null)
                throw new AppException(ExceptionStatusCode.Aborted,
                                       $"Could not find the in the database nor create it from message body, holder id: {aggregateId}");

            holder = await HandelHolderCreated(firstMessage.ToMessageBody<HolderCreatedData>());
        }

        foreach (var message in model.Messages)
        {
            if (holder.Sequence != message.Sequence - 1)
                if (holder.Sequence >= message.Sequence)
                    continue;
                else
                    throw new AppException(ExceptionStatusCode.Aborted,
                            $"Message sequence {message.Sequence} greater than holder sequence {holder.Sequence}");

            var task = (message.Type) switch
            {
                EventType.HolderDeleted => HandleHolderSequenceIncremented(holder, message.ToMessageBody<HolderSequenceIncrementedData>()),
                EventType.NameChanged => HandelNameChanged(holder, message.ToMessageBody<NameChangedData>()),
                EventType.RegionUnLocked => HandleHolderSequenceIncremented(holder, message.ToMessageBody<HolderSequenceIncrementedData>()),
                EventType.RegionAdded => HandleHolderSequenceIncremented(holder, message.ToMessageBody<HolderSequenceIncrementedData>()),
                EventType.RegionLocked => HandleHolderSequenceIncremented(holder, message.ToMessageBody<HolderSequenceIncrementedData>()),
                EventType.RegionRemoved => HandleHolderSequenceIncremented(holder, message.ToMessageBody<HolderSequenceIncrementedData>()),
                EventType.RegionDifferenceChanged => HandleHolderSequenceIncremented(holder, message.ToMessageBody<HolderSequenceIncrementedData>()),
                _ => throw new AppException(ExceptionStatusCode.OutOfRange, $"Event type out of range in rebuild service => {message.Type}")
            };

            await task;
        }

        await _unitOfWork.SaveChangesAsync();

        return Unit.Value;
    }

    private async Task<Holder> HandelHolderCreated(MessageBody<HolderCreatedData> message)
    {
        var holder = new Holder(message);

        await _unitOfWork.Holders.AddAsync(holder);

        return holder;
    }

    private async Task HandleHolderSequenceIncremented(Holder holder, MessageBody<HolderSequenceIncrementedData> message)
    {
        holder.UpdateSequence(message);

        await Task.CompletedTask;
    }

    private async Task HandelNameChanged(Holder holder, MessageBody<NameChangedData> message)
    {
        holder.Update(message);

        await Task.CompletedTask;
    }
}
