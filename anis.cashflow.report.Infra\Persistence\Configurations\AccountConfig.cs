﻿using Anis.Cashflow.Report.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Anis.Cashflow.Report.Infra.Persistence.Configurations
{
    public class AccountConfig : IEntityTypeConfiguration<Account>
    {
        public void Configure(EntityTypeBuilder<Account> builder)
        {
            builder.Property(c => c.Id).HasMaxLength(Config.StringIdLength).IsRequired();

            builder.Property(c => c.Sequence).IsConcurrencyToken();

            builder.Property(pi => pi.OwnerName).IsRequired();

            builder.Property(pi => pi.LocationId).IsRequired();

            builder.Property(pi => pi.ConfirmedAt).IsRequired(false);


            builder.HasIndex(a => a.ExpiryAt);

            builder.HasIndex(a => a.Phone);

            builder.HasIndex(a => a.Email);

            builder.HasOne(i => i.Location)
            .WithMany(i => i.Accounts)
            .HasForeignKey(i => i.LocationId)
            .OnDelete(DeleteBehavior.Cascade);
        }
    }
}
