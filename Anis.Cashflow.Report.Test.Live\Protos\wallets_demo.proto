syntax = "proto3";

package anis.gateway.demo.v2;

import "google/protobuf/wrappers.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/Timestamp.proto";

option csharp_namespace = "Anis.Cashflow.Report.Test.Live.Protos.WalltesDemoEvent";

service WalltesDemoEvents {
  rpc CreateWallet (WalletRequest) returns (google.protobuf.Empty);
  rpc CreateTransaction (TransactionRequest) returns (google.protobuf.Empty);
}

message WalletRequest {
	google.protobuf.StringValue aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	google.protobuf.StringValue subscription_id = 3;
	SubscriptionType subscription_type = 4;
	CurrencyType currency_type = 5;
	google.protobuf.StringValue type = 6;
	google.protobuf.StringValue region_id = 7;
	google.protobuf.StringValue holder_id = 8;
}

message TransactionRequest {
	google.protobuf.StringValue aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	google.protobuf.StringValue event_id = 3;
	google.protobuf.StringValue reference_id = 4;
	google.protobuf.DoubleValue debit = 5;
	google.protobuf.DoubleValue credit = 6;
	google.protobuf.DoubleValue value = 7;
	google.protobuf.StringValue type = 8;
	int64 sequence = 9;
}

enum CurrencyType {
	NON = 0;
	LYD = 1;
	USD = 2;
}


enum SubscriptionType {
	TREASURY = 0;
	BUSINESS = 1;
	PERSONAL = 2;
}
