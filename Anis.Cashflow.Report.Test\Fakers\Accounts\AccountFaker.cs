﻿using Anis.Cashflow.Report.Domain.Entities;

namespace Anis.Cashflow.Report.Test.Fakers.Accounts
{
    public class AccountFaker : PrivateFaker<Account>
    {
        public AccountFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.Id, Guid.NewGuid().ToString());
            RuleFor(r => r.OwnerName, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.Number, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.Email, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.LocationId, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.Phone, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.ExpiryAt, DateTime.UtcNow.AddDays(1));
            RuleFor(r => r.Sequence, 1);
        }
    }
}