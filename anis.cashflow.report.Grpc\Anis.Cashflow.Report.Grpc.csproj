﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>disable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>3dc3d50a-5abc-45ad-990d-ccb6bc790486</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
	</PropertyGroup>

	<ItemGroup>
		<None Remove="Protos\cashflow_rebuild.proto" />
		<None Remove="Protos\cashflow_rebuild_v3.proto" />
		<None Remove="Protos\Client\events_history_ecom.proto" />
		<None Remove="Protos\Client\holders_events_history.proto" />
		<None Remove="Protos\Client\notification_history_operator_business_link.proto" />
		<None Remove="Protos\Client\special_queries.proto" />
		<None Remove="Protos\queries.proto" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="Calzolari.Grpc.AspNetCore.Validation" Version="6.2.0" />
		<PackageReference Include="Grpc.AspNetCore" Version="2.49.0" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.17.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\anis.cashflow.report.Infra\Anis.Cashflow.Report.Infra.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Protobuf Include="Protos\cashflow_rebuild_v3.proto" GrpcServices="Server" />
		<Protobuf Include="Protos\Client\events_history_ecom.proto" GrpcServices="Client" />
		<Protobuf Include="Protos\Client\holders_events_history.proto" GrpcServices="Client" />
		<Protobuf Include="Protos\Client\notification_history_operator_business_link.proto" GrpcServices="Client" />
		<Protobuf Include="Protos\Client\special_queries.proto" GrpcServices="Client" />
		<Protobuf Include="Protos\Managmant\queries_managment.proto" GrpcServices="Server" />
		<Protobuf Include="Protos\cashflow_rebuild.proto" GrpcServices="Server" />
		<Protobuf Include="Protos\Consumer\queries_consumer.proto" GrpcServices="Server" />
	</ItemGroup>

	<ItemGroup>
		<Compile Update="Resources\Phrases.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Phrases.resx</DependentUpon>
		</Compile>
		<Compile Update="Resources\Titles.Designer.cs">
			<DesignTime>True</DesignTime>
			<AutoGen>True</AutoGen>
			<DependentUpon>Titles.resx</DependentUpon>
		</Compile>
	</ItemGroup>

	<ItemGroup>
		<EmbeddedResource Update="Resources\Phrases.en.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\Phrases.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
			<LastGenOutput>Phrases.Designer.cs</LastGenOutput>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\Titles.en.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
		</EmbeddedResource>
		<EmbeddedResource Update="Resources\Titles.resx">
			<Generator>PublicResXFileCodeGenerator</Generator>
			<LastGenOutput>Titles.Designer.cs</LastGenOutput>
		</EmbeddedResource>
	</ItemGroup>

</Project>
