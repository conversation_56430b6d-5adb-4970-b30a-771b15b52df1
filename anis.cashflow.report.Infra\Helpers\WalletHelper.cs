﻿using Anis.Cashflow.Report.Domain.Entities;

namespace Anis.Cashflow.Report.Infra.Helpers
{
    public static class WalletHelper
    {
        public static string CreateWalletIdentifier(Wallet wallet, Account account)
        {
            return string.Concat((int)wallet.Subscription.Type,
                                  account.Number,
                                  "-" + "0",
                                  (int)wallet.CurrencyType,
                                  "(",
                                  CultureHelper.Read(wallet.Region.ArabicName,
                                                     wallet.Region.EnglishName),
                                  wallet.Holder is null ? string.Empty :
                                  "-" + CultureHelper.Read(wallet.Holder!.ArabicName,
                                                           wallet.Holder.EnglishName)!,
                                  ")");
        }

        public static string CreateWalletIdentifier(Wallet wallet, Account account, Subscription subscription, Region region, Holder? holder)
        {
            return string.Concat((int)subscription.Type,
                                       account.Number,
                                       "-" + "0",
                                       (int)wallet.CurrencyType,
                                       "(",
                                       CultureHelper.Read(region.ArabicName,
                                                          region.EnglishName),
                                       holder is null ? string.Empty :
                                       "-" + CultureHelper.Read(holder!.ArabicName,
                                                                holder.EnglishName)!,
                                       ")");
        }
    }
}
