﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Holders.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.HolderHandlers
{
    public class HolderCreatedHandler : IRequestHandler<MessageBody<HolderCreatedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public HolderCreatedHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> Handle(MessageBody<HolderCreatedData> message, CancellationToken cancellationToken)
        {
            var isExist = await _unitOfWork.Holders.IsExistAsync(message.AggregateId);

            if (isExist)
                return true;

            var holder = new Holder(message);

            await _unitOfWork.Holders.AddAsync(holder);

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}