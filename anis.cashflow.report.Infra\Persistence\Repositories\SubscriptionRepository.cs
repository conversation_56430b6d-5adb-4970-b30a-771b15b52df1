﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace Anis.Cashflow.Report.Infra.Persistence.Repositories
{
    public class SubscriptionRepository : AsyncRepository<Subscription>, ISubscriptionRepository
    {
        private readonly AppDbContext _appDbContext;

        public SubscriptionRepository(AppDbContext appDbContext) : base(appDbContext)
        {
            _appDbContext = appDbContext;
        }

        public override Task<Subscription?> FindAsync(string id, bool includeRelated = false)
        {
            if (includeRelated)
            {
                return _appDbContext.Subscriptions
                    .Include(s => s.SubscriptionWallets)
                        .ThenInclude(sw => sw.Wallet)
                    .FirstOrDefaultAsync(s => s.Id == id);
            }

            return base.FindAsync(id, includeRelated);
        }

        public async Task<bool> IsExistAsync(string id)
            => await _appDbContext.Subscriptions.AnyAsync(c => c.Id == id);
    }
}
