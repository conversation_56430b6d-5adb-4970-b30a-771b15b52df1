﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.AccountHandlers
{
    public class SubscriptionCreatedHandler : IRequestHandler<MessageBody<SubscriptionCreatedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public SubscriptionCreatedHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> Handle(MessageBody<SubscriptionCreatedData> messageBody, CancellationToken cancellationToken)
        {
            if (await _unitOfWork.Subscriptions.IsExistAsync(messageBody.Data.Id))
                return true;

            var account = await _unitOfWork.Accounts.FindAsync(messageBody.AggregateId);

            if (account == null) return false;

            if (account.Sequence != messageBody.Sequence - 1)
                return account.Sequence >= messageBody.Sequence;

            var subscription = new Subscription(messageBody);

            account.IncrementSequence();

            await _unitOfWork.Subscriptions.AddAsync(subscription);

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}