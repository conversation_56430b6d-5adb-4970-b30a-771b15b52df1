﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.RegionHandlers
{
    public class RegionUpdatedHandler : IRequestHandler<MessageBody<RegionUpdatedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public RegionUpdatedHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> Handle(MessageBody<RegionUpdatedData> message, CancellationToken cancellationToken)
        {
            var region = await _unitOfWork.Regions.FindAsync(message.AggregateId);

            if (region == null) return false;

            if (region.Sequence != message.Sequence - 1)
                return region.Sequence >= message.Sequence;

            region.Modify(message);

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}