﻿using Anis.Cashflow.Report.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Anis.Cashflow.Report.Infra.Persistence.Configurations
{
    public class SubscriptionConfig : IEntityTypeConfiguration<Subscription>
    {
        public void Configure(EntityTypeBuilder<Subscription> builder)
        {
            builder.Property(c => c.Id).HasMaxLength(Config.StringIdLength).IsRequired();

            builder.Property(c => c.AccountId).HasMaxLength(Config.StringIdLength).IsRequired();

            builder.Property(c => c.Type).HasConversion<string>();

            builder.Property(c => c.StateVersion).IsConcurrencyToken();

            builder.HasOne(i => i.Account)
                .WithMany(g => g.Subscriptions)
                .HasForeignKey(i => i.AccountId)
                .OnDelete(DeleteBehavior.Cascade);

        }
    }
}
