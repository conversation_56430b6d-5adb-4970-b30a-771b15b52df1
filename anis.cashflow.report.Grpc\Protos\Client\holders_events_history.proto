syntax = "proto3";

package anis.holder;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Anis.Cashflow.Report.Grpc.Protos.Clients.Holders";

service HoldersEventsHistory {
  rpc GetEvents (GetEventsRequest) returns (Response);
}

message GetEventsRequest{
	int32 current_page = 1;
	int32 page_size = 2;
}

message Response {
  repeated EventMessage events = 1;
}

message EventMessage {
  string correlation_id = 1;
  google.protobuf.Timestamp date_time = 2;
  string data = 3;
  string type = 4;
  int32 sequence = 5;
  int32 version = 6;
  string aggregate_id = 7;
  string user_id = 8;
}