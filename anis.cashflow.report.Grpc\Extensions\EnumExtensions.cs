﻿using Anis.Cashflow.Report.Application.Enums;
using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.Exceptions;
using GrpcCore = Grpc.Core;
using ManagementWalletTypeFilter = Anis.Cashflow.Report.Grpc.Protos.Managmant.WalletTypeFilter;
using ConsumerWalletTypeFilter = Anis.Cashflow.Report.Grpc.Protos.Consumer.WalletTypeFilter;

namespace Anis.Cashflow.Report.Grpc.Extensions
{
    public static class EnumExtensions
    {
        public static WalletType ToDomain(this string walletType)
            => walletType switch
            {
                "Profits" => WalletType.Profits,
                "Normal" => WalletType.Normal,

                _ => throw new NotImplementedException("WalletType not implemented ."),
            };

        public static GrpcCore.StatusCode ToRpcStatuseCode(this ExceptionStatusCode exceptionStatusCode)
            => exceptionStatusCode switch
            {
                ExceptionStatusCode.OK => GrpcCore.StatusCode.OK,
                ExceptionStatusCode.Cancelled => GrpcCore.StatusCode.Cancelled,
                ExceptionStatusCode.Unknown => GrpcCore.StatusCode.Unknown,
                ExceptionStatusCode.InvalidArgument => GrpcCore.StatusCode.InvalidArgument,
                ExceptionStatusCode.DeadlineExceeded => GrpcCore.StatusCode.DeadlineExceeded,
                ExceptionStatusCode.NotFound => GrpcCore.StatusCode.NotFound,
                ExceptionStatusCode.AlreadyExists => GrpcCore.StatusCode.AlreadyExists,
                ExceptionStatusCode.PermissionDenied => GrpcCore.StatusCode.PermissionDenied,
                ExceptionStatusCode.ResourceExhausted => GrpcCore.StatusCode.ResourceExhausted,
                ExceptionStatusCode.FailedPrecondition => GrpcCore.StatusCode.FailedPrecondition,
                ExceptionStatusCode.Aborted => GrpcCore.StatusCode.Aborted,
                ExceptionStatusCode.OutOfRange => GrpcCore.StatusCode.OutOfRange,
                ExceptionStatusCode.Unimplemented => GrpcCore.StatusCode.Unimplemented,
                ExceptionStatusCode.Internal => GrpcCore.StatusCode.Internal,
                ExceptionStatusCode.Unavailable => GrpcCore.StatusCode.Unavailable,
                ExceptionStatusCode.DataLoss => GrpcCore.StatusCode.DataLoss,
                ExceptionStatusCode.Unauthenticated => GrpcCore.StatusCode.Unauthenticated,
                _ => throw new NotImplementedException("StatusCode not implemented ."),
            };

        public static Domain.Enums.FilterEnums.SubscriptionType ToRpcSubscriptionType(this Protos.Managmant.SubscriptionType subscriptionType)
            => subscriptionType switch
            {
                Protos.Managmant.SubscriptionType.AllOptions => Domain.Enums.FilterEnums.SubscriptionType.All,
                Protos.Managmant.SubscriptionType.Operator => Domain.Enums.FilterEnums.SubscriptionType.Operator,
                Protos.Managmant.SubscriptionType.Business => Domain.Enums.FilterEnums.SubscriptionType.Business,
                Protos.Managmant.SubscriptionType.Normal => Domain.Enums.FilterEnums.SubscriptionType.Normal,
                _ => throw new NotImplementedException("SubscriptionType not implemented ."),
            };


        public static Domain.Enums.FilterEnums.CurrencyType ToRpcCurrencyType(this Protos.Managmant.Currency currencyType)
            => currencyType switch
            {
                Protos.Managmant.Currency.All => Domain.Enums.FilterEnums.CurrencyType.All,
                Protos.Managmant.Currency.Usd => Domain.Enums.FilterEnums.CurrencyType.Usd,
                Protos.Managmant.Currency.Lyd => Domain.Enums.FilterEnums.CurrencyType.Lyd,
                _ => throw new NotImplementedException("CurrencyType not implemented ."),
            };

        public static Domain.Enums.FilterEnums.SubscriptionType ToRpcSubscriptionType(this Protos.Consumer.SubscriptionType subscriptionType)
           => subscriptionType switch
           {
               Protos.Consumer.SubscriptionType.AllOptions => Domain.Enums.FilterEnums.SubscriptionType.All,
               Protos.Consumer.SubscriptionType.Operator => Domain.Enums.FilterEnums.SubscriptionType.Operator,
               Protos.Consumer.SubscriptionType.Business => Domain.Enums.FilterEnums.SubscriptionType.Business,
               Protos.Consumer.SubscriptionType.Normal => Domain.Enums.FilterEnums.SubscriptionType.Normal,
               _ => throw new NotImplementedException("SubscriptionType not implemented ."),
           };


        public static Protos.Managmant.SubscriptionType ToDomainEnumSubscriptionType(this Domain.Enums.FilterEnums.SubscriptionType subscriptionType)
            => subscriptionType switch
            {
                Domain.Enums.FilterEnums.SubscriptionType.All => Protos.Managmant.SubscriptionType.AllOptions,
                Domain.Enums.FilterEnums.SubscriptionType.Operator => Protos.Managmant.SubscriptionType.Operator,
                Domain.Enums.FilterEnums.SubscriptionType.Business => Protos.Managmant.SubscriptionType.Business,
                Domain.Enums.FilterEnums.SubscriptionType.Normal => Protos.Managmant.SubscriptionType.Normal,
                _ => throw new NotImplementedException("SubscriptionType not implemented ."),
            };

        public static Protos.Managmant.Currency ToDomainEnumCurrencyType(this Domain.Enums.FilterEnums.CurrencyType currencyType)
            => currencyType switch
            {
                Domain.Enums.FilterEnums.CurrencyType.All => Protos.Managmant.Currency.All,
                Domain.Enums.FilterEnums.CurrencyType.Usd => Protos.Managmant.Currency.Usd,
                Domain.Enums.FilterEnums.CurrencyType.Lyd => Protos.Managmant.Currency.Lyd,
                _ => throw new NotImplementedException("CurrencyType not implemented ."),
            };

        public static Protos.Consumer.DebtType ToRpcDebtType(this Domain.Enums.FilterEnums.DebtType debtType)
            => debtType switch
            {
                Domain.Enums.FilterEnums.DebtType.All => Protos.Consumer.DebtType.All,
                Domain.Enums.FilterEnums.DebtType.None => Protos.Consumer.DebtType.None,
                Domain.Enums.FilterEnums.DebtType.DebitOnly => Protos.Consumer.DebtType.DebitOnly,
                Domain.Enums.FilterEnums.DebtType.Urgent => Protos.Consumer.DebtType.Urgent,
                Domain.Enums.FilterEnums.DebtType.Delayed => Protos.Consumer.DebtType.Delayed,
                Domain.Enums.FilterEnums.DebtType.HasDebit => Protos.Consumer.DebtType.HasDebit,
                _ => throw new NotImplementedException("DebtType not implemented ."),
            };

        public static Domain.Enums.FilterEnums.DebtType ToRpcDebtType(this Protos.Consumer.DebtType debtType)
            => debtType switch
            {
                Protos.Consumer.DebtType.All => Domain.Enums.FilterEnums.DebtType.All,
                Protos.Consumer.DebtType.None => Domain.Enums.FilterEnums.DebtType.None,
                Protos.Consumer.DebtType.DebitOnly => Domain.Enums.FilterEnums.DebtType.DebitOnly,
                Protos.Consumer.DebtType.Urgent => Domain.Enums.FilterEnums.DebtType.Urgent,
                Protos.Consumer.DebtType.Delayed => Domain.Enums.FilterEnums.DebtType.Delayed,
                Protos.Consumer.DebtType.HasDebit => Domain.Enums.FilterEnums.DebtType.HasDebit,
                _ => throw new NotImplementedException("DebtType not implemented ."),
            };

        public static WalletTypeFilter ToApplicationWalletTypeFilter(this ManagementWalletTypeFilter walletTypeFilter)
            => walletTypeFilter switch
            {
                ManagementWalletTypeFilter.NormalWallets => WalletTypeFilter.Normal, 
                ManagementWalletTypeFilter.ProfitWallets => WalletTypeFilter.Profits, 
                ManagementWalletTypeFilter.AllWallets => WalletTypeFilter.All,
                _ => throw new NotImplementedException("WalletFilterType not implemented .")
            };
        
        public static WalletTypeFilter ToApplicationWalletTypeFilter(this ConsumerWalletTypeFilter walletTypeFilter)
            => walletTypeFilter switch
            {
                ConsumerWalletTypeFilter.NormalWallets => WalletTypeFilter.Normal, 
                ConsumerWalletTypeFilter.ProfitWallets => WalletTypeFilter.Profits, 
                ConsumerWalletTypeFilter.AllWallets => WalletTypeFilter.All,
                _ => throw new NotImplementedException("WalletFilterType not implemented .")
            };
    }
}
