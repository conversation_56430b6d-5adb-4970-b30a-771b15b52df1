﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>

    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Protos\accounts_demo.proto" />
    <None Remove="Protos\demo_events.proto" />
    <None Remove="Protos\wallets_demo.proto" />
	<None Remove="Protos\regions.proto" />
  </ItemGroup>

	<ItemGroup>
		<PackageReference Include="Grpc.Tools" Version="2.47.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="6.0.11" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.4.0" />
		<PackageReference Include="xunit" Version="2.4.2" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
		<PackageReference Include="coverlet.collector" Version="3.1.2">
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			<PrivateAssets>all</PrivateAssets>
		</PackageReference>
	</ItemGroup>
	<ItemGroup>
	  <ProjectReference Include="..\Anis.Cashflow.Report.Test\Anis.Cashflow.Report.Test.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Protobuf Include="Protos\accounts_demo.proto" GrpcServices="Client" />
	  <Protobuf Include="Protos\demo_events.proto" Access="Public" GrpcServices="Client" />
	  <Protobuf Include="Protos\operator-business-link-demo.proto">
	    <GrpcServices>Client</GrpcServices>
	  </Protobuf>
	  <Protobuf Include="Protos\wallets_demo.proto" GrpcServices="Client" />
	  <Protobuf Include="Protos\regions.proto" GrpcServices="Client" />
	</ItemGroup>
</Project>
