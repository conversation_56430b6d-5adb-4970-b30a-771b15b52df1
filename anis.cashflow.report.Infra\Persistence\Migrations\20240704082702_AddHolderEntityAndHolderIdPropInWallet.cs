﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Anis.Cashflow.Report.Infra.Migrations
{
    public partial class AddHolderEntityAndHolderIdPropInWallet : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "HolderId",
                table: "Wallets",
                type: "nvarchar(128)",
                maxLength: 128,
                nullable: true);

            migrationBuilder.CreateTable(
                name: "Holders",
                columns: table => new
                {
                    Id = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: false),
                    ArabicName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EnglishName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Sequence = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Holders", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Wallets_HolderId",
                table: "Wallets",
                column: "HolderId");

            migrationBuilder.AddForeignKey(
                name: "FK_Wallets_Holders_HolderId",
                table: "Wallets",
                column: "HolderId",
                principalTable: "Holders",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Wallets_Holders_HolderId",
                table: "Wallets");

            migrationBuilder.DropTable(
                name: "Holders");

            migrationBuilder.DropIndex(
                name: "IX_Wallets_HolderId",
                table: "Wallets");

            migrationBuilder.DropColumn(
                name: "HolderId",
                table: "Wallets");
        }
    }
}
