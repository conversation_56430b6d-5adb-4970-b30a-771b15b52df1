﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Holders;
using Anis.Cashflow.Report.Test.Grpc.Protos.Managmant;
using Google.Protobuf.WellKnownTypes;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.GrpcTest.Management;

public class GetHoldersTest : TestBase
{
    public GetHoldersTest(ITestOutputHelper output) : base(output) { }

    [Fact]
    public async Task GetHolders_WhenGivenValidRequest_ReturnAllHolders()
    {
        using var scope = Factory.Services.CreateScope();

        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

        var firstHolderFaker = new HolderFaker().Generate();

        var secondHolderFaker = new HolderFaker().Generate();

        var thirdHolderFaker = new HolderFaker().Generate();

        await context.Holders.AddRangeAsync(firstHolderFaker, secondHolderFaker, thirdHolderFaker);

        await context.SaveChangesAsync();

        context.ChangeTracker.Clear();

        var response = await client.GetHoldersAsync(new Empty());

        var dbHolders = await context.Holders.ToListAsync();

        HolderAssert.AssertEquality(response, dbHolders);
    }

    [Fact]
    public async Task GetHolders_WhenGivenValidRequestAndNoHolderExist_ReturnEmpty()
    {
        using var scope = Factory.Services.CreateScope();

        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var client = new CashflowReportManagmentV5.CashflowReportManagmentV5Client(Channel);

        var response = await client.GetHoldersAsync(new Empty());

        var dbHolders = await context.Holders.ToListAsync();

        Assert.NotNull(response);

        Assert.Empty(response.Holders);

        Assert.Empty(dbHolders);

        Assert.Equal(dbHolders.Count, response.Holders.Count);
    }
}
