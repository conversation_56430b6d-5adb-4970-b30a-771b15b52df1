syntax = "proto3";

package anis.gateway.demo.v2;

import "google/protobuf/wrappers.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/Timestamp.proto";

option csharp_namespace = "Ecom.Cards.Grpc.Demo.V2.Regions";

service RegionsDemoEvents {
  rpc Create (CreateRequest) returns (google.protobuf.Empty);
  rpc ChangeCode (ChangeCodeRequest) returns (google.protobuf.Empty);
  rpc Update (UpdateRequest) returns (google.protobuf.Empty);
  rpc Delete (DeleteRequest) returns (google.protobuf.Empty);
}

message CreateRequest {
	google.protobuf.StringValue aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	google.protobuf.StringValue arabic_name = 3;
	google.protobuf.StringValue english_name = 4;
	google.protobuf.StringValue code = 5;
	google.protobuf.Int32Value number = 6;
	google.protobuf.Int32Value level = 7;
	google.protobuf.StringValue parent_code = 8;
	google.protobuf.DoubleValue international_transfer_difference = 9;
	google.protobuf.DoubleValue local_transfer_difference = 10;
}

message ChangeCodeRequest {
	google.protobuf.StringValue aggregate_id = 1;
	google.protobuf.Int32Value sequence = 2;
	google.protobuf.Timestamp date_time = 3;
	google.protobuf.StringValue code = 4;
	google.protobuf.Int32Value number = 5;
	google.protobuf.Int32Value level = 6;
	google.protobuf.StringValue parent_code = 7;
}

message UpdateRequest {
	google.protobuf.StringValue aggregate_id = 1;
	google.protobuf.Int32Value sequence = 2;
	google.protobuf.Timestamp date_time = 3;
	google.protobuf.StringValue arabic_name = 4;
	google.protobuf.StringValue english_name = 5;
	google.protobuf.DoubleValue international_transfer_difference = 6;
	google.protobuf.DoubleValue local_transfer_difference = 7;
}

message DeleteRequest {
	google.protobuf.StringValue aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	google.protobuf.Int32Value sequence = 3;
}
