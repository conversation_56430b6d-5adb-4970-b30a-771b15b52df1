﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Extensions;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Exceptions;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Anis.Cashflow.Report.Application.MessageHandlers.AccountHandlers;

public class AccountRebuildHandler : IRequestHandler<AccountRebuildModel, Unit>
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<AccountConfirmedHandler> _logger;

    public AccountRebuildHandler(IUnitOfWork unitOfWork,
        ILogger<AccountConfirmedHandler> logger)
    {
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task<Unit> Handle(AccountRebuildModel command, CancellationToken cancellationToken)
    {
        var account = await _unitOfWork.Accounts.FindAsync(command.Messages.First().AggregateId);

        foreach (var message in command.Messages)
        {

            if (account is null)
            {

                if (message.Type != EventType.AccountConfirmed)
                {
                    throw new AppException(ExceptionStatusCode.Aborted,
                        $"Could not find the in the database nor create it from message body, aggregate id {message.AggregateId}");
                }

                account ??= await HandleAccountConfirmed(account, message.ToMessageBody<AccountConfirmedData>());

                continue;
            }

            if (IsSequenceInvalid(account, message))
            {
                if (account.Sequence < message.Sequence)
                {
                    throw new AppException(ExceptionStatusCode.Aborted,
                        $"Message sequence {message.Sequence} greater than account sequence {account.Sequence}");
                }

                _logger.LogWarning("Received an old message with sequence {sequence}, aggregateId {id}, account sequence {sequence}",
                    message.Sequence, message.AggregateId, account.Sequence);
                continue;
            }

            var task = (message.Type) switch
            {
                EventType.AccountConfirmed => HandleAccountConfirmed(account, message.ToMessageBody<AccountConfirmedData>()),
                EventType.AccountUpdated => HandleAccountUpdated(account, message.ToMessageBody<AccountUpdatedData>()),
                EventType.AccountDeleted => HandleSequenceIncremented(account),
                EventType.AccountPhoneNumberChanged => HandlePhoneNumberChanged(account, message.ToMessageBody<AccountPhoneNumberChangedData>()),
                EventType.AccountUnconfirmed => HandleSequenceIncremented(account),
                EventType.AccountEmailChanged => HandleSequenceIncremented(account),
                EventType.AccountPriceDifferenceChanged => HandleSequenceIncremented(account),
                EventType.AccountExpirationRemoved => HandleAccountExpirationRemoved(account, message.ToMessageBody<ExpiryDateRemovedData>()),
                EventType.AccountExpirationChanged => HandleAccountExpirationChanged(account, message.ToMessageBody<ExpiryDateChangedData>()),
                EventType.SubscriptionCreated => HandleSubscriptionCreated(account, message.ToMessageBody<SubscriptionCreatedData>()),
                EventType.SubscriptionUpdated => HandleSubscriptionUpdated(account, message.ToMessageBody<SubscriptionUpdatedData>()),
                EventType.SubscriptionRemoved => HandleSequenceIncremented(account),
                EventType.AniscomActivated => HandleSequenceIncremented(account),
                EventType.AniscomDeactivated => HandleSequenceIncremented(account),
                EventType.AccountAffiliationSet => HandleSequenceIncremented(account),
                EventType.AccountAffiliationRemoved => HandleSequenceIncremented(account),

                _ => throw new AppException(ExceptionStatusCode.OutOfRange, $"Event type out of range in rebuild service => {message.Type}")
            };
            await task;
        }

        await _unitOfWork.SaveChangesAsync();

        return Unit.Value;
    }

    private Task HandleAccountExpirationChanged(Account account, MessageBody<ExpiryDateChangedData> message)
    {
        account.Modify(message);

        return Task.CompletedTask;
    }

    private Task HandleAccountExpirationRemoved(Account account, MessageBody<ExpiryDateRemovedData> message)
    {
        account.Modify(message);

        return Task.CompletedTask;
    }

    private Task HandleSequenceIncremented(Account account)
    {
        account.IncrementSequence();

        return Task.CompletedTask;
    }

    private async Task HandleSubscriptionUpdated(Account account, MessageBody<SubscriptionUpdatedData> message)
    {
        var subscription = await _unitOfWork.Subscriptions.FindAsync(message.Data.Id);

        subscription ??= account.Subscriptions.FirstOrDefault(s => s.Id == message.Data.Id);

        if (subscription is null)
        {
            throw new AppException(ExceptionStatusCode.FailedPrecondition,
                $"Operation Failed could not update subscription, No subscription with id : {message.Data.Id} was found");
        }

        subscription.Update(message);

        account.IncrementSequence();
    }

    private async Task HandleSubscriptionCreated(Account account, MessageBody<SubscriptionCreatedData> message)
    {
        var isExist = await CheckIfSubscriptionExists(message.Data.Id);

        if (isExist)
        {
            throw new AppException(ExceptionStatusCode.FailedPrecondition,
                $"Operation Failed could not create subscription with id : {message.Data.Id} as it already exist");
        }

        var subscription = new Subscription(message);

        account.IncrementSequence();

        await _unitOfWork.Subscriptions.AddAsync(subscription);
    }

    private Task HandlePhoneNumberChanged(Account account, MessageBody<AccountPhoneNumberChangedData> message)
    {
        account.Modify(message);

        return Task.CompletedTask;
    }

    private async Task HandleAccountUpdated(Account account, MessageBody<AccountUpdatedData> message)
    {
        if (!string.IsNullOrWhiteSpace(message.Data.LocationId))
        {
            var isRegionExists = await CheckIfRegionExists(message.Data.LocationId);

            if (!isRegionExists)
            {
                throw new AppException(ExceptionStatusCode.FailedPrecondition,
                    $"Location not found with AccountId : {message.AggregateId}  , LocationId {message.Data.LocationId}");
            }
        }

        account.Modify(message);
    }

    private async Task<Account> HandleAccountConfirmed(Account? account, MessageBody<AccountConfirmedData> message)
    {
        if (!string.IsNullOrWhiteSpace(message.Data.LocationId))
        {
            var isRegionExists = await CheckIfRegionExists(message.Data.LocationId);

            if (!isRegionExists)
            {
                throw new AppException(ExceptionStatusCode.FailedPrecondition,
                    $"Location not found with AccountId : {message.AggregateId}  , LocationId {message.Data.LocationId}");
            }
        }

        if (account == null)
        {
            account = new Account(message);

            await _unitOfWork.Accounts.AddAsync(account);
        }
        else
        {
            account.Modify(message);
        }

        return account;
    }

    private bool IsSequenceInvalid<T>(Account account, MessageBody<T> message)
        => (account.Sequence != message.Sequence - 1);

    private async Task<bool> CheckIfRegionExists(string locationId)
        => await _unitOfWork.Regions.AnyAsync(locationId);

    private async Task<bool> CheckIfSubscriptionExists(string subscriptionId)
        => await _unitOfWork.Subscriptions.IsExistAsync(subscriptionId);
}