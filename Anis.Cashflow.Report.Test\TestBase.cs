﻿using Anis.Cashflow.Report.Application.Contracts.Services.GrpcService;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using Xunit.Abstractions;

[assembly: CollectionBehavior(DisableTestParallelization = TestBase.UseSqlDataBase)]
namespace Anis.Cashflow.Report.Test
{
    public abstract class TestBase
    {
        public const bool UseSqlDataBase = true;
        private GrpcChannel? _channel;
        private TestWebApplicationFactory<Program>? _factory;
        public const int Delay = 3000;

        protected TestBase(ITestOutputHelper output)
        {
            Output = output;
        }

        public ITestOutputHelper Output { get; }

        public GrpcChannel Channel
        {
            get
            {
                if (_channel != null)
                    return _channel;

                Initialize();
                return _channel ?? throw new Exception("return _channel");
            }

            private set => _channel = value;
        }
        protected TestWebApplicationFactory<Program> Factory
        {
            get
            {
                if (_factory != null)
                    return _factory;

                Initialize();
                return _factory ?? throw new Exception("return _factory");
            }

            private set => _factory = value;
        }

        public void Initialize(
            Action<IServiceCollection>? configureTopic = null,
            Action<IServiceCollection>? configureOther = null,
            decimal averageSaleLocation = default,
            List<WalletAverageSaleModel>? salesWalletAverage = null,
            List<string>? blackListSubscriptions = null
        )
        {
            if (configureTopic == null)
                configureTopic = services =>
                {
                    var mockGrpcClientService = new Mock<IGrpcClientService>();
                    var blackListSubscriptionsModel = new Mock<BlackListSubscriptionsModel>();

                    services.Remove(services.Single(s => s.ServiceType == typeof(IGrpcClientService)));
                    services.Remove(services.Single(s => s.ServiceType == typeof(BlackListSubscriptionsModel)));

                    mockGrpcClientService
                        .Setup(c => c.GetAverageSaleLocationAsync(It.IsAny<string>(), It.IsAny<int>())).Returns(Task.FromResult(averageSaleLocation));

                    if (salesWalletAverage is not null)
                        mockGrpcClientService
                         .Setup(c => c.GetAverageSaleWalletAsync(It.IsAny<List<string>>(), It.IsAny<int>())).Returns(Task.FromResult(salesWalletAverage));

                    else
                        mockGrpcClientService
                        .Setup(c => c.GetAverageSaleWalletAsync(It.IsAny<List<string>>(), It.IsAny<int>())).Returns(Task.FromResult(new List<WalletAverageSaleModel>()));

                    if (blackListSubscriptions is not null)
                        blackListSubscriptionsModel
                            .Setup(c => c.GetSubscriptionIds()).Returns(blackListSubscriptions);
                    else
                        blackListSubscriptionsModel
                          .Setup(c => c.GetSubscriptionIds()).Returns(new List<string>() { });

                    services.AddScoped(m => mockGrpcClientService.Object);
                    services.AddScoped(m => blackListSubscriptionsModel.Object);
                };

            void Configure(IServiceCollection services)
            {
                configureTopic?.Invoke(services);
                configureOther?.Invoke(services);
            }
            ;

            var factory = new TestWebApplicationFactory<Program>(Output, Configure);

            var client = factory.CreateClient();

            Channel = GrpcChannel.ForAddress(client.BaseAddress ?? throw new Exception(), new GrpcChannelOptions()
            {
                HttpClient = client,
            });

            Factory = factory;

            ResetDb();
        }

        private void ResetDb()
        {
#pragma warning disable CS8519 // The given expression never matches the provided pattern.
            if (UseSqlDataBase is false)
#pragma warning restore CS8519 // The given expression never matches the provided pattern.
                return;

            using var scope = Factory.Services.CreateScope();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            context.Database.Migrate();
            context.Accounts.RemoveRange(context.Accounts);
            context.Subscriptions.RemoveRange(context.Subscriptions);
            context.Wallets.RemoveRange(context.Wallets);
            context.Regions.RemoveRange(context.Regions);
            context.Holders.RemoveRange(context.Holders);
            context.SubscriptionWallets.RemoveRange(context.SubscriptionWallets);

            context.SaveChanges();
#pragma warning restore CS8520 // The given expression always matches the provided constant.
        }
    }
}
