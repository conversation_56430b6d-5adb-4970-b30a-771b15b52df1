﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.RegionsHandlers
{
    public class DeleteRegion : TestBase
    {
        public DeleteRegion(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Delete_EventArrivedWithValidSequence_EventHandled()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 2)
                                          .Generate();


            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<RegionDeletedData>()
            {
                AggregateId = aggregateId,
                Sequence = 3,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionDeleted,
                Data = new RegionDeletedData(),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            var region = await context.Regions.SingleAsync();

            Assert.True(isHandled);

            RegionAssert.AssertEquality(messageBody, region, createRegion, 3);
        }

        [Fact]
        public async Task Delete_EventArrivedWithNotValidSequence_EventNotHandled()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 2)
                                          .Generate();


            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<RegionDeletedData>()
            {
                AggregateId = aggregateId,
                Sequence = 4,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionDeleted,
                Data = new RegionDeletedData(),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            var region = await context.Regions.SingleAsync();

            Assert.False(isHandled);

            RegionAssert.AssertEquality(createRegion, region, 2);
        }

        [Fact]
        public async Task Delete_EventArrivedWithOldSequence_EventHandled()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 4)
                                          .Generate();


            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<RegionDeletedData>()
            {
                AggregateId = aggregateId,
                Sequence = 3,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionDeleted,
                Data = new RegionDeletedData(),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            var region = await context.Regions.SingleAsync();

            Assert.True(isHandled);

            RegionAssert.AssertEquality(createRegion, region, 4);
        }
    }
}
