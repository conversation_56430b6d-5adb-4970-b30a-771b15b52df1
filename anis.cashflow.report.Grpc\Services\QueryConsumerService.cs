using Anis.Cashflow.Report.Application.Contracts.Services.GrpcService;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Grpc.Extensions;
using Anis.Cashflow.Report.Grpc.Protos.Consumer;
using Grpc.Core;
using MediatR;

namespace Anis.Cashflow.Report.Grpc.Services
{
    public class QueryConsumerService : CashflowReportConsumerV5.CashflowReportConsumerV5Base
    {
        private readonly IMediator _mediator;
        private readonly IGrpcClientService _grpcClient;
        private readonly ConfigModel _config;

        public QueryConsumerService(IMediator mediator,
                                    IGrpcClientService grpcClient,
                                    ConfigModel config)
        {
            _mediator = mediator;
            _grpcClient = grpcClient;
            _config = config;
        }

        public async override Task<Response> Index(Request request, ServerCallContext context)
        {
            var days = request.DaysCount == 0 ? _config.DaysCount : request.DaysCount;

            var query = request.ToQuery();

            var response = await _mediator.Send(query);

            var walletIds = response.WalletDtos.Select(w => w.Id).ToList();

            var walletSaleAverage = await _grpcClient.GetAverageSaleWalletAsync(walletIds, days);

            var resultResponse = new Response
            {
                PageSize = response.PageSize,
                CurrentPage = response.CurrentPage,
                Total = response.Total
            };

            resultResponse.WalletDtos.ToOutputs(response.WalletDtos, walletSaleAverage);

            return resultResponse;
        }
    }
}
