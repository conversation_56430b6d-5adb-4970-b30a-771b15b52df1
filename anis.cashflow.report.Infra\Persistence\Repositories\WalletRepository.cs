﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Enums;
using Anis.Cashflow.Report.Application.Features.Management.CashflowFilter;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.Extensions;
using Anis.Cashflow.Report.Infra.Helpers;
using Anis.Cashflow.Report.Infra.Resources;
using Microsoft.EntityFrameworkCore;

namespace Anis.Cashflow.Report.Infra.Persistence.Repositories
{
    public class WalletRepository : AsyncRepository<Wallet>, IWalletRepository
    {
        public readonly AppDbContext _appDbContext;
        public WalletRepository(AppDbContext appDbContext) : base(appDbContext)
        {
            _appDbContext = appDbContext;
        }

        public async Task<Wallet?> FindAsync(string walletId)
          => await _appDbContext.Wallets.FirstOrDefaultAsync(x => x.Id == walletId);

        public async Task<bool> AnyAsync(string walletId)
          => await _appDbContext.Wallets.AnyAsync(x => x.Id == walletId);

        public async Task<CashFlowFilterResponse> FilterAsync(CashFlowFilterQuery request, BlackListSubscriptionsModel model, CancellationToken cancellationToken)
        {
            var skip = request.PageSize * (request.CurrentPage - 1);

            var location = await _appDbContext.Regions.FindAsync(request.LocationId);

            var ignoresIds = model.GetSubscriptionIds();

            var wallets = _appDbContext.Wallets.OrderByDescending(w => w.Subscription.Account.ExpiryAt <= DateTime.UtcNow)
                                               .ThenByDescending(w => w.CurrentDelayed)
                                               .ThenByDescending(w => w.CurrentUrgent)
                                               .ThenBy(w => w.Balance)
                                               .AsQueryable();

            if (location is not null)
            {
                if (location!.Code.Length == 2)
                    wallets = wallets.Where(w => w.Region.Code.StartsWith(location.Code.Substring(0, 2)));

                else
                {
                    wallets = wallets.Where(w => w.Subscription.Account.Location.Code.StartsWith(location.Code));

                    wallets = wallets.Where(w => w.Region.Code.StartsWith(location.Code.Substring(0, 2)));
                }
            }

            wallets = request.WalletType switch
            {
                WalletTypeFilter.Normal => wallets.Where(w => w.Type == WalletType.Normal),
                WalletTypeFilter.Profits => wallets.Where(w => w.Type == WalletType.Profits),
                _ => wallets
            };

            var totalNetBalance = await wallets.Where(i => !ignoresIds.Contains(i.SubscriptionId)).SumAsync(w => w.NetBalance);

            if (request.Type != Domain.Enums.FilterEnums.SubscriptionType.All)
            {
                wallets = wallets.Where(s => s.Subscription.Type == request.Type.ToSubscriptionType());
            }

            if (request.Currency != Domain.Enums.FilterEnums.CurrencyType.All)
            {
                wallets = wallets.Where(s => s.CurrencyType == request.Currency.ToCurrencyType());
            }

            if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
                wallets = wallets.Where(s => s.Subscription.Account.Phone == request.PhoneNumber);

            if (request.DebtOnly)
                wallets = wallets.Where(w => w.Balance < 0 || w.CurrentUrgent > 0 || w.CurrentDelayed > 0);

            wallets = request.CashAndHoldersFilter switch
            {
                CashAndHoldersFilter.CashOnly => wallets.Where(w => w.HolderId == null),
                CashAndHoldersFilter.HoldersOnly => wallets.Where(w => w.HolderId != null),
                CashAndHoldersFilter.SpecificHolder => wallets.Where(w => w.HolderId == request.HolderId),
                _ => wallets
            };

            if (request.ExpirationOnly)
            {
                wallets = wallets.Where(w => w.Subscription.Account.ExpiryAt <= DateTime.UtcNow);
            }

            var total = await wallets.CountAsync(cancellationToken);

            var result = await wallets.Select(w => new WalletDto
            (
                w.Id,
                w.Subscription.Name,
                w.Balance >= 0 ? w.Balance : 0,
                w.Balance < 0 ? w.Balance : 0,
                w.NetBalance,
                w.CurrentUrgent,
                w.CurrentDelayed,
                w.Subscription.Account.Phone,
                w.Subscription.Account.ExpiryAt.HasValue ? w.Subscription.Account.ExpiryAt.Value <= DateTime.UtcNow ?
                    CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState) :
                    CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState) :
                    CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState),
                    CultureHelper.Read(w.Subscription.Account.Location.ArabicName, w.Subscription.Account.Location.EnglishName),
                w.Subscription.Account.Location.ArabicName,
                w.Subscription.Account.Location.EnglishName,
                w.Subscription.Account.OwnerName,
                w.Subscription.Account.Email,
                WalletHelper.CreateWalletIdentifier(w, w.Subscription.Account, w.Subscription, w.Region, w.Holder),
                w.Region.Code,
                w.NetBalance >= 0,
                w.Subscription.Account.Location.Code,
                    w.Holder == null ? null :
                    CultureHelper.Read(w.Holder.ArabicName,
                    w.Holder.EnglishName),
                w.Subscription.Account.ConfirmedAt,
                w.LastDelayRefundDate,
                w.Subscription.Account.ExpiryAt.HasValue ?
                    w.Subscription.Account.ExpiryAt.Value > DateTime.UtcNow ?
                        GetRemainingTime(w.Subscription.Account.ExpiryAt.Value) :
                        null :
                    null
                )

            ).Skip(skip).Take(request.PageSize).ToListAsync(cancellationToken);

            return new CashFlowFilterResponse
            {
                CurrentPage = request.CurrentPage,
                PageSize = request.PageSize,
                Total = total,
                TotalNetBalance = totalNetBalance,
                WalletDtos = result
            };
        }

        private static string GetRemainingTime(DateTime expiryAt)
        {
            var remaining = expiryAt - DateTime.UtcNow;
            if (remaining.TotalMinutes <= 0) return "00h 00m";

            var hours = (int)remaining.TotalHours;
            var minutes = remaining.Minutes;
            return $"{hours:D2}h {minutes:D2}m";
        }
    }
}