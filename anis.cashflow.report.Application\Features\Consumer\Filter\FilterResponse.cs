namespace Anis.Cashflow.Report.Application.Features.Consumer.Filter
{
    public class FilterResponse
    {
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int Total { get; set; }
        public List<WalletDto> WalletDtos { get; set; } = new();
    }

    public record WalletDto(
    string Id,
    string SubscriptionName,
    string PhoneNumber,
    string AccountLocation,
    string WalletRegion,
    decimal Balance,
    decimal Delay,
    decimal Urgent,
    decimal NetBalance,
    DateTime? ConfirmedAt,
    DateTime? LastDelayRefundDate,
    string OperatorName,
    string AccountState,
    string? RemainingTime
    );
}