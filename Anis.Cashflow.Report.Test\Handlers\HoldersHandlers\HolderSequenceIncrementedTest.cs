﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Holders.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Holders;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.HoldersHandlers
{
    public class HolderSequenceIncrementedTest : TestBase
    {
        public HolderSequenceIncrementedTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task HolderSequenceIncremented_WhenHolderIsNotExist_ReturnFalse()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var message = new MessageBody<HolderSequenceIncrementedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.HolderDeleted,
                Data = new(),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(message);

            var dbHolders = await context.Holders.ToListAsync();

            //assert
            Assert.False(isHandled);

            Assert.Empty(dbHolders);
        }

        [Fact]
        public async Task HolderSequenceIncremented_WhenReceivedValidData_ReturnTurnAndHandleMessage()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderFaker = new HolderFaker().Generate();

            await context.Holders.AddAsync(holderFaker);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var message = new MessageBody<HolderSequenceIncrementedData>
            {
                AggregateId = holderFaker.Id,
                Sequence = 2,
                Type = EventType.HolderDeleted,
                Data = new(),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(message);

            var dbHolders = await context.Holders.ToListAsync();

            //assert
            Assert.True(isHandled);

            HolderAssert.AssertEquality(holderFaker, Assert.Single(dbHolders), sequence: 2);
        }

        [Theory]
        [InlineData(2)]
        [InlineData(3)]
        public async Task HolderSequenceIncremented_WhenReceivedSequenceIsLessOrEqualExistHandler_ReturnTurnAndIgnoreMessage(long sequence)
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderFaker = new HolderFaker()
                .RuleFor(h => h.Sequence, 3)
                .Generate();

            await context.Holders.AddAsync(holderFaker);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var message = new MessageBody<HolderSequenceIncrementedData>
            {
                AggregateId = holderFaker.Id,
                Sequence = sequence,
                Type = EventType.HolderDeleted,
                Data = new(),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(message);

            var dbHolders = await context.Holders.ToListAsync();

            //assert
            Assert.True(isHandled);

            HolderAssert.AssertEquality(holderFaker, Assert.Single(dbHolders));
        }

        [Fact]
        public async Task HolderSequenceIncremented_WhenReceivedSequenceIsGreaterThenExpected_ReturnFalse()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderFaker = new HolderFaker().Generate();

            await context.Holders.AddAsync(holderFaker);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var message = new MessageBody<HolderSequenceIncrementedData>
            {
                AggregateId = holderFaker.Id,
                Sequence = 3,
                Type = EventType.HolderDeleted,
                Data = new(),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(message);

            var dbHolders = await context.Holders.ToListAsync();

            //assert
            Assert.False(isHandled);

            HolderAssert.AssertEquality(holderFaker, Assert.Single(dbHolders));
        }
    }
}
