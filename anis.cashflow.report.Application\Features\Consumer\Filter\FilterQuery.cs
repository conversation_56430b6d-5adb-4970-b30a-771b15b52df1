﻿using Anis.Cashflow.Report.Application.Enums;
using Anis.Cashflow.Report.Domain.Enums.FilterEnums;
using MediatR;

namespace Anis.Cashflow.Report.Application.Features.Consumer.Filter;

public record FilterQuery(
    string LocationId,
    SubscriptionType Type,
    DebtType DebtType,
    string PhoneNumber,
    WalletTypeFilter WalletType,
    int CurrentPage,
    bool IsAscending,
    string? OperatorId,
    bool ExpirationOnly,
    int PageSize = 25) : IRequest<FilterResponse>;

