using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Enums;
using Anis.Cashflow.Report.Application.Features.Management.Filter;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.Enums.FilterEnums;
using Anis.Cashflow.Report.Domain.Extensions;
using Anis.Cashflow.Report.Infra.Helpers;
using Anis.Cashflow.Report.Infra.Resources;
using Microsoft.EntityFrameworkCore;

namespace Anis.Cashflow.Report.Infra.Persistence.Repositories;

public class SubscriptionWalletRepository : AsyncRepository<SubscriptionWallet>, ISubscriptionWalletRepository
{
    private readonly AppDbContext _appDbContext;

    public SubscriptionWalletRepository(AppDbContext appDbContext) : base(appDbContext)
    {
        _appDbContext = appDbContext;
    }

    public async Task<SubscriptionWallet?> GetBySubscriptionIdIdAndWalletIdAsync(string subscriptionId, string walletId)
        => await _appDbContext.SubscriptionWallets
                              .FirstOrDefaultAsync(c => c.SubscriptionId == subscriptionId && c.WalletId == walletId);

    public async Task<FilterResponse> FilterAsync(FilterQuery request, BlackListSubscriptionsModel model, CancellationToken cancellationToken)
    {
        var skip = request.PageSize * (request.CurrentPage - 1);

        var location = await _appDbContext.Regions.FindAsync(request.LocationId);

        var ignoresIds = model.GetSubscriptionIds();

        IQueryable<SubscriptionWallet> wallets = _appDbContext.SubscriptionWallets.AsQueryable();

        if (location is not null)
        {
            if (location!.Code.Length == 2)
                wallets = wallets.Where(w => w.Wallet.Region.Code.StartsWith(location.Code.Substring(0, 2)));

            else
            {
                wallets = wallets.Where(w => w.Wallet.Subscription.Account.Location.Code.StartsWith(location.Code));

                wallets = wallets.Where(w => w.Wallet.Region.Code.StartsWith(location.Code.Substring(0, 2)));
            }
        }

        if (request.OperatorId != null)
            wallets = wallets.Where(w => w.Subscription.AccountId == request.OperatorId);

        wallets = request.WalletType switch
        {
            WalletTypeFilter.Normal => wallets.Where(w => w.Wallet.Type == WalletType.Normal),
            WalletTypeFilter.Profits => wallets.Where(w => w.Wallet.Type == WalletType.Profits),
            _ => wallets
        };

        var totalNetBalance = await wallets.Where(i => !ignoresIds.Contains(i.Wallet.SubscriptionId))
                                           .SumAsync(w => w.Wallet.NetBalance);

        if (request.Type != Domain.Enums.FilterEnums.SubscriptionType.All)
        {
            wallets = wallets.Where(s => s.Wallet.Subscription.Type == request.Type.ToSubscriptionType());
        }

        if (request.Currency != Domain.Enums.FilterEnums.CurrencyType.All)
        {
            wallets = wallets.Where(s => s.Wallet.CurrencyType == request.Currency.ToCurrencyType());
        }

        if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
            wallets = wallets.Where(s => s.Wallet.Subscription.Account.Phone == request.PhoneNumber);

        if (request.DebtOnly)
            wallets = wallets.Where(w => w.Wallet.Balance < 0 || w.Wallet.CurrentUrgent > 0 || w.Wallet.CurrentDelayed > 0);

        wallets = request.CashAndHoldersFilter switch
        {
            CashAndHoldersFilter.CashOnly => wallets.Where(w => w.Wallet.HolderId == null),
            CashAndHoldersFilter.HoldersOnly => wallets.Where(w => w.Wallet.HolderId != null),
            CashAndHoldersFilter.SpecificHolder => wallets.Where(w => w.Wallet.HolderId == request.HolderId),
            _ => wallets
        };

        if (request.ShowStoresOnly)
        {
            var latestLinkIds = await wallets
                .Where(w => w.LinkDateTime != null)
                .GroupBy(w => w.WalletId)
                .Select(g => g.OrderByDescending(w => w.LinkDateTime).First().Id)
                .ToListAsync(cancellationToken);

            wallets = wallets.Where(sw => latestLinkIds.Contains(sw.Id));
        }

        wallets = wallets
       .OrderByDescending(w => w.Wallet.Subscription.Account.ExpiryAt.HasValue &&
                             w.Wallet.Subscription.Account.ExpiryAt.Value <= DateTime.UtcNow)
       .ThenByDescending(w => w.Wallet.CurrentDelayed)
       .ThenByDescending(w => w.Wallet.CurrentUrgent)
       .ThenBy(w => w.Wallet.Balance);

        var total = await wallets.CountAsync(cancellationToken);

        var result = await wallets.Select(w => new WalletDto
        (
            w.Wallet.Id,
            w.Wallet.Subscription.Name,
            w.Wallet.Balance >= 0 ? w.Wallet.Balance : 0,
            w.Wallet.Balance < 0 ? w.Wallet.Balance : 0,
            w.Wallet.NetBalance,
            w.Wallet.CurrentUrgent,
            w.Wallet.CurrentDelayed,
            w.Wallet.Subscription.Account.Phone,
            w.Wallet.Subscription.Account.ExpiryAt.HasValue ?
                w.Wallet.Subscription.Account.ExpiryAt.Value <= DateTime.UtcNow ?
                CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState)
                : CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState)
                : CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState),
            CultureHelper.Read(w.Wallet.Subscription.Account.Location.ArabicName, w.Wallet.Subscription.Account.Location.EnglishName),
            w.Wallet.Subscription.Account.Location.ArabicName,
            w.Wallet.Subscription.Account.Location.EnglishName,
            w.Wallet.Subscription.Account.OwnerName,
            w.Wallet.Subscription.Account.Email,
            WalletHelper.CreateWalletIdentifier(w.Wallet, w.Wallet.Subscription.Account, w.Wallet.Subscription, w.Wallet.Region, w.Wallet.Holder),
            w.Wallet.Region.Code,
            w.Wallet.NetBalance >= 0,
            w.Wallet.Subscription.Account.Location.Code,
            w.Wallet.Holder == null ? null :
                CultureHelper.Read(w.Wallet.Holder.ArabicName,
                w.Wallet.Holder.EnglishName),
            w.Wallet.Subscription.Account.ConfirmedAt,
            w.Wallet.LastDelayRefundDate,
            w.Subscription.Name
            )
        ).Skip(skip).Take(request.PageSize).ToListAsync(cancellationToken);


        return new FilterResponse
        {
            CurrentPage = request.CurrentPage,
            PageSize = request.PageSize,
            Total = total,
            TotalNetBalance = totalNetBalance,
            WalletDtos = result
        };
    }

    public async Task<Application.Features.Consumer.Filter.FilterResponse> FilterConsumerAsync(Application.Features.Consumer.Filter.FilterQuery request,
                                                                                               BlackListSubscriptionsModel model,
                                                                                               CancellationToken cancellationToken)
    {
        var skip = request.PageSize * (request.CurrentPage - 1);

        var location = await _appDbContext.Regions.FindAsync(request.LocationId);

        var wallets = _appDbContext.SubscriptionWallets.AsQueryable();

        if (location is not null)
        {
            if (location!.Code.Length == 2)
                wallets = wallets.Where(w => w.Wallet.Region.Code.StartsWith(location.Code.Substring(0, 2)));

            else
            {
                wallets = wallets.Where(w => w.Wallet.Subscription.Account.Location.Code.StartsWith(location.Code));

                wallets = wallets.Where(w => w.Wallet.Region.Code.StartsWith(location.Code.Substring(0, 2)));
            }
        }

        if (request.Type != Domain.Enums.FilterEnums.SubscriptionType.All)
            wallets = wallets.Where(s => s.Wallet.Subscription.Type == request.Type.ToSubscriptionType());

        if (request.OperatorId != null)
            wallets = wallets.Where(w => w.Subscription.AccountId == request.OperatorId);

        if (request.DebtType != DebtType.All)
        {
            switch (request.DebtType)
            {
                case DebtType.None:
                    {
                        wallets = wallets.Where(w => w.Wallet.Balance >= 0 && w.Wallet.CurrentUrgent == 0 && w.Wallet.CurrentDelayed == 0);
                        break;
                    }
                case DebtType.DebitOnly:
                    {
                        wallets = wallets.Where(w => w.Wallet.Balance < 0 || w.Wallet.CurrentUrgent > 0 || w.Wallet.CurrentDelayed > 0);
                        break;
                    }

                case DebtType.Urgent:
                    {
                        wallets = wallets.Where(w => w.Wallet.CurrentUrgent > 0);
                        break;
                    }

                case DebtType.Delayed:
                    {
                        wallets = wallets.Where(w => w.Wallet.CurrentDelayed > 0);
                        break;
                    }

                case DebtType.HasDebit:
                    {
                        wallets = wallets.Where(w => w.Wallet.Balance < 0);
                        break;
                    }
                default: break;
            }
        }

        wallets = request.WalletType switch
        {
            WalletTypeFilter.Normal => wallets.Where(w => w.Wallet.Type == WalletType.Normal),
            WalletTypeFilter.Profits => wallets.Where(w => w.Wallet.Type == WalletType.Profits),
            _ => wallets
        };

        if (!string.IsNullOrWhiteSpace(request.PhoneNumber))
            wallets = wallets.Where(s => s.Wallet.Subscription.Account.Phone == request.PhoneNumber);

        if (request.IsAscending)
            wallets = wallets.OrderBy(w => w.Wallet.Balance);

        else
            wallets = wallets.OrderByDescending(w => w.Wallet.Balance);

        if (request.ExpirationOnly)
        {
            wallets = wallets.Where(w => w.Subscription.Account.ExpiryAt <= DateTime.UtcNow);
        }

        var total = await wallets.CountAsync(cancellationToken);

        var result = await wallets.Select(w => new Application.Features.Consumer.Filter.WalletDto
        (
            w.Wallet.Id,
            w.Wallet.Subscription.Name,
            w.Wallet.Subscription.Account.Phone,
            CultureHelper.Read(w.Wallet.Subscription.Account.Location.ArabicName, w.Wallet.Subscription.Account.Location.EnglishName),
            CultureHelper.Read(w.Wallet.Region.ArabicName, w.Wallet.Region.EnglishName),
            w.Wallet.Balance,
            w.Wallet.CurrentDelayed,
            w.Wallet.CurrentUrgent,
            w.Wallet.NetBalance,
            w.Wallet.Subscription.Account.ConfirmedAt,
            w.Wallet.LastDelayRefundDate,
            w.Subscription.Name,
            w.Subscription.Account.ExpiryAt.HasValue ? w.Subscription.Account.ExpiryAt.Value <= DateTime.UtcNow ?
                    CultureHelper.Read(ResourceConst.ArDisabledState, ResourceConst.EnDisabledState) :
                    CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState) :
                    CultureHelper.Read(ResourceConst.ArEnabledState, ResourceConst.EnEnabledState),
            w.Subscription.Account.ExpiryAt.HasValue ?
                    w.Subscription.Account.ExpiryAt.Value > DateTime.UtcNow ?
                        GetRemainingTime(w.Subscription.Account.ExpiryAt.Value) :
                        null :
                    null
       )).Skip(skip).Take(request.PageSize)
       .ToListAsync(cancellationToken);

        return new Application.Features.Consumer.Filter.FilterResponse
        {
            CurrentPage = request.CurrentPage,
            PageSize = request.PageSize,
            Total = total,
            WalletDtos = result
        };
    }

    private static string GetRemainingTime(DateTime expiryAt)
    {
        var remaining = expiryAt - DateTime.UtcNow;
        if (remaining.TotalMinutes <= 0) return "00h 00m";

        var hours = (int)remaining.TotalHours;
        var minutes = remaining.Minutes;
        return $"{hours:D2}h {minutes:D2}m";
    }
}