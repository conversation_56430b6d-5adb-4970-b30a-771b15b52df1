﻿apiVersion: v1
kind: Namespace
metadata:
  name: ___SERVICE_NAMESPACE___
  labels:
    name: ___SERVICE_NAMESPACE___
---
apiVersion: v1
kind: Secret
metadata:
  name: ___SERVICE_NAME___
  namespace: ___SERVICE_NAMESPACE___
data:
  DatabaseConnectionString: ___CONNECTION_STRING___
  AccountsEcomlyServiceBusConnectionString: ___ACCOUNTS_SERVICE_BUS_CONNECTION_STRING___
  RegionsEcomlyServiceBusConnectionString: ___REGIONS_SERVICE_BUS_CONNECTION_STRING___
  WalletsEcomlyServiceBusConnectionString: ___WALLETS_SERVICE_BUS_CONNECTION_STRING___
  HoldersEcomlyServiceBusConnectionString: ___HOLDERS_SERVICE_BUS_CONNECTION_STRING___

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ___SERVICE_NAME___
  namespace: ___SERVICE_NAMESPACE___
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: ___SERVICE_NAME___
  template:
    metadata:
      labels:
        app: ___SERVICE_NAME___
    spec:
      nodeSelector:
        "kubernetes.io/os": linux
      containers:
      - name: ___SERVICE_NAME___
        image: ___CONTAINER_REGISTRY___.azurecr.io/___SERVICE_NAME___:___IMAGE_TAG___
        ports:
        - containerPort: 80
        env:

        ## Environment
        - name: ASPNETCORE_ENVIRONMENT
          value: Production

        ## Database
        - name: ConnectionStrings__AppConnectionString
          valueFrom:
            secretKeyRef:
              name: ___SERVICE_NAME___
              key: DatabaseConnectionString
              
        ## ServiceBus
        - name: ServiceBus__AccountsServiceBusConnectionString
          valueFrom:
            secretKeyRef:
              name: ___SERVICE_NAME___
              key: AccountsEcomlyServiceBusConnectionString
        
        - name: ServiceBus__RegionsServiceBusConnectionString
          valueFrom:
            secretKeyRef:
              name: ___SERVICE_NAME___
              key: RegionsEcomlyServiceBusConnectionString
        
        - name: ServiceBus__WalletsServiceBusConnectionString
          valueFrom:
            secretKeyRef:
              name: ___SERVICE_NAME___
              key: WalletsEcomlyServiceBusConnectionString

        - name: ServiceBus__HoldersServiceBusConnectionString
          valueFrom:
            secretKeyRef:
              name: ___SERVICE_NAME___
              key: HoldersEcomlyServiceBusConnectionString
      
        - name: ServiceBus__RegionTopic
          value: "ecom-cards"
          ##value: ""

        - name: ServiceBus__RegionSubscription
          value: "anis-cashflow-report-v2"
          
        - name: ServiceBus__AccountTopic
          value: "ecom-cards-accounts"
          ##value: ""

        - name: ServiceBus__AccountSubscription
          value: "anis-cashflow-report-v2"

        - name: ServiceBus__WalletTopic
          value: "ecom-cards-wallets"
          ##value: ""

        - name: ServiceBus__WalletSubscription
          value: "anis-cashflow-report-v2"

        - name: ServiceBus__HolderTopic
          value: "anis-holders"

        - name: ServiceBus__HolderSubscription
          value: "anis-holder-cashflow-report-v2"
          
        - name: ServiceBus__OperatorBusinessLinkTopic
          value: "operator-to-business-link-service"

        - name: ServiceBus__OperatorBusinessLinkSubscription
          value: "anis-cashflow-report-v4"

        - name: ServiceBus__IsDeadLetterEnabled
          value: "true"
        
        ## Client urls
        - name: ClientUrls__GateWayEventsHistoryClient
          value: "http://ecom-cards-grpc-replica.anis-gateway"

        - name: ClientUrls__DailyLocationSaleClient
          value: "http://anis-daily-location-sales.anis-sales-reports"
          
        - name: ClientUrls__DailyWalletSaleClient
          value: "http://anis-daily-wallet-sales.anis-sales-reports"

        - name: ClientUrls__HoldersClient
          value: "http://anis-holders.anis-holders"
          
        - name: ClientUrls__NotificationOperatorBusinessLinkClient
          value: "http://anis-operator-to-business-link-service.anis-operator-to-business-link-service"

         ## Config
        - name: Config__DaysCount
          value: "7"
           
        - name: Config__ResetDataBase
          value: "True"
           
        ## Serilog
        - name: Serilog__SeqUrl
          value: "http://seq.logger"

        - name: Serilog__AppName
          value: "___SERVICE_NAME___"
---
apiVersion: v1
kind: Service
metadata:
  name: ___SERVICE_NAME___
  namespace: ___SERVICE_NAMESPACE___
spec:
  type: ClusterIP
  ports:
  - port: 80
  selector:
    app: ___SERVICE_NAME___
---
 
