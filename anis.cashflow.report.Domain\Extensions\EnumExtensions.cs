﻿namespace Anis.Cashflow.Report.Domain.Extensions
{
    public static class EnumExtensions
    {
        public static Enums.SubscriptionType ToSubscriptionType(this Enums.FilterEnums.SubscriptionType subscriptionType)
            => subscriptionType switch
            {
                Enums.FilterEnums.SubscriptionType.Operator => Enums.SubscriptionType.Operator,
                Enums.FilterEnums.SubscriptionType.Business => Enums.SubscriptionType.Business,
                Enums.FilterEnums.SubscriptionType.Normal => Enums.SubscriptionType.Normal,
                _ => throw new NotImplementedException("SubscriptionType not implemented ."),
            };

        public static Enums.CurrencyType ToCurrencyType(this Enums.FilterEnums.CurrencyType currencyType)
            => currencyType switch
            {
                Enums.FilterEnums.CurrencyType.Usd => Enums.CurrencyType.Usd,
                Enums.FilterEnums.CurrencyType.Lyd => Enums.CurrencyType.Lyd,
                _ => throw new NotImplementedException("CurrencyType not implemented ."),
            };
    }
}
