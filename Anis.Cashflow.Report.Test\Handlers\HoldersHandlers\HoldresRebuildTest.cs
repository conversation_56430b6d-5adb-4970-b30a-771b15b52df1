﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Application.Extensions;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Holders.DataTypes;
using Anis.Cashflow.Report.Domain.Exceptions;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Extensions;
using Anis.Cashflow.Report.Test.Fakers.Holders;
using Anis.Cashflow.Report.Test.Fakers.Holders.EventsData;
using MediatR;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.Azure.Amqp.Framing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.VisualStudio.TestPlatform.CommunicationUtilities;
using System.Diagnostics.Tracing;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.HoldersHandlers
{
    public class HoldresRebuildTest : TestBase
    {
        public HoldresRebuildTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task HoldresRebuild_WhenReceivedValidMessages_SuccessHandleMessages()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderCreatedDataFaker = new HolderCreatedDataFaker().Generate();

            var firstMessage = new MessageBody<string>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 1,
                UserId = Guid.NewGuid().ToString(),
                Data = holderCreatedDataFaker.Serialize(),
                Type = EventType.HolderCreated,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var nameChangedDataFaker = new NameChangedDataFaker().Generate();

            var secondMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 2,
                UserId = Guid.NewGuid().ToString(),
                Data = nameChangedDataFaker.Serialize(),
                Type = EventType.NameChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var thirdMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 3,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionAdded,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var forthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 4,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionDifferenceChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var fifthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 5,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionUnLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var sixthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 6,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var seventhMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 7,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionRemoved,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var eightMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 8,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.HolderDeleted,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var messages = new List<MessageBody<string>>()
            {
                firstMessage,
                secondMessage,
                thirdMessage,
                forthMessage,
                fifthMessage,
                sixthMessage,
                seventhMessage,
                eightMessage,
            };

            var holdersRebuildModle = new HoldersRebuildModel(Messages: messages);

            await mediator.Send(holdersRebuildModle);

            var dbHolder = await context.Holders.SingleAsync();

            HolderAssert.AssertEquality(secondMessage.ToMessageBody<NameChangedData>(), dbHolder, 8);
        }

        [Fact]
        public async Task HoldresRebuild_WhenReceivedValidMessagesAndHolderAlreadyExist_SuccessHandleMessages()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderFaker = new HolderFaker().Generate();

            await context.Holders.AddAsync(holderFaker);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var holderCreatedDataFaker = new HolderCreatedDataFaker().Generate();

            var firstMessage = new MessageBody<string>
            {
                AggregateId = holderFaker.Id,
                Sequence = 1,
                UserId = Guid.NewGuid().ToString(),
                Data = holderCreatedDataFaker.Serialize(),
                Type = EventType.HolderCreated,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var nameChangedDataFaker = new NameChangedDataFaker().Generate();

            var secondMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 2,
                UserId = Guid.NewGuid().ToString(),
                Data = nameChangedDataFaker.Serialize(),
                Type = EventType.NameChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var thirdMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 3,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionAdded,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var forthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 4,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionDifferenceChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var fifthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 5,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionUnLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var sixthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 6,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var seventhMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 7,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionRemoved,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var eightMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 8,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.HolderDeleted,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var messages = new List<MessageBody<string>>()
            {
                firstMessage,
                secondMessage,
                thirdMessage,
                forthMessage,
                fifthMessage,
                sixthMessage,
                seventhMessage,
                eightMessage,
            };

            var holdersRebuildModle = new HoldersRebuildModel(Messages: messages);

            await mediator.Send(holdersRebuildModle);

            var dbHolder = await context.Holders.SingleAsync();

            HolderAssert.AssertEquality(secondMessage.ToMessageBody<NameChangedData>(), dbHolder, 8);
        }

        [Fact]
        public async Task HoldresRebuild_WhenReceivedMessagesButHolderCreatedMessageIsNotExistAndItIsNotExistInDataBase_NotHandleMessages()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var nameChangedDataFaker = new NameChangedDataFaker().Generate();

            var secondMessage = new MessageBody<string>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                UserId = Guid.NewGuid().ToString(),
                Data = nameChangedDataFaker.Serialize(),
                Type = EventType.NameChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var thirdMessage = new MessageBody<string>
            {
                AggregateId = secondMessage.AggregateId,
                Sequence = 3,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionAdded,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var forthMessage = new MessageBody<string>
            {
                AggregateId = secondMessage.AggregateId,
                Sequence = 4,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionDifferenceChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var fifthMessage = new MessageBody<string>
            {
                AggregateId = secondMessage.AggregateId,
                Sequence = 5,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionUnLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var sixthMessage = new MessageBody<string>
            {
                AggregateId = secondMessage.AggregateId,
                Sequence = 6,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var seventhMessage = new MessageBody<string>
            {
                AggregateId = secondMessage.AggregateId,
                Sequence = 7,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionRemoved,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var eightMessage = new MessageBody<string>
            {
                AggregateId = secondMessage.AggregateId,
                Sequence = 8,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.AccountConfirmed,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var messages = new List<MessageBody<string>>()
            {
                secondMessage,
                thirdMessage,
                forthMessage,
                fifthMessage,
                sixthMessage,
                seventhMessage,
                eightMessage,
            };

            var holdersRebuildModle = new HoldersRebuildModel(Messages: messages);

            var exception = await Assert.ThrowsAsync<AppException>(async () => await mediator.Send(holdersRebuildModle));

            Assert.NotEmpty(exception.Message);

            Assert.Equal(ExceptionStatusCode.Aborted, exception.StatusCode);

            Assert.Equal($"Could not find the in the database nor create it from message body, holder id: {secondMessage.AggregateId}", exception.Message);

            var dbHolder = await context.Holders.SingleOrDefaultAsync();

            Assert.Null(dbHolder);
        }

        [Fact]
        public async Task HoldresRebuild_WhenReceivedValidMessagesAndHolderAlreadyExistAndThereAreMessagesThemSequenceIsLessThenExist_SuccessHandleMessages()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderFaker = new HolderFaker()
                .RuleFor(h => h.Sequence, 3)
                .Generate();

            await context.Holders.AddAsync(holderFaker);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var holderCreatedDataFaker = new HolderCreatedDataFaker().Generate();

            var firstMessage = new MessageBody<string>
            {
                AggregateId = holderFaker.Id,
                Sequence = 1,
                UserId = Guid.NewGuid().ToString(),
                Data = holderCreatedDataFaker.Serialize(),
                Type = EventType.HolderCreated,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var nameChangedDataFaker = new NameChangedDataFaker().Generate();

            var secondMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 2,
                UserId = Guid.NewGuid().ToString(),
                Data = nameChangedDataFaker.Serialize(),
                Type = EventType.NameChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var thirdMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 3,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionAdded,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var forthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 4,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionDifferenceChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var fifthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 5,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionUnLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var sixthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 6,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var seventhMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 7,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionRemoved,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var eightMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 8,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.HolderDeleted,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var messages = new List<MessageBody<string>>()
            {
                firstMessage,
                secondMessage,
                thirdMessage,
                forthMessage,
                fifthMessage,
                sixthMessage,
                seventhMessage,
                eightMessage,
            };

            var holdersRebuildModle = new HoldersRebuildModel(Messages: messages);

            await mediator.Send(holdersRebuildModle);

            var dbHolder = await context.Holders.SingleAsync();

            HolderAssert.AssertEquality(holderFaker, dbHolder, 8);
        }

        [Fact]
        public async Task HoldresRebuild_WhenReceivedMessagesWithGabInSequence_NotHandleMessages()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderCreatedDataFaker = new HolderCreatedDataFaker().Generate();

            var firstMessage = new MessageBody<string>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 1,
                UserId = Guid.NewGuid().ToString(),
                Data = holderCreatedDataFaker.Serialize(),
                Type = EventType.HolderCreated,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var nameChangedDataFaker = new NameChangedDataFaker().Generate();

            var secondMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 2,
                UserId = Guid.NewGuid().ToString(),
                Data = nameChangedDataFaker.Serialize(),
                Type = EventType.NameChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var thirdMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 3,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionAdded,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var forthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 4,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionDifferenceChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var fifthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 5,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionUnLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var sixthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 6,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var seventhMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 7,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionRemoved,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var eightMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 9,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.HolderDeleted,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var messages = new List<MessageBody<string>>()
            {
                firstMessage,
                secondMessage,
                thirdMessage,
                forthMessage,
                fifthMessage,
                sixthMessage,
                seventhMessage,
                eightMessage,
            };

            var holdersRebuildModle = new HoldersRebuildModel(Messages: messages);

            var exception = await Assert.ThrowsAsync<AppException>( async () => await mediator.Send(holdersRebuildModle));

            Assert.NotEmpty(exception.Message);

            Assert.Equal(ExceptionStatusCode.Aborted, exception.StatusCode);

            Assert.Equal($"Message sequence {eightMessage.Sequence} greater than holder sequence {seventhMessage.Sequence}", exception.Message);

            var dbHolder = await context.Holders.SingleOrDefaultAsync();

            Assert.Null(dbHolder);
        }

        [Fact]
        public async Task HoldresRebuild_WhenReceivedMessagesAndHaveMessageItIsTypeNotHandlet_NotHandleMessages()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderCreatedDataFaker = new HolderCreatedDataFaker().Generate();

            var firstMessage = new MessageBody<string>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 1,
                UserId = Guid.NewGuid().ToString(),
                Data = holderCreatedDataFaker.Serialize(),
                Type = EventType.HolderCreated,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var nameChangedDataFaker = new NameChangedDataFaker().Generate();

            var secondMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 2,
                UserId = Guid.NewGuid().ToString(),
                Data = nameChangedDataFaker.Serialize(),
                Type = EventType.NameChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var thirdMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 3,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionAdded,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var forthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 4,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionDifferenceChanged,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var fifthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 5,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionUnLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var sixthMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 6,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionLocked,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var seventhMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 7,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.RegionRemoved,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var eightMessage = new MessageBody<string>
            {
                AggregateId = firstMessage.AggregateId,
                Sequence = 8,
                UserId = Guid.NewGuid().ToString(),
                Data = string.Empty,
                Type = EventType.AccountConfirmed,
                DateTime = DateTime.UtcNow,
                Version = 1
            };

            var messages = new List<MessageBody<string>>()
            {
                firstMessage,
                secondMessage,
                thirdMessage,
                forthMessage,
                fifthMessage,
                sixthMessage,
                seventhMessage,
                eightMessage,
            };

            var holdersRebuildModle = new HoldersRebuildModel(Messages: messages);

            var exception = await Assert.ThrowsAsync<AppException>(async () => await mediator.Send(holdersRebuildModle));

            Assert.NotEmpty(exception.Message);

            Assert.Equal(ExceptionStatusCode.OutOfRange, exception.StatusCode);

            Assert.Equal($"Event type out of range in rebuild service => {eightMessage.Type}", exception.Message);

            var dbHolder = await context.Holders.SingleOrDefaultAsync();

            Assert.Null(dbHolder);
        }
    }
}