﻿using Anis.Cashflow.Report.Grpc.Protos.Managmant;
using Anis.Cashflow.Report.Grpc.Resources;
using FluentValidation;

namespace Anis.Cashflow.Report.Grpc.Validatiors
{
    public class FilterRequestValidator : AbstractValidator<Request>
    {
        public FilterRequestValidator()
        {//ToDo remove 
            //RuleFor(c => c.LocationId)
            //    .Must(locationId => Guid.TryParse(locationId, out _))
            //    .WithName(Titles.LocationId)
            //    .WithMessage(Phrases.LocationIdNotSend);

            RuleFor(r => r).Must(ValidateSpecificHolder)
                                    .WithName(Titles.HoldersId)
                                    .WithMessage(Phrases.HolderMustBeSelected);
        }

        private bool ValidateSpecificHolder(Request request)
        {
            if (request.CashAndHoldersFilter != CashAndHoldersFilter.SpecificHolder)
                return true;

            return Guid.TryParse(request.HolderId, out _) && request.HolderId != Guid.Empty.ToString();
        }
    }
}
