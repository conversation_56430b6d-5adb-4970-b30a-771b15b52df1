﻿using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;

namespace Anis.Cashflow.Report.Test.Fakers.Wallets.EventsData
{
    public class TransactionCreatedDataFaker : PrivateFaker<TransactionCreatedData>
    {
        public TransactionCreatedDataFaker()
        {
            UsePrivateConstructor();

            RuleFor(f => f.EventId, Guid.NewGuid().ToString());
            RuleFor(f => f.ReferenceId, Guid.NewGuid().ToString());
            RuleFor(f => f.Debit, 0);
            RuleFor(f => f.Credit, 0);
            RuleFor(f => f.Value, 0);
            RuleFor(f => f.GlobalId, Guid.NewGuid().ToString());
            RuleFor(f => f.UserId, Guid.NewGuid().ToString());
            RuleFor(f => f.UserId, Guid.NewGuid().ToString());
        }

    }
}