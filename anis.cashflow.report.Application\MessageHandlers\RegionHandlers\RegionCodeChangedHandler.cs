﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.RegionHandlers
{
    public class RegionCodeChangedHandler : IRequestHandler<MessageBody<RegionCodeChangedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public RegionCodeChangedHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> Handle(MessageBody<RegionCodeChangedData> request, CancellationToken cancellationToken)
        {
            var region = await _unitOfWork.Regions.FindAsync(request.AggregateId);

            if (region == null) return false;

            if (region.Sequence != request.Sequence - 1)
                return region.Sequence >= request.Sequence;

            region.ChangeCode(request);

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}