﻿using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;

namespace Anis.Cashflow.Report.Domain.Entities
{
    public class Subscription
    {
        private Subscription() { }

        public Subscription(string accountId, SubscriptionModel model)
        {
            Id = model.Id;
            Name = model.Name;
            Type = model.Type;
            AccountId = accountId;
        }

        public Subscription(MessageBody<SubscriptionCreatedData> model)
        {
            Id = model.Data.Id;
            Name = model.Data.Name;
            Type = model.Data.Type;
            AccountId = model.AggregateId;
        }

        public string Id { get; private set; }
        public string Name { get; private set; }
        public SubscriptionType Type { get; private set; }
        public int StateVersion { get; private set; }
        public string AccountId { get; private set; }
        public Account Account { get; private set; }

        private HashSet<Wallet> _wallets = new HashSet<Wallet>();
        public IReadOnlyCollection<Wallet> Wallets => _wallets;

        private HashSet<SubscriptionWallet> _subscriptionWallets = new HashSet<SubscriptionWallet>();
        public IReadOnlyCollection<SubscriptionWallet> SubscriptionWallets => _subscriptionWallets;

        public void Update(MessageBody<SubscriptionUpdatedData> model)
        {
            Name = model.Data.Name;
        }

        public void IncrementStateVersion()
        {
            StateVersion++;
        }

        public void UpdateStateVersion(int stateVersion)
        {
            StateVersion = stateVersion;
        }

        public void ReplaceSubscriptionWallets(HashSet<SubscriptionWallet> subscriptionWallets, int stateVersion)
        {
            _subscriptionWallets = subscriptionWallets;
            StateVersion = stateVersion;
        }
    }
}