﻿namespace Anis.Cashflow.Report.Application.Features.Management.CashflowFilter
{
    public class CashFlowFilterResponse
    {
        public int PageSize { get; set; }
        public int CurrentPage { get; set; }
        public int Total { get; set; }
        public List<WalletDto> WalletDtos { get; set; } = new();
        public decimal TotalNetBalance { get; set; }
    }

    public record WalletDto(
        string Id,
        string SubscriptionName,
        decimal Credit,
        decimal Debit,
        decimal NetBalance,
        decimal Urgent,
        decimal Delay,
        string PhoneNumber,
        string State,
        string Location,
        string ArabicLocationName,
        string EnglishLocationName,
        string Owner,
        string? Email,
        string WalletIdentifier,
        string WalletRegion,
        bool IsNormal,
        string Code,
        string? HolderName,
        DateTime? ConfirmedAt,
        DateTime? LastDelayRefundDate,
        string? RemainingTime);
}
