﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Regions.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.RegionsHandlers
{
    public class CreateRegion : TestBase
    {
        public CreateRegion(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task Add_RegionAdded_EventHandled()
        {
            using var scope = Factory.Services.CreateScope();


            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var messageBody = new MessageBody<RegionCreatedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 1,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionCreated,
                Data = new RegionCreatedDataFaker().Generate(),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            var region = await context.Regions.SingleAsync();

            Assert.True(isHandled);

            // Data assert 
            RegionAssert.AssertEquality(messageBody, region, 1);
        }

        [Fact]
        public async Task Add_RegionAlradyExited_EventNotHandled()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Code, Guid.NewGuid().ToString())
                                          .RuleFor(f => f.Sequence, 1)
                                          .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<RegionCreatedData>()
            {
                AggregateId = aggregateId,
                Sequence = 1,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionCreated,
                Data = new RegionCreatedDataFaker().RuleFor(f => f.Code, createRegion.Code).Generate(),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            var dbRegion = await context.Regions.SingleAsync();

            Assert.True(isHandled);

            RegionAssert.AssertEquality(createRegion, dbRegion, 1);
        }
    }
}
