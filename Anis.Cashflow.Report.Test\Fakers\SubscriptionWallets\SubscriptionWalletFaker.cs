﻿using Anis.Cashflow.Report.Domain.Entities;

namespace Anis.Cashflow.Report.Test.Fakers.SubscriptionWallets
{
    public sealed class SubscriptionWalletFaker : PrivateFaker<SubscriptionWallet>
    {
        public SubscriptionWalletFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.WalletId, f => f.Random.String());
            RuleFor(r => r.SubscriptionId, f => f.Random.String());
            RuleFor(r => r.LinkDateTime, f => f.Date.PastOffset(1).DateTime);
        }
    }
}
