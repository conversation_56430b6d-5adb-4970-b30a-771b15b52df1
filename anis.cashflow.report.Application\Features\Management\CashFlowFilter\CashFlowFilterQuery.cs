﻿using Anis.Cashflow.Report.Application.Enums;
using Anis.Cashflow.Report.Domain.Enums.FilterEnums;
using MediatR;

namespace Anis.Cashflow.Report.Application.Features.Management.CashflowFilter
{
    public record CashFlowFilterQuery(
        string LocationId,
        SubscriptionType Type,
        CurrencyType Currency,
        string PhoneNumber,
        bool DebtOnly,
        int CurrentPage,
        WalletTypeFilter WalletType,
        CashAndHoldersFilter CashAndHoldersFilter,
        string? HolderId,
        bool ExpirationOnly,
        int PageSize = 25) : IRequest<CashFlowFilterResponse>;
}
