﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Live.Asserts;
using Anis.Cashflow.Report.Test.Live.Protos;
using Google.Protobuf.WellKnownTypes;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.AccountEvents
{
    public class AccountUnnecessaryEventTest : TestBase
    {
        public AccountUnnecessaryEventTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Create_UnnecessaryEventWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
             .Generate();

            var createAccount = new AccountFaker()
             .RuleFor(f => f.LocationId, createRegion.Id)
             .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var channel = GrpcChannel.ForAddress(Address.EcomCards);

            var grpcClient = new AccountsDemoEvents.AccountsDemoEventsClient(channel);

            var request = new UnconfirmeAccountRequest
            {
                AggregateId = createAccount.Id,
                DateTime = DateTime.UtcNow.ToTimestamp(),
                Sequence = 2,
            };

            await grpcClient.UnconfirmeAccountAsync(request);

            await Task.Delay(Delay);

            var dbAccount = await context.Accounts.SingleOrDefaultAsync();

            DemoEventAsserts.AssertEquality(request, dbAccount);
        }
    }
}
