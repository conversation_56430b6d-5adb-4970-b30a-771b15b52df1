﻿using MediatR;

namespace Anis.Cashflow.Report.Application.Models
{
    public record OperatorToBusinessLinkRebuildModel() : IRequest<Unit>
    {
        public string SubscriptionId { get; set; } = string.Empty;
        public int StateVersion { get; set; }
        public int DataVersion { get; set; }
        public int BuildVersion { get; set; }
        public List<SubscriptionWalletBuild> SubscriptionWallets { get; set; } = new List<SubscriptionWalletBuild>();
    }

    public record SubscriptionWalletBuild
    {
        public string WalletId { get; set; } = string.Empty;

        public DateTime LinkDateTime { get; set; }
    }
}
