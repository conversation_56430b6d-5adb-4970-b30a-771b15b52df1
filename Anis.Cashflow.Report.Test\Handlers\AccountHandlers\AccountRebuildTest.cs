﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Exceptions;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Extensions;
using Anis.Cashflow.Report.Test.Fakers.Accounts.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.AccountHandlers;

public class AccountRebuildTest : TestBase
{
    public AccountRebuildTest(ITestOutputHelper output) : base(output)
    { }

    [Fact]
    public async Task AccountRebuild_WhenGivenValidRebuildModelAndAccountNotFoundInDbButItsEventExist_HandleSuccessfully()
    {
        using var scope = Factory.Services.CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var fakerRegion = new RegionFaker()
            .RuleFor(c => c.Sequence, 1)
            .Generate();

        await dbContext.Regions.AddAsync(fakerRegion);

        await dbContext.SaveChangesAsync();
        
        dbContext.ChangeTracker.Clear();
        
        List<MessageBody<string>> listMessageBodies = new(14);
        
        var firstAccountEvent = new AccountConfirmedDataFaker()
            .RuleFor(a => a.LocationId, fakerRegion.Id)
            .Generate();

        var firstMessageBody = new MessageBody<string>()
        {
            AggregateId = Guid.NewGuid().ToString(),
            Sequence = 1,
            Type = EventType.AccountConfirmed,
            Data = firstAccountEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var secondMessageEvent = new SubscriptionCreatedDataFaker().Generate();
        
        var secondMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 2,
            Type = EventType.SubscriptionCreated,
            Data = secondMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var thirdMessageEvent = AccountUpdatedDataFaker.Create(fakerRegion.Id);
        
        var thirdMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 3,
            Type = EventType.AccountUpdated,
            Data = thirdMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var fourthMessageEvent = new AccountPhoneNumberChangedDataFaker()
            .RuleFor(a => a.Phone, "**********")
            .Generate();
        
        var fourthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 4,
            Type = EventType.AccountPhoneNumberChanged,
            Data = fourthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fifthEventMessage = AccountEmailChangedDataFaker.Create();
        
        var fifthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 5,
            Type = EventType.AccountEmailChanged,
            Data = fifthEventMessage.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var sixthMessageEvent = new SequenceIncrementedData();
        
        var sixthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 6,
            Type = EventType.AccountUnconfirmed,
            Data = sixthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var seventhMessageEvent = new SequenceIncrementedData();
        
        var seventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 7,
            Type = EventType.AccountPriceDifferenceChanged,
            Data = seventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var eighthMessageEvent = new ExpiryDateChangedData(DateTime.UtcNow.AddDays(1));
        
        var eighthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 8,
            Type = EventType.AccountExpirationChanged,
            Data = eighthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var ninthMessageEvent = new ExpiryDateRemovedData();
        
        var ninthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 9,
            Type = EventType.AccountExpirationRemoved,
            Data = ninthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var tenthMessageEvent = new SubscriptionUpdatedDataFaker()
            .RuleFor(r => r.Id, secondMessageEvent.Id)
            .Generate();
        
        var tenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 10,
            Type = EventType.SubscriptionUpdated,
            Data = tenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var eleventhMessageEvent = new SubscriptionRemovedData(secondMessageEvent.Id);
        
        var eleventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 11,
            Type = EventType.SubscriptionRemoved,
            Data = eleventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var twelvethMessageEvent = new SequenceIncrementedData();
        
        var twelvethMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 12,
            Type = EventType.AniscomActivated,
            Data = twelvethMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var thirteenthMessageEvent = new SequenceIncrementedData();
        
        var thirteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 13,
            Type = EventType.AniscomDeactivated,
            Data = thirteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fourteenthMessageEvent = new SequenceIncrementedData();
        var fourteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 14,
            Type = EventType.AccountDeleted,
            Data = fourteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        listMessageBodies.Add(firstMessageBody);
        listMessageBodies.Add(secondMessageBody);
        listMessageBodies.Add(thirdMessageBody);
        listMessageBodies.Add(fourthMessageBody);
        listMessageBodies.Add(fifthMessageBody);
        listMessageBodies.Add(sixthMessageBody);
        listMessageBodies.Add(seventhMessageBody);
        listMessageBodies.Add(eighthMessageBody);
        listMessageBodies.Add(ninthMessageBody);
        listMessageBodies.Add(tenthMessageBody);
        listMessageBodies.Add(eleventhMessageBody);
        listMessageBodies.Add(twelvethMessageBody);
        listMessageBodies.Add(thirteenthMessageBody);
        listMessageBodies.Add(fourteenthMessageBody);

        var request = new AccountRebuildModel(listMessageBodies);

        await mediator.Send(request);

        var firstDbSubscription = await dbContext.Subscriptions.SingleAsync(c => c.Id == firstAccountEvent.Subscriptions.First().Id);
        
        var secondDbSubscription = await dbContext.Subscriptions.SingleAsync(c => c.Id == secondMessageEvent.Id);

        var dbAccount = await dbContext.Accounts.SingleAsync();

        var dbRegion = await dbContext.Regions.SingleAsync();

        var fakerSecondSubscription = new SubscriptionModel()
        {
            Id = secondMessageEvent.Id,
            Name = tenthMessageEvent.Name,
            Type = secondMessageEvent.Type,
        };

        EntityAssert.AssertEquality(
            subscriptionData: firstAccountEvent.Subscriptions.First(),
            dbSubscription: firstDbSubscription,
            firstMessageBody.AggregateId);

        EntityAssert.AssertEquality(
            subscriptionData: fakerSecondSubscription,
            dbSubscription: secondDbSubscription,
            firstMessageBody.AggregateId);
        
        RegionAssert.AssertEquality(fakerRegion, dbRegion);
        
        Assert.Equal(firstMessageBody.AggregateId, dbAccount.Id);
        
        Assert.Equal(fourthMessageEvent.Phone, dbAccount.Phone);
        
        Assert.Equal(fakerRegion.Id, dbAccount.LocationId);
        
        Assert.Equal(fourteenthMessageBody.Sequence, dbAccount.Sequence);
    }
    
    [Fact]
    public async Task AccountRebuild_WhenOnAccountSubscriptionUpdateTheSubscriptionNotFoundInDatabaseButFoundInTheAccountInMemoryListOfSubscriptions_HandleSuccessfully()
    {
        using var scope = Factory.Services.CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var fakerRegion = new RegionFaker()
            .RuleFor(c => c.Sequence, 1)
            .Generate();
        
        var fakerAccount = new AccountFaker()
            .RuleFor(c => c.Sequence, 1)
            .RuleFor(c => c.Number, "*********")
            .RuleFor(c => c.LocationId, fakerRegion.Id)
            .Generate();
        
        await dbContext.Regions.AddAsync(fakerRegion);
        
        await dbContext.Accounts.AddAsync(fakerAccount);

        await dbContext.SaveChangesAsync();
        
        dbContext.ChangeTracker.Clear();
        
        List<MessageBody<string>> listMessageBodies = new(14);
        
        var firstAccountEvent = new AccountConfirmedDataFaker()
            .RuleFor(a => a.LocationId, fakerRegion.Id)
            .Generate();

        var firstMessageBody = new MessageBody<string>()
        {
            AggregateId = fakerAccount.Id,
            Sequence = 2,
            Type = EventType.AccountConfirmed,
            Data = firstAccountEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var secondMessageEvent = new SubscriptionCreatedDataFaker().Generate();
        
        var secondMessageBody = new MessageBody<string>()
        {
            AggregateId = fakerAccount.Id,
            Sequence = 3,
            Type = EventType.SubscriptionCreated,
            Data = secondMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var thirdMessageEvent = new SubscriptionUpdatedDataFaker()
                .RuleFor(r => r.Id, firstAccountEvent.Subscriptions.First().Id)
                .Generate();
        
        var thirdMessageBody = new MessageBody<string>()
        {
            AggregateId = fakerAccount.Id,
            Sequence = 4,
            Type = EventType.SubscriptionUpdated,
            Data = thirdMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        listMessageBodies.Add(firstMessageBody);
        listMessageBodies.Add(secondMessageBody);
        listMessageBodies.Add(thirdMessageBody);

        var request = new AccountRebuildModel(listMessageBodies);

        await mediator.Send(request);

        var firstDbSubscription = await dbContext.Subscriptions.SingleAsync(c => c.Id == firstAccountEvent.Subscriptions.First().Id);
        
        var secondDbSubscription = await dbContext.Subscriptions.SingleAsync(c => c.Id == secondMessageEvent.Id);

        var dbAccount = await dbContext.Accounts.SingleAsync();

        var dbRegion = await dbContext.Regions.SingleAsync();

        var firstSubscription = firstAccountEvent.Subscriptions.First(); 
        
        var fakerFirstSubscription = new SubscriptionModel()
        {
            Id = firstSubscription.Id,
            Name = thirdMessageEvent.Name,
            Type = firstSubscription.Type,
        };
        
        var fakerSecondSubscription = new SubscriptionModel()
        {
            Id = secondMessageEvent.Id,
            Name = secondMessageEvent.Name,
            Type = secondMessageEvent.Type,
        };
        
        EntityAssert.AssertEquality(
            subscriptionData: fakerFirstSubscription,
            dbSubscription: firstDbSubscription,
            firstMessageBody.AggregateId);
        
        EntityAssert.AssertEquality(
            subscriptionData: fakerSecondSubscription,
            dbSubscription: secondDbSubscription,
            firstMessageBody.AggregateId);
        
        RegionAssert.AssertEquality(fakerRegion, dbRegion);
        
        Assert.Equal(firstMessageBody.AggregateId, dbAccount.Id);
        
        Assert.Equal(firstAccountEvent.Phone, dbAccount.Phone);
        
        Assert.Equal(fakerRegion.Id, dbAccount.LocationId);
        
        Assert.Equal(thirdMessageBody.Sequence, dbAccount.Sequence);
    }
    
    [Fact]
    public async Task AccountRebuild_WhenGivenValidRebuildModelAndAccountAlreadyExist_HandleSuccessfully()
    {
        #region Arrange

        using var scope = Factory.Services.CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var fakerRegion = new RegionFaker()
            .RuleFor(c => c.Sequence, 1)
            .Generate();

        var fakerAccount = new AccountFaker()
            .RuleFor(c => c.Sequence, 1)
            .RuleFor(c => c.Number, "*********")
            .RuleFor(c => c.LocationId, fakerRegion.Id)
            .Generate();
        
        await dbContext.Regions.AddAsync(fakerRegion);
        
        await dbContext.Accounts.AddAsync(fakerAccount);

        await dbContext.SaveChangesAsync();
        
        dbContext.ChangeTracker.Clear();
        
        List<MessageBody<string>> listMessageBodies = new(14);
        
        var firstAccountEvent = new AccountConfirmedDataFaker()
            .RuleFor(a => a.LocationId, fakerRegion.Id)
            .Generate();

        var firstMessageBody = new MessageBody<string>()
        {
            AggregateId = fakerAccount.Id,
            Sequence = 1,
            Type = EventType.AccountConfirmed,
            Data = firstAccountEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var secondMessageEvent = new SubscriptionCreatedDataFaker().Generate();
        
        var secondMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 2,
            Type = EventType.SubscriptionCreated,
            Data = secondMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var thirdMessageEvent = AccountUpdatedDataFaker.Create(fakerRegion.Id);
        
        var thirdMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 3,
            Type = EventType.AccountUpdated,
            Data = thirdMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var fourthMessageEvent = new AccountPhoneNumberChangedDataFaker()
            .RuleFor(a => a.Phone, "**********")
            .Generate();
        
        var fourthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 4,
            Type = EventType.AccountPhoneNumberChanged,
            Data = fourthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fifthEventMessage = AccountEmailChangedDataFaker.Create();
        
        var fifthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 5,
            Type = EventType.AccountEmailChanged,
            Data = fifthEventMessage.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var sixthMessageEvent = new SequenceIncrementedData();
        
        var sixthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 6,
            Type = EventType.AccountUnconfirmed,
            Data = sixthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var seventhMessageEvent = new SequenceIncrementedData();
        
        var seventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 7,
            Type = EventType.AccountPriceDifferenceChanged,
            Data = seventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var eighthMessageEvent = new ExpiryDateChangedData(DateTime.UtcNow.AddDays(1));
        
        var eighthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 8,
            Type = EventType.AccountExpirationChanged,
            Data = eighthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var ninthMessageEvent = new ExpiryDateRemovedData();
        
        var ninthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 9,
            Type = EventType.AccountExpirationRemoved,
            Data = ninthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var tenthMessageEvent = new SubscriptionUpdatedDataFaker()
            .RuleFor(r => r.Id, secondMessageEvent.Id)
            .Generate();
        
        var tenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 10,
            Type = EventType.SubscriptionUpdated,
            Data = tenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var eleventhMessageEvent = new SubscriptionRemovedData(secondMessageEvent.Id);
        
        var eleventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 11,
            Type = EventType.SubscriptionRemoved,
            Data = eleventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var twelvethMessageEvent = new SequenceIncrementedData();
        
        var twelvethMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 12,
            Type = EventType.AniscomActivated,
            Data = twelvethMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var thirteenthMessageEvent = new SequenceIncrementedData();
        
        var thirteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 13,
            Type = EventType.AniscomDeactivated,
            Data = thirteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fourteenthMessageEvent = new SequenceIncrementedData();
        var fourteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 14,
            Type = EventType.AccountDeleted,
            Data = fourteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        #endregion

        #region Act

        listMessageBodies.Add(firstMessageBody);
        listMessageBodies.Add(secondMessageBody);
        listMessageBodies.Add(thirdMessageBody);
        listMessageBodies.Add(fourthMessageBody);
        listMessageBodies.Add(fifthMessageBody);
        listMessageBodies.Add(sixthMessageBody);
        listMessageBodies.Add(seventhMessageBody);
        listMessageBodies.Add(eighthMessageBody);
        listMessageBodies.Add(ninthMessageBody);
        listMessageBodies.Add(tenthMessageBody);
        listMessageBodies.Add(eleventhMessageBody);
        listMessageBodies.Add(twelvethMessageBody);
        listMessageBodies.Add(thirteenthMessageBody);
        listMessageBodies.Add(fourteenthMessageBody);

        var request = new AccountRebuildModel(listMessageBodies);

        await mediator.Send(request);

        var dbSubscription = await dbContext.Subscriptions.SingleAsync();

        var dbAccount = await dbContext.Accounts.SingleAsync();

        var dbRegion = await dbContext.Regions.SingleAsync();

        var fakerSubscription = new SubscriptionModel()
        {
            Id = secondMessageEvent.Id,
            Name = tenthMessageEvent.Name,
            Type = secondMessageEvent.Type,
        };

        #endregion

        #region Assert

        EntityAssert.AssertEquality(
            subscriptionData: fakerSubscription,
            dbSubscription: dbSubscription,
            firstMessageBody.AggregateId);
        
        RegionAssert.AssertEquality(fakerRegion, dbRegion);
        
        Assert.Equal(firstMessageBody.AggregateId, dbAccount.Id);
        
        Assert.Equal(fourthMessageEvent.Phone, dbAccount.Phone);
        
        Assert.Equal(fakerRegion.Id, dbAccount.LocationId);
        
        Assert.Equal(fourteenthMessageBody.Sequence, dbAccount.Sequence);

        #endregion
    }
    
    [Fact]
    public async Task AccountRebuild_WhenGivenValidRebuildModelAndAccountAlreadyExist_UpdateAccountHandleSuccessfully()
    {
        #region Arrange

        using var scope = Factory.Services.CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var fakerRegion = new RegionFaker()
            .RuleFor(c => c.Sequence, 1)
            .Generate();

        var fakerAccount = new AccountFaker()
            .RuleFor(c => c.Sequence, 1)
            .RuleFor(c => c.Number, "*********")
            .RuleFor(c => c.LocationId, fakerRegion.Id)
            .Generate();
        
        await dbContext.Regions.AddAsync(fakerRegion);
        
        await dbContext.Accounts.AddAsync(fakerAccount);

        await dbContext.SaveChangesAsync();
        
        dbContext.ChangeTracker.Clear();
        
        List<MessageBody<string>> listMessageBodies = new(14);
        
        var firstAccountEvent = new AccountConfirmedDataFaker()
            .RuleFor(a => a.LocationId, fakerRegion.Id)
            .Generate();

        var firstMessageBody = new MessageBody<string>()
        {
            AggregateId = fakerAccount.Id,
            Sequence = 2,
            Type = EventType.AccountConfirmed,
            Data = firstAccountEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var secondMessageEvent = new SubscriptionCreatedDataFaker().Generate();
        
        var secondMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 3,
            Type = EventType.SubscriptionCreated,
            Data = secondMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var thirdMessageEvent = AccountUpdatedDataFaker.Create(fakerRegion.Id);
        
        var thirdMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 4,
            Type = EventType.AccountUpdated,
            Data = thirdMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var fourthMessageEvent = new AccountPhoneNumberChangedDataFaker()
            .RuleFor(a => a.Phone, "**********")
            .Generate();
        
        var fourthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 5,
            Type = EventType.AccountPhoneNumberChanged,
            Data = fourthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fifthEventMessage = AccountEmailChangedDataFaker.Create();
        
        var fifthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 6,
            Type = EventType.AccountEmailChanged,
            Data = fifthEventMessage.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var sixthMessageEvent = new SequenceIncrementedData();
        
        var sixthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 7,
            Type = EventType.AccountUnconfirmed,
            Data = sixthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var seventhMessageEvent = new SequenceIncrementedData();
        
        var seventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 8,
            Type = EventType.AccountPriceDifferenceChanged,
            Data = seventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var eighthMessageEvent = new ExpiryDateChangedData(DateTime.UtcNow.AddDays(1));
        
        var eighthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 9,
            Type = EventType.AccountExpirationChanged,
            Data = eighthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var ninthMessageEvent = new ExpiryDateRemovedData();
        
        var ninthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 10,
            Type = EventType.AccountExpirationRemoved,
            Data = ninthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var tenthMessageEvent = new SubscriptionUpdatedDataFaker()
            .RuleFor(r => r.Id, secondMessageEvent.Id)
            .Generate();
        
        var tenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 11,
            Type = EventType.SubscriptionUpdated,
            Data = tenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var eleventhMessageEvent = new SubscriptionRemovedData(secondMessageEvent.Id);
        
        var eleventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 12,
            Type = EventType.SubscriptionRemoved,
            Data = eleventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var twelvethMessageEvent = new SequenceIncrementedData();
        
        var twelvethMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 13,
            Type = EventType.AniscomActivated,
            Data = twelvethMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var thirteenthMessageEvent = new SequenceIncrementedData();
        
        var thirteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 14,
            Type = EventType.AniscomDeactivated,
            Data = thirteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fourteenthMessageEvent = new SequenceIncrementedData();
        var fourteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 15,
            Type = EventType.AccountDeleted,
            Data = fourteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        #endregion

        #region Act

        listMessageBodies.Add(firstMessageBody);
        listMessageBodies.Add(secondMessageBody);
        listMessageBodies.Add(thirdMessageBody);
        listMessageBodies.Add(fourthMessageBody);
        listMessageBodies.Add(fifthMessageBody);
        listMessageBodies.Add(sixthMessageBody);
        listMessageBodies.Add(seventhMessageBody);
        listMessageBodies.Add(eighthMessageBody);
        listMessageBodies.Add(ninthMessageBody);
        listMessageBodies.Add(tenthMessageBody);
        listMessageBodies.Add(eleventhMessageBody);
        listMessageBodies.Add(twelvethMessageBody);
        listMessageBodies.Add(thirteenthMessageBody);
        listMessageBodies.Add(fourteenthMessageBody);

        var request = new AccountRebuildModel(listMessageBodies);

        await mediator.Send(request);

        var firstDbSubscription = await dbContext.Subscriptions.SingleAsync(c => c.Id == secondMessageEvent.Id);
        
        var secondDbSubscription = await dbContext.Subscriptions.SingleAsync(c => c.Id == firstAccountEvent.Subscriptions.First().Id);

        var dbAccount = await dbContext.Accounts.SingleAsync();

        var dbRegion = await dbContext.Regions.SingleAsync();

        var fakerSubscription = new SubscriptionModel()
        {
            Id = secondMessageEvent.Id,
            Name = tenthMessageEvent.Name,
            Type = secondMessageEvent.Type,
        };

        #endregion

        #region Assert

        EntityAssert.AssertEquality(
            subscriptionData: fakerSubscription,
            dbSubscription: firstDbSubscription,
            firstMessageBody.AggregateId);
        
        EntityAssert.AssertEquality(
            subscriptionData: firstAccountEvent.Subscriptions.First(),
            dbSubscription: secondDbSubscription,
            firstMessageBody.AggregateId);
        
        RegionAssert.AssertEquality(fakerRegion, dbRegion);
        
        Assert.Equal(firstMessageBody.AggregateId, dbAccount.Id);
        
        Assert.Equal(fourthMessageEvent.Phone, dbAccount.Phone);
        
        Assert.Equal(fakerRegion.Id, dbAccount.LocationId);
        
        Assert.Equal(fourteenthMessageBody.Sequence, dbAccount.Sequence);

        #endregion
    }
    
    [Fact]
    public async Task AccountRebuild_WhenGivenValidRebuildModelAndAccountDoesNotExistInDbAndCouldNotBeCreatedFromEvent_ThrowsException()
    {
        using var scope = Factory.Services.CreateScope();

        var accountId = Guid.NewGuid().ToString();

        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var fakerRegion = new RegionFaker()
            .RuleFor(c => c.Sequence, 1)
            .Generate();

        await dbContext.Regions.AddAsync(fakerRegion);
        
        await dbContext.SaveChangesAsync();
        
        dbContext.ChangeTracker.Clear();
        
        List<MessageBody<string>> listMessageBodies = new(14);
        
        var secondMessageEvent = new SubscriptionCreatedDataFaker().Generate();
        
        var secondMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 2,
            Type = EventType.SubscriptionCreated,
            Data = secondMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var thirdMessageEvent = AccountUpdatedDataFaker.Create(fakerRegion.Id);
        
        var thirdMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 3,
            Type = EventType.AccountUpdated,
            Data = thirdMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var fourthMessageEvent = new AccountPhoneNumberChangedDataFaker()
            .RuleFor(a => a.Phone, "**********")
            .Generate();
        
        var fourthMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 4,
            Type = EventType.AccountPhoneNumberChanged,
            Data = fourthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fifthEventMessage = AccountEmailChangedDataFaker.Create();
        
        var fifthMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 5,
            Type = EventType.AccountEmailChanged,
            Data = fifthEventMessage.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var sixthMessageEvent = new SequenceIncrementedData();
        
        var sixthMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 6,
            Type = EventType.AccountUnconfirmed,
            Data = sixthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var seventhMessageEvent = new SequenceIncrementedData();
        
        var seventhMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 7,
            Type = EventType.AccountPriceDifferenceChanged,
            Data = seventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var eighthMessageEvent = new ExpiryDateChangedData(DateTime.UtcNow.AddDays(1));
        
        var eighthMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 8,
            Type = EventType.AccountExpirationChanged,
            Data = eighthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var ninthMessageEvent = new ExpiryDateRemovedData();
        
        var ninthMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 9,
            Type = EventType.AccountExpirationRemoved,
            Data = ninthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var tenthMessageEvent = new SubscriptionUpdatedDataFaker()
            .RuleFor(r => r.Id, secondMessageEvent.Id)
            .Generate();
        
        var tenthMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 10,
            Type = EventType.SubscriptionUpdated,
            Data = tenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var eleventhMessageEvent = new SubscriptionRemovedData(secondMessageEvent.Id);
        
        var eleventhMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 11,
            Type = EventType.SubscriptionRemoved,
            Data = eleventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var twelvethMessageEvent = new SequenceIncrementedData();
        
        var twelvethMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 12,
            Type = EventType.AniscomActivated,
            Data = twelvethMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var thirteenthMessageEvent = new SequenceIncrementedData();
        
        var thirteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 13,
            Type = EventType.AniscomDeactivated,
            Data = thirteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fourteenthMessageEvent = new SequenceIncrementedData();
        var fourteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = accountId,
            Sequence = 14,
            Type = EventType.AccountDeleted,
            Data = fourteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        listMessageBodies.Add(secondMessageBody);
        listMessageBodies.Add(thirdMessageBody);
        listMessageBodies.Add(fourthMessageBody);
        listMessageBodies.Add(fifthMessageBody);
        listMessageBodies.Add(sixthMessageBody);
        listMessageBodies.Add(seventhMessageBody);
        listMessageBodies.Add(eighthMessageBody);
        listMessageBodies.Add(ninthMessageBody);
        listMessageBodies.Add(tenthMessageBody);
        listMessageBodies.Add(eleventhMessageBody);
        listMessageBodies.Add(twelvethMessageBody);
        listMessageBodies.Add(thirteenthMessageBody);
        listMessageBodies.Add(fourteenthMessageBody);

        var request = new AccountRebuildModel(listMessageBodies);

        var exception = await Assert.ThrowsAsync<AppException>(async () => await mediator.Send(request));

        var dbSubscription = await dbContext.Subscriptions.SingleOrDefaultAsync();

        var dbAccount = await dbContext.Accounts.SingleOrDefaultAsync();

        var dbRegion = await dbContext.Regions.SingleAsync();

        RegionAssert.AssertEquality(fakerRegion, dbRegion);
        
        Assert.Null(dbSubscription);
        
        Assert.Null(dbAccount);
        
        Assert.NotNull(exception);
        
        Assert.Equal(ExceptionStatusCode.Aborted, exception.StatusCode);
        
        Assert.Contains("Could not find the in the database nor create it from message body", exception.Message);
    }

    [Fact]
    public async Task AccountRebuild_WhenAccountSequenceIsLessThenMessagesSequence_ThrowException()
    {
        using var scope = Factory.Services.CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var fakerRegion = new RegionFaker()
            .RuleFor(c => c.Sequence, 1)
            .Generate();

        await dbContext.Regions.AddAsync(fakerRegion);

        await dbContext.SaveChangesAsync();
        
        dbContext.ChangeTracker.Clear();
        
        List<MessageBody<string>> listMessageBodies = new(14);
        
        var firstAccountEvent = new AccountConfirmedDataFaker()
            .RuleFor(a => a.LocationId, fakerRegion.Id)
            .Generate();

        var firstMessageBody = new MessageBody<string>()
        {
            AggregateId = Guid.NewGuid().ToString(),
            Sequence = 1,
            Type = EventType.AccountConfirmed,
            Data = firstAccountEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var secondMessageEvent = new SubscriptionCreatedDataFaker().Generate();
        
        var secondMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 12,
            Type = EventType.SubscriptionCreated,
            Data = secondMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var thirdMessageEvent = AccountUpdatedDataFaker.Create(fakerRegion.Id);
        
        var thirdMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 13,
            Type = EventType.AccountUpdated,
            Data = thirdMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var fourthMessageEvent = new AccountPhoneNumberChangedDataFaker()
            .RuleFor(a => a.Phone, "**********")
            .Generate();
        
        var fourthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 14,
            Type = EventType.AccountPhoneNumberChanged,
            Data = fourthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fifthEventMessage = AccountEmailChangedDataFaker.Create();
        
        var fifthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 15,
            Type = EventType.AccountEmailChanged,
            Data = fifthEventMessage.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var sixthMessageEvent = new SequenceIncrementedData();
        
        var sixthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 16,
            Type = EventType.AccountUnconfirmed,
            Data = sixthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var seventhMessageEvent = new SequenceIncrementedData();
        
        var seventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 17,
            Type = EventType.AccountPriceDifferenceChanged,
            Data = seventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var eighthMessageEvent = new ExpiryDateChangedData(DateTime.UtcNow.AddDays(1));
        
        var eighthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 18,
            Type = EventType.AccountExpirationChanged,
            Data = eighthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var ninthMessageEvent = new ExpiryDateRemovedData();
        
        var ninthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 19,
            Type = EventType.AccountExpirationRemoved,
            Data = ninthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var tenthMessageEvent = new SubscriptionUpdatedDataFaker()
            .RuleFor(r => r.Id, secondMessageEvent.Id)
            .Generate();
        
        var tenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 20,
            Type = EventType.SubscriptionUpdated,
            Data = tenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var eleventhMessageEvent = new SubscriptionRemovedData(secondMessageEvent.Id);
        
        var eleventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 21,
            Type = EventType.SubscriptionRemoved,
            Data = eleventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var twelvethMessageEvent = new SequenceIncrementedData();
        
        var twelvethMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 22,
            Type = EventType.AniscomActivated,
            Data = twelvethMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var thirteenthMessageEvent = new SequenceIncrementedData();
        
        var thirteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 23,
            Type = EventType.AniscomDeactivated,
            Data = thirteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fourteenthMessageEvent = new SequenceIncrementedData();
        var fourteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 24,
            Type = EventType.AccountDeleted,
            Data = fourteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        listMessageBodies.Add(firstMessageBody);
        listMessageBodies.Add(secondMessageBody);
        listMessageBodies.Add(thirdMessageBody);
        listMessageBodies.Add(fourthMessageBody);
        listMessageBodies.Add(fifthMessageBody);
        listMessageBodies.Add(sixthMessageBody);
        listMessageBodies.Add(seventhMessageBody);
        listMessageBodies.Add(eighthMessageBody);
        listMessageBodies.Add(ninthMessageBody);
        listMessageBodies.Add(tenthMessageBody);
        listMessageBodies.Add(eleventhMessageBody);
        listMessageBodies.Add(twelvethMessageBody);
        listMessageBodies.Add(thirteenthMessageBody);
        listMessageBodies.Add(fourteenthMessageBody);

        var request = new AccountRebuildModel(listMessageBodies);

        var exception = await Assert.ThrowsAsync<AppException>(async () => await mediator.Send(request));

        var subscriptions = await dbContext.Subscriptions.SingleOrDefaultAsync();
        
        var dbAccount = await dbContext.Accounts.SingleOrDefaultAsync();

        var dbRegion = await dbContext.Regions.SingleAsync();
        
        Assert.Null(subscriptions);
        
        Assert.Null(dbAccount);
        
        RegionAssert.AssertEquality(fakerRegion, dbRegion);
        
        Assert.NotNull(exception);
        
        Assert.Equal(ExceptionStatusCode.Aborted, exception.StatusCode);
        
        Assert.Equal($"Message sequence {secondMessageBody.Sequence} greater than account sequence {firstMessageBody.Sequence}", exception.Message);
    }
    
    [Fact]
    public async Task AccountRebuild_WhenReceivesOldMessages_SkipItAndHandleOnlyUnhandledEvents()
    {
        #region Arrange

        using var scope = Factory.Services.CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var fakerRegion = new RegionFaker()
            .RuleFor(c => c.Sequence, 1)
            .Generate();

        var fakerAccount = new AccountFaker()
            .RuleFor(c => c.Sequence, 6)
            .RuleFor(c => c.Number, "*********")
            .RuleFor(c => c.LocationId, fakerRegion.Id)
            .Generate();
        
        await dbContext.Regions.AddAsync(fakerRegion);
        
        await dbContext.Accounts.AddAsync(fakerAccount);

        await dbContext.SaveChangesAsync();
        
        dbContext.ChangeTracker.Clear();
        
        List<MessageBody<string>> listMessageBodies = new(14);
        
        var firstAccountEvent = new AccountConfirmedDataFaker()
            .RuleFor(a => a.LocationId, fakerRegion.Id)
            .Generate();

        var firstMessageBody = new MessageBody<string>()
        {
            AggregateId = fakerAccount.Id,
            Sequence = 1,
            Type = EventType.AccountConfirmed,
            Data = firstAccountEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var secondMessageEvent = new SequenceIncrementedData();
        
        var secondMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 2,
            Type = EventType.AccountPriceDifferenceChanged,
            Data = secondMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var thirdMessageEvent = AccountUpdatedDataFaker.Create(fakerRegion.Id);
        
        var thirdMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 3,
            Type = EventType.AccountUpdated,
            Data = thirdMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var fourthMessageEvent = new AccountPhoneNumberChangedDataFaker()
            .RuleFor(a => a.Phone, "**********")
            .Generate();
        
        var fourthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 4,
            Type = EventType.AccountPhoneNumberChanged,
            Data = fourthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fifthEventMessage = AccountEmailChangedDataFaker.Create();
        
        var fifthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 5,
            Type = EventType.AccountEmailChanged,
            Data = fifthEventMessage.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var sixthMessageEvent = new SequenceIncrementedData();
        
        var sixthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 6,
            Type = EventType.AccountUnconfirmed,
            Data = sixthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var  seventhMessageEvent = new SubscriptionCreatedDataFaker().Generate();
        
        var  seventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 7,
            Type = EventType.SubscriptionCreated,
            Data = seventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var eighthMessageEvent = new ExpiryDateChangedData(DateTime.UtcNow.AddDays(1));
        
        var eighthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 8,
            Type = EventType.AccountExpirationChanged,
            Data = eighthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var ninthMessageEvent = new ExpiryDateRemovedData();
        
        var ninthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 9,
            Type = EventType.AccountExpirationRemoved,
            Data = ninthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var tenthMessageEvent = new SubscriptionUpdatedDataFaker()
            .RuleFor(r => r.Id, seventhMessageEvent.Id)
            .Generate();
        
        var tenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 10,
            Type = EventType.SubscriptionUpdated,
            Data = tenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var eleventhMessageEvent = new SubscriptionRemovedData(seventhMessageEvent.Id);
        
        var eleventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 11,
            Type = EventType.SubscriptionRemoved,
            Data = eleventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var twelvethMessageEvent = new SequenceIncrementedData();
        
        var twelvethMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 12,
            Type = EventType.AniscomActivated,
            Data = twelvethMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var thirteenthMessageEvent = new SequenceIncrementedData();
        
        var thirteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 13,
            Type = EventType.AniscomDeactivated,
            Data = thirteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fourteenthMessageEvent = new SequenceIncrementedData();
        var fourteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 14,
            Type = EventType.AccountDeleted,
            Data = fourteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        #endregion

        #region Act

        listMessageBodies.Add(firstMessageBody);
        listMessageBodies.Add(secondMessageBody);
        listMessageBodies.Add(thirdMessageBody);
        listMessageBodies.Add(fourthMessageBody);
        listMessageBodies.Add(fifthMessageBody);
        listMessageBodies.Add(sixthMessageBody);
        listMessageBodies.Add(seventhMessageBody);
        listMessageBodies.Add(eighthMessageBody);
        listMessageBodies.Add(ninthMessageBody);
        listMessageBodies.Add(tenthMessageBody);
        listMessageBodies.Add(eleventhMessageBody);
        listMessageBodies.Add(twelvethMessageBody);
        listMessageBodies.Add(thirteenthMessageBody);
        listMessageBodies.Add(fourteenthMessageBody);

        var request = new AccountRebuildModel(listMessageBodies);

        await mediator.Send(request);

        var dbSubscription = await dbContext.Subscriptions.SingleAsync();

        var dbAccount = await dbContext.Accounts.SingleAsync();

        var dbRegion = await dbContext.Regions.SingleAsync();

        var fakerSubscription = new SubscriptionModel()
        {
            Id = seventhMessageEvent.Id,
            Name = tenthMessageEvent.Name,
            Type = seventhMessageEvent.Type,
        };

        #endregion

        #region Assert

        EntityAssert.AssertEquality(
            subscriptionData: fakerSubscription,
            dbSubscription: dbSubscription,
            firstMessageBody.AggregateId);
        
        RegionAssert.AssertEquality(fakerRegion, dbRegion);
        
        Assert.Equal(fakerAccount.Id, dbAccount.Id);
        
        Assert.Equal(fakerAccount.Phone, dbAccount.Phone);
        
        Assert.NotEqual(fourthMessageEvent.Phone, dbAccount.Phone);
        
        Assert.Equal(fakerRegion.Id, dbAccount.LocationId);
        
        Assert.Equal(fourteenthMessageBody.Sequence, dbAccount.Sequence);

        #endregion
    }
    
    [Theory]
    [InlineData(null, "0AA9700C-C9F3-4E65-A164-2CE88683B329")]
    [InlineData("0AA9700C-C9F3-4E65-A164-2CE88683B329", null)]
    public async Task AccountRebuild_WhenReceivesMessageWithLocationIdNotFoundInDatabase_ThrowAnException(
        string? accountUpdatedEventLocationId,
        string? accountConfirmedEventLocationId)
    {
        #region Arrange

        using var scope = Factory.Services.CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        await dbContext.SaveChangesAsync();
        
        dbContext.ChangeTracker.Clear();
        
        List<MessageBody<string>> listMessageBodies = new(14);
        
        var firstAccountEvent = new AccountConfirmedDataFaker()
            .RuleFor(a => a.LocationId, accountConfirmedEventLocationId)
            .Generate();

        var firstMessageBody = new MessageBody<string>()
        {
            AggregateId = Guid.NewGuid().ToString(),
            Sequence = 2,
            Type = EventType.AccountConfirmed,
            Data = firstAccountEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var secondMessageEvent = new SubscriptionCreatedDataFaker()
            .Generate();
        
        var secondMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 3,
            Type = EventType.SubscriptionCreated,
            Data = secondMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var thirdMessageEvent = AccountUpdatedDataFaker.Create(accountUpdatedEventLocationId);
        
        var thirdMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 4,
            Type = EventType.AccountUpdated,
            Data = thirdMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var fourthMessageEvent = new AccountPhoneNumberChangedDataFaker()
            .RuleFor(a => a.Phone, "**********")
            .Generate();
        
        var fourthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 5,
            Type = EventType.AccountPhoneNumberChanged,
            Data = fourthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fifthEventMessage = AccountEmailChangedDataFaker.Create();
        
        var fifthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 6,
            Type = EventType.AccountEmailChanged,
            Data = fifthEventMessage.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var sixthMessageEvent = new SequenceIncrementedData();
        
        var sixthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 7,
            Type = EventType.AccountUnconfirmed,
            Data = sixthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var seventhMessageEvent = new SequenceIncrementedData();
        
        var seventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 8,
            Type = EventType.AccountPriceDifferenceChanged,
            Data = seventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var eighthMessageEvent = new ExpiryDateChangedData(DateTime.UtcNow.AddDays(1));
        
        var eighthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 9,
            Type = EventType.AccountExpirationChanged,
            Data = eighthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var ninthMessageEvent = new ExpiryDateRemovedData();
        
        var ninthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 10,
            Type = EventType.AccountExpirationRemoved,
            Data = ninthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var tenthMessageEvent = new SubscriptionUpdatedDataFaker()
            .RuleFor(r => r.Id, secondMessageEvent.Id)
            .Generate();
        
        var tenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 11,
            Type = EventType.SubscriptionUpdated,
            Data = tenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var eleventhMessageEvent = new SubscriptionRemovedData(secondMessageEvent.Id);
        
        var eleventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 12,
            Type = EventType.SubscriptionRemoved,
            Data = eleventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var twelvethMessageEvent = new SequenceIncrementedData();
        
        var twelvethMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 13,
            Type = EventType.AniscomActivated,
            Data = twelvethMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var thirteenthMessageEvent = new SequenceIncrementedData();
        
        var thirteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 14,
            Type = EventType.AniscomDeactivated,
            Data = thirteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fourteenthMessageEvent = new SequenceIncrementedData();
        var fourteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 15,
            Type = EventType.AccountDeleted,
            Data = fourteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        #endregion

        #region Act

        listMessageBodies.Add(firstMessageBody);
        listMessageBodies.Add(secondMessageBody);
        listMessageBodies.Add(thirdMessageBody);
        listMessageBodies.Add(fourthMessageBody);
        listMessageBodies.Add(fifthMessageBody);
        listMessageBodies.Add(sixthMessageBody);
        listMessageBodies.Add(seventhMessageBody);
        listMessageBodies.Add(eighthMessageBody);
        listMessageBodies.Add(ninthMessageBody);
        listMessageBodies.Add(tenthMessageBody);
        listMessageBodies.Add(eleventhMessageBody);
        listMessageBodies.Add(twelvethMessageBody);
        listMessageBodies.Add(thirteenthMessageBody);
        listMessageBodies.Add(fourteenthMessageBody);

        var request = new AccountRebuildModel(listMessageBodies);

        var exception = await Assert.ThrowsAsync<AppException>(async () => await mediator.Send(request));

        var dbAccount = await dbContext.Accounts.SingleOrDefaultAsync();

        var dbRegion = await dbContext.Regions.SingleOrDefaultAsync();

        var dbSubscription = await dbContext.Subscriptions.ToListAsync();

        var notFoundLocationId = "0AA9700C-C9F3-4E65-A164-2CE88683B329";

        #endregion
        
        #region Assert

        Assert.Empty(dbSubscription);
        
        Assert.Null(dbAccount);
        
        Assert.Null(dbRegion);
        
        Assert.NotNull(exception);
        
        Assert.Equal(ExceptionStatusCode.FailedPrecondition, exception.StatusCode);
        
        Assert.Contains($"Location not found with AccountId : {firstMessageBody.AggregateId}  , LocationId {notFoundLocationId}", exception.Message);
        
        #endregion
    }
    
    [Fact]
    public async Task AccountRebuild_WhenUpdatingAccountSubscriptionThatDoesNotExistInDatabase_ThrowAnException()
    {
        #region Arrange

        using var scope = Factory.Services.CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var fakerRegion = new RegionFaker()
            .RuleFor(c => c.Sequence, 1)
            .Generate();

        var fakerAccount = new AccountFaker()
            .RuleFor(c => c.Sequence, 1)
            .RuleFor(c => c.Number, "*********")
            .RuleFor(c => c.LocationId, fakerRegion.Id)
            .Generate();
        
        await dbContext.Regions.AddAsync(fakerRegion);
        
        await dbContext.Accounts.AddAsync(fakerAccount);

        await dbContext.SaveChangesAsync();
        
        dbContext.ChangeTracker.Clear();
        
        List<MessageBody<string>> listMessageBodies = new(14);
        
        var firstAccountEvent = new AccountConfirmedDataFaker()
            .RuleFor(a => a.LocationId, fakerRegion.Id)
            .Generate();

        var firstMessageBody = new MessageBody<string>()
        {
            AggregateId = fakerAccount.Id,
            Sequence = 2,
            Type = EventType.AccountConfirmed,
            Data = firstAccountEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var secondMessageEvent = new SubscriptionCreatedDataFaker().Generate();
        
        var secondMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 3,
            Type = EventType.SubscriptionCreated,
            Data = secondMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var thirdMessageEvent = AccountUpdatedDataFaker.Create(fakerRegion.Id);
        
        var thirdMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 4,
            Type = EventType.AccountUpdated,
            Data = thirdMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var fourthMessageEvent = new AccountPhoneNumberChangedDataFaker()
            .RuleFor(a => a.Phone, "**********")
            .Generate();
        
        var fourthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 5,
            Type = EventType.AccountPhoneNumberChanged,
            Data = fourthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fifthEventMessage = AccountEmailChangedDataFaker.Create();
        
        var fifthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 6,
            Type = EventType.AccountEmailChanged,
            Data = fifthEventMessage.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var sixthMessageEvent = new SequenceIncrementedData();
        
        var sixthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 7,
            Type = EventType.AccountUnconfirmed,
            Data = sixthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var seventhMessageEvent = new SequenceIncrementedData();
        
        var seventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 8,
            Type = EventType.AccountPriceDifferenceChanged,
            Data = seventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var eighthMessageEvent = new ExpiryDateChangedData(DateTime.UtcNow.AddDays(1));
        
        var eighthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 9,
            Type = EventType.AccountExpirationChanged,
            Data = eighthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var ninthMessageEvent = new ExpiryDateRemovedData();
        
        var ninthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 10,
            Type = EventType.AccountExpirationRemoved,
            Data = ninthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var tenthMessageEvent = new SubscriptionUpdatedDataFaker()
            .RuleFor(r => r.Id, Guid.NewGuid().ToString())
            .Generate();
        
        var tenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 11,
            Type = EventType.SubscriptionUpdated,
            Data = tenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var eleventhMessageEvent = new SubscriptionRemovedData(secondMessageEvent.Id);
        
        var eleventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 12,
            Type = EventType.SubscriptionRemoved,
            Data = eleventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var twelvethMessageEvent = new SequenceIncrementedData();
        
        var twelvethMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 13,
            Type = EventType.AniscomActivated,
            Data = twelvethMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var thirteenthMessageEvent = new SequenceIncrementedData();
        
        var thirteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 14,
            Type = EventType.AniscomDeactivated,
            Data = thirteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fourteenthMessageEvent = new SequenceIncrementedData();
        var fourteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 15,
            Type = EventType.AccountDeleted,
            Data = fourteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        #endregion

        #region Act

        listMessageBodies.Add(firstMessageBody);
        listMessageBodies.Add(secondMessageBody);
        listMessageBodies.Add(thirdMessageBody);
        listMessageBodies.Add(fourthMessageBody);
        listMessageBodies.Add(fifthMessageBody);
        listMessageBodies.Add(sixthMessageBody);
        listMessageBodies.Add(seventhMessageBody);
        listMessageBodies.Add(eighthMessageBody);
        listMessageBodies.Add(ninthMessageBody);
        listMessageBodies.Add(tenthMessageBody);
        listMessageBodies.Add(eleventhMessageBody);
        listMessageBodies.Add(twelvethMessageBody);
        listMessageBodies.Add(thirteenthMessageBody);
        listMessageBodies.Add(fourteenthMessageBody);

        var request = new AccountRebuildModel(listMessageBodies);

        var exception = await Assert.ThrowsAsync<AppException>(async () => await mediator.Send(request));


        var dbAccount = await dbContext.Accounts.SingleAsync();

        var dbRegion = await dbContext.Regions.SingleAsync();

        var dbSubscriptions = await dbContext.Subscriptions.ToListAsync();

        #endregion

        #region Assert

        Assert.Empty(dbSubscriptions);
        
        Assert.NotNull(exception);
        
        Assert.Equal(ExceptionStatusCode.FailedPrecondition, exception.StatusCode);

        Assert.Contains($"Operation Failed could not update subscription, No subscription with id : {tenthMessageEvent.Id} was found", exception.Message);

        RegionAssert.AssertEquality(fakerRegion, dbRegion);

        Assert.Equal(fakerAccount.Id, dbAccount.Id);
        
        Assert.Equal(fakerAccount.Phone, dbAccount.Phone);
        
        Assert.Equal(fakerAccount.LocationId, dbAccount.LocationId);
        
        Assert.Equal(fakerAccount.Sequence, dbAccount.Sequence);

        #endregion
    }
    
    [Fact]
    public async Task AccountRebuild_WhenCreatingAccountSubscriptionThatDoesAlreadyExistInDatabase_ThrowAnException()
    {
        #region Arrange

        using var scope = Factory.Services.CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var fakerRegion = new RegionFaker()
            .RuleFor(c => c.Sequence, 1)
            .Generate();

        var fakerAccount = new AccountFaker()
            .RuleFor(c => c.Sequence, 1)
            .RuleFor(c => c.Number, "*********")
            .RuleFor(c => c.LocationId, fakerRegion.Id)
            .Generate();

        var fakerSubscription = new SubscriptionFaker()
            .RuleFor(c => c.AccountId, fakerAccount.Id)
            .Generate();
        
        await dbContext.Regions.AddAsync(fakerRegion);
        
        await dbContext.Accounts.AddAsync(fakerAccount);
        
        await dbContext.Subscriptions.AddAsync(fakerSubscription);

        await dbContext.SaveChangesAsync();
        
        dbContext.ChangeTracker.Clear();
        
        List<MessageBody<string>> listMessageBodies = new(14);
        
        var firstAccountEvent = new AccountConfirmedDataFaker()
            .RuleFor(a => a.LocationId, fakerRegion.Id)
            .Generate();

        var firstMessageBody = new MessageBody<string>()
        {
            AggregateId = fakerAccount.Id,
            Sequence = 2,
            Type = EventType.AccountConfirmed,
            Data = firstAccountEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var secondMessageEvent = new SubscriptionCreatedDataFaker()
            .RuleFor(c => c.Id, fakerSubscription.Id)
            .Generate();
        
        var secondMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 3,
            Type = EventType.SubscriptionCreated,
            Data = secondMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var thirdMessageEvent = AccountUpdatedDataFaker.Create(fakerRegion.Id);
        
        var thirdMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 4,
            Type = EventType.AccountUpdated,
            Data = thirdMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var fourthMessageEvent = new AccountPhoneNumberChangedDataFaker()
            .RuleFor(a => a.Phone, "**********")
            .Generate();
        
        var fourthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 5,
            Type = EventType.AccountPhoneNumberChanged,
            Data = fourthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fifthEventMessage = AccountEmailChangedDataFaker.Create();
        
        var fifthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 6,
            Type = EventType.AccountEmailChanged,
            Data = fifthEventMessage.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var sixthMessageEvent = new SequenceIncrementedData();
        
        var sixthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 7,
            Type = EventType.AccountUnconfirmed,
            Data = sixthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var seventhMessageEvent = new SequenceIncrementedData();
        
        var seventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 8,
            Type = EventType.AccountPriceDifferenceChanged,
            Data = seventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var eighthMessageEvent = new ExpiryDateChangedData(DateTime.UtcNow.AddDays(1));
        
        var eighthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 9,
            Type = EventType.AccountExpirationChanged,
            Data = eighthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var ninthMessageEvent = new ExpiryDateRemovedData();
        
        var ninthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 10,
            Type = EventType.AccountExpirationRemoved,
            Data = ninthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var tenthMessageEvent = new SubscriptionUpdatedDataFaker()
            .RuleFor(r => r.Id, secondMessageEvent.Id)
            .Generate();
        
        var tenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 11,
            Type = EventType.SubscriptionUpdated,
            Data = tenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var eleventhMessageEvent = new SubscriptionRemovedData(secondMessageEvent.Id);
        
        var eleventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 12,
            Type = EventType.SubscriptionRemoved,
            Data = eleventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var twelvethMessageEvent = new SequenceIncrementedData();
        
        var twelvethMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 13,
            Type = EventType.AniscomActivated,
            Data = twelvethMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var thirteenthMessageEvent = new SequenceIncrementedData();
        
        var thirteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 14,
            Type = EventType.AniscomDeactivated,
            Data = thirteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fourteenthMessageEvent = new SequenceIncrementedData();
        var fourteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 15,
            Type = EventType.AccountDeleted,
            Data = fourteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        #endregion

        #region Act

        listMessageBodies.Add(firstMessageBody);
        listMessageBodies.Add(secondMessageBody);
        listMessageBodies.Add(thirdMessageBody);
        listMessageBodies.Add(fourthMessageBody);
        listMessageBodies.Add(fifthMessageBody);
        listMessageBodies.Add(sixthMessageBody);
        listMessageBodies.Add(seventhMessageBody);
        listMessageBodies.Add(eighthMessageBody);
        listMessageBodies.Add(ninthMessageBody);
        listMessageBodies.Add(tenthMessageBody);
        listMessageBodies.Add(eleventhMessageBody);
        listMessageBodies.Add(twelvethMessageBody);
        listMessageBodies.Add(thirteenthMessageBody);
        listMessageBodies.Add(fourteenthMessageBody);

        var request = new AccountRebuildModel(listMessageBodies);

        var exception = await Assert.ThrowsAsync<AppException>(async () => await mediator.Send(request));

        var dbAccount = await dbContext.Accounts.SingleAsync();

        var dbRegion = await dbContext.Regions.SingleAsync();

        var dbSubscription = await dbContext.Subscriptions.SingleAsync();

        #endregion
        
        #region Assert
        
        Assert.NotNull(exception);
        
        Assert.Equal(ExceptionStatusCode.FailedPrecondition, exception.StatusCode);

        Assert.Contains($"Operation Failed could not create subscription with id : {secondMessageEvent.Id} as it already exist", exception.Message);

        RegionAssert.AssertEquality(fakerRegion, dbRegion);

        Assert.Equal(fakerAccount.Id, dbAccount.Id);
        
        Assert.Equal(fakerAccount.Phone, dbAccount.Phone);
        
        Assert.Equal(fakerAccount.LocationId, dbAccount.LocationId);
        
        Assert.Equal(fakerAccount.Sequence, dbAccount.Sequence);
        
        Assert.Equal(fakerSubscription.Id, dbSubscription.Id);
        
        Assert.Equal(fakerSubscription.Type, dbSubscription.Type);
        
        Assert.Equal(fakerSubscription.Name, dbSubscription.Name);
        
        Assert.Equal(fakerSubscription.AccountId, dbSubscription.AccountId);

        #endregion
    }
    
    [Fact]
    public async Task AccountRebuild_WhenReceivesUnrecognizedEventType_ThrowAnException()
    {
        #region Arrange

        var UnrecogizedEvent = "AllowedDebtTopUp";
        
        using var scope = Factory.Services.CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var fakerRegion = new RegionFaker()
            .RuleFor(c => c.Sequence, 1)
            .Generate();

        var fakerAccount = new AccountFaker()
            .RuleFor(c => c.Sequence, 1)
            .RuleFor(c => c.Number, "*********")
            .RuleFor(c => c.LocationId, fakerRegion.Id)
            .Generate();
        
        await dbContext.Regions.AddAsync(fakerRegion);
        
        await dbContext.Accounts.AddAsync(fakerAccount);

        await dbContext.SaveChangesAsync();
        
        dbContext.ChangeTracker.Clear();
        
        List<MessageBody<string>> listMessageBodies = new(14);
        
        var firstAccountEvent = new AccountConfirmedDataFaker()
            .RuleFor(a => a.LocationId, fakerRegion.Id)
            .Generate();

        var firstMessageBody = new MessageBody<string>()
        {
            AggregateId = fakerAccount.Id,
            Sequence = 2,
            Type = EventType.AccountConfirmed,
            Data = firstAccountEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var secondMessageEvent = new SubscriptionCreatedDataFaker().Generate();
        
        var secondMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 3,
            Type = EventType.SubscriptionCreated,
            Data = secondMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var thirdMessageEvent = AccountUpdatedDataFaker.Create(fakerRegion.Id);
        
        var thirdMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 4,
            Type = EventType.AccountUpdated,
            Data = thirdMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var fourthMessageEvent = new AccountPhoneNumberChangedDataFaker()
            .RuleFor(a => a.Phone, "**********")
            .Generate();
        
        var fourthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 5,
            Type = EventType.AccountPhoneNumberChanged,
            Data = fourthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fifthEventMessage = AccountEmailChangedDataFaker.Create();
        
        var fifthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 6,
            Type = EventType.AccountEmailChanged,
            Data = fifthEventMessage.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var sixthMessageEvent = new SequenceIncrementedData();
        
        var sixthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 7,
            Type = EventType.AccountUnconfirmed,
            Data = sixthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var seventhMessageEvent = new SequenceIncrementedData();
        
        var seventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 8,
            Type = EventType.AccountPriceDifferenceChanged,
            Data = seventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var eighthMessageEvent = new ExpiryDateChangedData(DateTime.UtcNow.AddDays(1));
        
        var eighthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 9,
            Type = EventType.AccountExpirationChanged,
            Data = eighthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var ninthMessageEvent = new ExpiryDateRemovedData();
        
        var ninthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 10,
            Type = EventType.AccountExpirationRemoved,
            Data = ninthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var tenthMessageEvent = new SubscriptionUpdatedDataFaker()
            .RuleFor(r => r.Id, secondMessageEvent.Id)
            .Generate();
        
        var tenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 11,
            Type = EventType.SubscriptionUpdated,
            Data = tenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var eleventhMessageEvent = new SubscriptionRemovedData(secondMessageEvent.Id);
        
        var eleventhMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 12,
            Type = EventType.SubscriptionRemoved,
            Data = eleventhMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var twelvethMessageEvent = new SequenceIncrementedData();
        
        var twelvethMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 13,
            Type = EventType.AniscomActivated,
            Data = twelvethMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };
        
        var thirteenthMessageEvent = new SequenceIncrementedData();
        
        var thirteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 14,
            Type = EventType.AniscomDeactivated,
            Data = thirteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        var fourteenthMessageEvent = new SequenceIncrementedData();
        var fourteenthMessageBody = new MessageBody<string>()
        {
            AggregateId = firstMessageBody.AggregateId,
            Sequence = 15,
            Type = UnrecogizedEvent,
            Data = fourteenthMessageEvent.Serialize(),
            DateTime = DateTime.UtcNow,
            Version = 2,
        };

        #endregion

        #region Act

        listMessageBodies.Add(firstMessageBody);
        listMessageBodies.Add(secondMessageBody);
        listMessageBodies.Add(thirdMessageBody);
        listMessageBodies.Add(fourthMessageBody);
        listMessageBodies.Add(fifthMessageBody);
        listMessageBodies.Add(sixthMessageBody);
        listMessageBodies.Add(seventhMessageBody);
        listMessageBodies.Add(eighthMessageBody);
        listMessageBodies.Add(ninthMessageBody);
        listMessageBodies.Add(tenthMessageBody);
        listMessageBodies.Add(eleventhMessageBody);
        listMessageBodies.Add(twelvethMessageBody);
        listMessageBodies.Add(thirteenthMessageBody);
        listMessageBodies.Add(fourteenthMessageBody);

        var request = new AccountRebuildModel(listMessageBodies);

        var exception = await Assert.ThrowsAsync<AppException>(async () =>await mediator.Send(request));

        var dbAccount = await dbContext.Accounts.SingleAsync();

        var dbRegion = await dbContext.Regions.SingleAsync();

        var dbSubscription = await dbContext.Subscriptions.ToListAsync();

        #endregion

        #region Assert

        Assert.Empty(dbSubscription);
        
        Assert.NotNull(exception);
        
        Assert.Equal(ExceptionStatusCode.OutOfRange, exception.StatusCode);

        Assert.Contains($"Event type out of range in rebuild service => {UnrecogizedEvent}", exception.Message);

        RegionAssert.AssertEquality(fakerRegion, dbRegion);

        Assert.Equal(fakerAccount.Id, dbAccount.Id);
        
        Assert.Equal(fakerAccount.Phone, dbAccount.Phone);
        
        Assert.Equal(fakerAccount.LocationId, dbAccount.LocationId);
        
        Assert.Equal(fakerAccount.Sequence, dbAccount.Sequence);
        
        #endregion
    }
}