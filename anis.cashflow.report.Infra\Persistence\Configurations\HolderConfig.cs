﻿using Anis.Cashflow.Report.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Anis.Cashflow.Report.Infra.Persistence.Configurations
{
    public class HolderConfig : IEntityTypeConfiguration<Holder>
    {
        public void Configure(EntityTypeBuilder<Holder> builder)
        {
            builder.Property(h => h.Id).HasMaxLength(Config.StringIdLength)
                                                       .IsRequired();

            builder.Property(c => c.Sequence).IsConcurrencyToken();
        }
    }
}
