﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Contracts.Services.BaseService;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Grpc.Extensions;
using Anis.Cashflow.Report.Grpc.Protos.Client;
using Anis.Cashflow.Report.Grpc.Protos.Client.SpecialQueries;
using Anis.Cashflow.Report.Grpc.Protos.Clients;
using Anis.Cashflow.Report.Grpc.Protos.Clients.Holders;
using Anis.Cashflow.Report.Grpc.Protos.Rebuild.v3;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using MediatR;
using System.Text.Json;

namespace Anis.Cashflow.Report.Grpc.Services;

public class RebuildServiceV3 : CashflowRebuildV3.CashflowRebuildV3Base
{
    private readonly EventsHistory.EventsHistoryClient _eventHistoryClient;
    private readonly SpecialQueries.SpecialQueriesClient _specialQueriesClient;
    private readonly HoldersEventsHistory.HoldersEventsHistoryClient _holdersEventsHistoryClient;
    private readonly NotificationHistoryOperatorToBusinessLink.NotificationHistoryOperatorToBusinessLinkClient _notificationHistoryOperatorToBusinessLinkClient;
    private readonly IMediator _mediator;
    private readonly ILogger<RebuildService> _logger;
    private readonly IServiceProvider _provider;
    private readonly IRetryCallerService _retryCallerService;

    public RebuildServiceV3(
        EventsHistory.EventsHistoryClient ecomClient,
        SpecialQueries.SpecialQueriesClient specialQueriesClient,
        HoldersEventsHistory.HoldersEventsHistoryClient holdersEventsHistoryClient,
        NotificationHistoryOperatorToBusinessLink.NotificationHistoryOperatorToBusinessLinkClient notificationHistoryOperatorToBusinessLinkClient,
        IMediator mediator,
        ILogger<RebuildService> logger,
        IServiceProvider provider,
        IRetryCallerService retryCallerService
        )
    {
        _eventHistoryClient = ecomClient;
        _specialQueriesClient = specialQueriesClient;
        _holdersEventsHistoryClient = holdersEventsHistoryClient;
        _notificationHistoryOperatorToBusinessLinkClient = notificationHistoryOperatorToBusinessLinkClient;
        _mediator = mediator;
        _logger = logger;
        _provider = provider;
        _retryCallerService = retryCallerService;
    }

    public override async Task<Empty> RebuildAccounts(Empty request, ServerCallContext context)
    {
        _logger.LogCritical("-------------- Account Rebuild Started -------------------");

        try
        {
            for (var i = 1; i > 0; i++)
            {
                var response = await _eventHistoryClient.GetAllAccountsAsync(new Request()
                {
                    Page = i,
                    Size = 1000
                });

                if (response.Messages.Count > 0)
                {
                    var groupedAccountEvents = response.Messages.GroupBy(c => c.AggregateId);

                    await Task.WhenAll(groupedAccountEvents.Select(x => HandelAccountResponseAsync(x)));
                }
                else
                    break;

                _logger.LogWarning("Rebuild Accounts size {size} ,with page {page}", response.Size, i);
            }

            _logger.LogCritical("------------- Done Rebuilding Accounts -------------------");
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "Account rebuild failed ...");
        }

        return new Empty();
    }

    private async Task HandelAccountResponseAsync(IEnumerable<Protos.Clients.EventMessage> messages)
    {
        using var scope = _provider.CreateScope();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var messageBodies = messages.Select(x => x.ToMessageBody()).ToList();

        await mediator.Send(new AccountRebuildModel(Messages: messageBodies.OrderBy(m => m.Sequence).ToList()));
    }

    public async override Task<Empty> RebuildWallets(Empty request, ServerCallContext context)
    {
        for (var i = 1; i > 0; i++)
        {
            var response = await _eventHistoryClient.GetWalletsAsync(new Request()
            {
                Page = i,
                Size = 500
            });

            if (response.Messages.Count > 0)
            {
                await HandelWalletResponseAsync(response);
            }
            else
                break;
        }

        return new Empty();
    }

    private async Task HandelWalletResponseAsync(Protos.Clients.Response response)
    {
        foreach (var @event in response.Messages)
        {
            switch (@event.Type)
            {
                case EventType.WalletCreated:
                    await _mediator.Send(@event.ToMessageBody<WalletCreatedData>());
                    break;
            }
        }
    }

    public async override Task<Empty> RebuildGateWayRegions(Empty request, ServerCallContext context)
    {
        _logger.LogWarning("------------------- Rebuild regions started --------------------");

        for (var i = 1; i > 0; i++)
        {
            var response = await _eventHistoryClient.GetAllRegionsAsync(new Request()
            {
                Page = i,
                Size = 100
            });

            if (response.Messages.Count > 0)
            {
                await HandelResponseAsync(response);
            }
            else
                break;
        }

        _logger.LogWarning("------------------- Rebuild regions end --------------------");

        return new Empty();
    }

    private async Task HandelResponseAsync(Protos.Clients.Response response)
    {
        foreach (var @event in response.Messages)
        {
            switch (@event.Type)
            {
                case EventType.RegionCreated:
                    await _mediator.Send(@event.ToMessageBody<RegionCreatedData>());
                    break;
                case EventType.RegionUpdated:
                    await _mediator.Send(@event.ToMessageBody<RegionUpdatedData>());
                    break;
                case EventType.RegionDeleted:
                    await _mediator.Send(@event.ToMessageBody<RegionDeletedData>());
                    break;
                case EventType.RegionCodeChanged:
                    await _mediator.Send(@event.ToMessageBody<RegionCodeChangedData>());
                    break;
                default:
                    throw new ArgumentOutOfRangeException($"{@event.Type}", $"Event type out of range in rebuild service => {@event}");
            }
        }
    }

    public async override Task<Empty> RebuildTransactions(RebuildTransactionsRequest request, ServerCallContext context)
    {
        _logger.LogWarning("------------------- Rebuild transations started --------------------");

        await RebuildTransactionsAsync(request.TotalEvents, request.PageSize);

        _logger.LogWarning("------------------- Rebuild transations end --------------------");

        return new Empty();
    }

    private async Task RebuildTransactionsAsync(int currentPage, int pageSize = 1000)
    {
        using var scope = _provider.CreateScope();

        var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

        for (var i = currentPage; i > 0; i++)
        {
            _logger.LogWarning("excute rebuild transaction , with  page {startPage}", i);

            var response = await _eventHistoryClient.GetTransactionsAsync(new Request()
            {
                Page = i,
                Size = pageSize,
            });

            if (response.Messages.Count == 0)
                continue;

            var walletEvents = new List<MessageBody<string>>();

            foreach (var message in response.Messages)
            {
                var @event = message.ToMessageBody();

                walletEvents.Add(@event);
            }

            var walletGroups = walletEvents.GroupBy(c => c.AggregateId).ToList();

            await Task.WhenAll(walletGroups.Select(g => HandleGroup(g)));
        }

        return;
    }

    private async Task HandleGroup(IEnumerable<MessageBody<string>> rebuildModeles)
    {
        await _retryCallerService.CallAsync(async () =>
        {
            using var scope = _provider.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            return await mediator.Send(new TransactionRebuildModel<string> { Models = rebuildModeles });
        });
    }

    public async override Task<Empty> RebuildSnapshotWallets(RebuildWalletsRequest request, ServerCallContext context)
    {
        _logger.LogWarning("------------------- Rebuild snapshot wallets started --------------------");

        var walletRequest = new GetWalletsSnapshotRequest
        {
            ResultNumber = request.ResultNumber,
            DateTime = DateTime.UtcNow.AddDays(-1).ToTimestamp(),
            IncludeDetailedSummation = true
        };

        using (var call = _specialQueriesClient.GetWalletsSnapshot(walletRequest))
        {
            await foreach (var response in call.ResponseStream.ReadAllAsync())
            {
                var model = response.ToModel();

                await _mediator.Send(model);

                _logger.LogCritical("Result number : {ResultNumber}", response.ResultNumber);
            }
        }

        _logger.LogWarning("------------------- Rebuild snapshot wallets end --------------------");

        return new Empty();
    }

    public async override Task<Empty> RefresheAccounts(Empty request, ServerCallContext context)
    {
        using (var call = _specialQueriesClient.GetAccountSnapshot(new AccountSnapshotRequest { ResultNumber = 0 }))
        {
            await foreach (var response in call.ResponseStream.ReadAllAsync())
            {
                var model = response.ToRefreshModel();

                await _mediator.Send(model);
            }
        }

        return new Empty();
    }

    public override async Task<Empty> RebuildHolders(RebuildHoldersRequest request, ServerCallContext context)
    {

        _logger.LogCritical("-------------- Holders Rebuild Started -------------------");

        try
        {
            var page = request.CurrentPage < 1 ? 1 : request.CurrentPage;

            var size = request.PageSize < 1 ? 100 : request.PageSize;

            for (var i = page; i > 0; i++)
            {
                var response = await _holdersEventsHistoryClient.GetEventsAsync(new GetEventsRequest()
                {
                    CurrentPage = i,
                    PageSize = size
                });

                if (response.Events.Count > 0)
                {
                    var groupedEvents = response.Events.GroupBy(c => c.AggregateId);

                    await Task.WhenAll(groupedEvents.Select(x => HandelHoldersEventMessageResponseAsync(x)));
                }
                else
                    break;

                _logger.LogWarning("Rebuild Holders size {size} ,with page {page}", request.PageSize, i);
            }

            _logger.LogCritical("------------- Done Rebuilding Holders -------------------");
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "Holders rebuild failed ...");
        }

        return new Empty();
    }

    private async Task HandelHoldersEventMessageResponseAsync(IEnumerable<Protos.Clients.Holders.EventMessage> messages)
    {
        using var scope = _provider.CreateScope();

        var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

        var messageBodies = messages.Select(x => x.ToMessageBody()).ToList();

        await mediator.Send(new HoldersRebuildModel(Messages: messageBodies));
    }

    public override async Task<Empty> RebuildOperatorToBusinessLink(RebuildHoldersRequest request, ServerCallContext context)
    {
        _logger.LogCritical("-------------- OperatorToBusiness Rebuild Started -------------------");

        try
        {
            var page = request.CurrentPage < 1 ? 1 : request.CurrentPage;
            var size = request.PageSize < 1 ? 100 : request.PageSize;

            for (var i = page; i > 0; i++)
            {
                var response = await _notificationHistoryOperatorToBusinessLinkClient.GetNotificationMessagesAsync(new GetNotificationMessagesRequest()
                {
                    CurrentPage = i,
                    PageSize = size
                });

                if (response.NotificationMessages.Count > 0)
                {
                    var groupedEvents = response.NotificationMessages.GroupBy(c => c.OperatorSubscriptionId);

                    await Task.WhenAll(groupedEvents.Select(x => HandelOperatorToBusinessEventMessageResponseAsync(x)));
                }
                else
                    break;

                _logger.LogWarning("Rebuild OperatorToBusiness size {size} ,with page {page}", request.PageSize, i);
            }

            _logger.LogCritical("------------- Done Rebuilding OperatorToBusiness -------------------");
        }
        catch (Exception e)
        {
            _logger.LogCritical(e, "OperatorToBusiness rebuild failed ...");
        }

        return new Empty();
    }

    private async Task HandelOperatorToBusinessEventMessageResponseAsync(IEnumerable<NotificationMessage> messages)
    {
        try
        {
            using var scope = _provider.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            _logger.LogWarning("Rebuild operator to business link with messages data serialization {data}",
                JsonSerializer.Serialize(messages));

            var messageBodies = messages.Select(x => x.ToRebuildModel()).ToList();

            // Modified: Iterate and send each message individually
            foreach (var messageBody in messageBodies)
            {
                await mediator.Send(messageBody);
            }
        }
        catch (Exception e)
        {
            _logger.LogError(e, "Error while handling operator to business link messages: {messages}",
                JsonSerializer.Serialize(messages));
            throw;
        }
    }
}