﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.AccountHandlers
{
    public class SubscriptionUpdatedHandler : IRequestHandler<MessageBody<SubscriptionUpdatedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public SubscriptionUpdatedHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> Handle(MessageBody<SubscriptionUpdatedData> messageBody, CancellationToken cancellationToken)
        {
            var account = await _unitOfWork.Accounts.FindAsync(messageBody.AggregateId);

            if (account == null) return false;

            if (account.Sequence != messageBody.Sequence - 1)
                return account.Sequence >= messageBody.Sequence;

            var subscription = await _unitOfWork.Subscriptions.FindAsync(messageBody.Data.Id);

            if (subscription == null) return false;

            subscription.Update(messageBody);

            account.IncrementSequence();

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}