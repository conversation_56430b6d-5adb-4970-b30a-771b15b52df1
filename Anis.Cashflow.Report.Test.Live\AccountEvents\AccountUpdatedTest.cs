﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Live.Asserts;
using Anis.Cashflow.Report.Test.Live.Protos;
using Google.Protobuf.WellKnownTypes;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.AccountEvents
{
    public class AccountUpdatedTest : TestBase
    {
        public AccountUpdatedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Update_UpdateAccountWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
             .Generate();

            var secondCreateRegion = new RegionFaker()
             .Generate();

            var createAccount = new AccountFaker()
             .RuleFor(f => f.LocationId, createRegion.Id)
             .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddRangeAsync(createRegion, secondCreateRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var channel = GrpcChannel.ForAddress(Address.EcomCards);

            var grpcClient = new AccountsDemoEvents.AccountsDemoEventsClient(channel);

            var request = new UpdateAccountRequest
            {
                AggregateId = createAccount.Id,
                DateTime = DateTime.UtcNow.ToTimestamp(),
                Sequence = 2,
                Number = "Number",
                LocationId = secondCreateRegion.Id,
            };

            await grpcClient.UpdateAccountAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<AccountEventsListener>().CloseProccessorAsync();

            var dbAccounts = await context.Accounts.SingleOrDefaultAsync();

            Assert.Equal(createAccount.Id, dbAccounts.Id);
            Assert.Equal(2, dbAccounts.Sequence);
            Assert.Equal(request.Number, dbAccounts.Number);

            DemoEventAsserts.AssertEquality(request, dbAccounts);
        }
    }
}
