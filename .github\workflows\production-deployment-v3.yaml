name: Deploy To Production v3
on:
  # push:
  #  branches: [ "master" ]
  workflow_dispatch:
env:
  SERVICE_NAME: anis-cashflow-report-v3
  SERVICE_NAMESPACE: anis-cashflow-report
permissions:
  actions: read
  contents: read
  id-token: write

jobs:
  build-service-image:
    environment: production
    runs-on: self-hosted
    steps:
      - name: "Checkout Code"
        uses: actions/checkout@v3
      - name: "Looking For Check Docker & Deployment File"
        id: check_files
        uses: andstor/file-existence-action@v1
        with:
          files: "Dockerfile, deployment-production-v3.yaml"
          allow_failure: true
      - name: Azure login
        uses: azure/login@v1.4.3
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}
      - name: Build and push image to ACR
        run: az acr build --image ${{ env.SERVICE_NAME }}:${{ github.sha }} --registry ecomly -g Core  -f ./Dockerfile ./
  
  deploy-to-production-v3:
    runs-on: self-hosted
    environment: production
    needs:
      - build-service-image
    steps:
      - name: "Checkout Code"
        uses: actions/checkout@v3
      - uses: cschleiden/replace-tokens@v1
        with:
          files: '["deployment-production-v3.yaml"]'
          tokenPrefix: ___
          tokenSuffix: ___
        env:
          SERVICE_NAME: ${{ env.SERVICE_NAME }}
          SERVICE_NAMESPACE: ${{ env.SERVICE_NAMESPACE }}
          CONTAINER_REGISTRY: ecomly
          CONNECTION_STRING: ${{ secrets.PRODUCTION_DATABASE_CONNECTION_STRING_V3 }}
          ACCOUNTS_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ECOMLY_SERVICE_BUS_CONNECTION_STRING }}
          REGIONS_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ECOMLY_SERVICE_BUS_CONNECTION_STRING }}
          WALLETS_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ECOMLY_SERVICE_BUS_CONNECTION_STRING }}
          HOLDERS_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ANIS_HOLDERS_PRIMARY_SERVICE_BUS_CONNECTION_STRING }}

          IMAGE_TAG: ${{ github.sha }}
      - name: Azure login
        uses: azure/login@v1.4.3
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}
      - uses: Azure/aks-set-context@v3
        with:
          cluster-name: anis-primary
          resource-group: ${{ secrets.CLUSTER_RESOURCE_GROUP }}
      - uses: Azure/k8s-deploy@v4
        name: Deploys application
        with:
          action: deploy
          images: ecomly.azurecr.io/${{ env.SERVICE_NAME }}:${{ github.sha }}
          manifests: |
            deployment-production-v3.yaml
          namespace: ${{ env.SERVICE_NAMESPACE }}
      - name: logout
        if: always()
        run: |
          az logout
