﻿using Anis.Cashflow.Report.Application.Enums;
using Anis.Cashflow.Report.Domain.Enums.FilterEnums;
using MediatR;

namespace Anis.Cashflow.Report.Application.Features.Management.Filter
{
    public record FilterQuery(
        string LocationId,
        SubscriptionType Type,
        CurrencyType Currency,
        string PhoneNumber,
        bool DebtOnly,
        int CurrentPage,
        WalletTypeFilter WalletType,
        CashAndHoldersFilter CashAndHoldersFilter,
        string? HolderId,
        string? OperatorId,
        bool ShowStoresOnly,
        int PageSize = 25) : IRequest<FilterResponse>;
}
