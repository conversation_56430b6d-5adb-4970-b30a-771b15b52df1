﻿using Calzolari.Grpc.AspNetCore.Validation;

namespace Anis.Cashflow.Report.Grpc.Validatiors.Main
{
    public static class ValidationContainer
    {
        public static IServiceCollection AddValidators(this IServiceCollection services)
        {
            services.AddGrpcValidation();

            services.AddValidator<FilterRequestValidator>();

            services.AddValidator<IndexConsumerRequestValidator>();

            services.AddValidator<CashflowFilterRequestValidator>();

            return services;
        }
    }
}
