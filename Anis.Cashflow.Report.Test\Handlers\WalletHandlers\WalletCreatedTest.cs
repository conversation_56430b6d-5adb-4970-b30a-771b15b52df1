﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Holders;
using Anis.Cashflow.Report.Test.Fakers.Wallets.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.WalletHandlers
{

    public class WalletCreatedTest : TestBase
    {
        public WalletCreatedTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task HandleWalletCreated_CreateWalletWithValidData_SuccessHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
             .Generate();

            var createAccount = new AccountFaker()
             .RuleFor(f => f.LocationId, createRegion.Id)
             .Generate();

            var createSubscription = new SubscriptionFaker()
             .RuleFor(f => f.AccountId, createAccount.Id)
             .Generate();

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var walletCreatedDataFaker = new WalletCreatedDataFaker()
             .RuleFor(f => f.SubscriptionId, createSubscription.Id)
             .RuleFor(f => f.RegionId, createRegion.Id)
             .Generate();

            var messageBody = new MessageBody<WalletCreatedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 1,
                Type = EventType.WalletCreated,
                Data = walletCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbWallet = await context.Wallets.SingleAsync();

            //assert
            Assert.True(isHandled);

            WalletAssert.AssertEquality(dbWallet, messageBody);
        }

        [Fact]
        public async Task HandleWalletCreated_CreateWalletIsExist_NotHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
            .RuleFor(f => f.Balance, 5000)
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var walletCreatedDataFaker = new WalletCreatedDataFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, creatWallet.SubscriptionId)
               .Generate();

            var messageBody = new MessageBody<WalletCreatedData>
            {
                AggregateId = creatWallet.Id,
                Sequence = 1,
                Type = EventType.WalletCreated,
                Data = walletCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbWallet = await context.Wallets.SingleOrDefaultAsync();

            //assert
            Assert.True(isHandled);

            Assert.NotNull(dbWallet);

            WalletAssert.AssertEquality(creatWallet, dbWallet);
        }

        [Fact]
        public async Task HandleWalletCreated_CreateWalletSubscriptionNotFound_NotHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var walletCreatedDataFaker = new WalletCreatedDataFaker()
               .Generate();

            var messageBody = new MessageBody<WalletCreatedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 1,
                Type = EventType.WalletCreated,
                Data = walletCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbwallet = await context.Wallets.SingleOrDefaultAsync();

            //assert
            Assert.False(isHandled);

            Assert.Null(dbwallet);
        }

        [Fact]
        public async Task HandleWalletCreated_CreateWalletRegionNotFound_NotHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
               .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Regions.AddAsync(createRegion);

            await context.Accounts.AddAsync(createAccount);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var walletCreatedDataFaker = new WalletCreatedDataFaker()
            .RuleFor(f => f.RegionId, Guid.NewGuid().ToString())
            .RuleFor(f => f.SubscriptionId, createSubscription.Id)
            .Generate();

            var messageBody = new MessageBody<WalletCreatedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 1,
                Type = EventType.WalletCreated,
                Data = walletCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbwallet = await context.Wallets.SingleOrDefaultAsync();

            //assert
            Assert.False(isHandled);

            Assert.Null(dbwallet);
        }

        [Fact]
        public async Task HandleWalletCreated_CreateWalletWithValidDataAndHolderIdIsNotNullAndItIsExist_SuccessHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
             .Generate();

            var createAccount = new AccountFaker()
             .RuleFor(f => f.LocationId, createRegion.Id)
             .Generate();

            var createSubscription = new SubscriptionFaker()
             .RuleFor(f => f.AccountId, createAccount.Id)
             .Generate();

            var holderFaker = new HolderFaker().Generate();

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.Holders.AddAsync(holderFaker);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var walletCreatedDataFaker = new WalletCreatedDataFaker()
             .RuleFor(f => f.SubscriptionId, createSubscription.Id)
             .RuleFor(f => f.RegionId, createRegion.Id)
             .RuleFor(f => f.HolderId, holderFaker.Id)
             .Generate();

            var messageBody = new MessageBody<WalletCreatedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 1,
                Type = EventType.WalletCreated,
                Data = walletCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbRegion = await context.Regions.SingleAsync();
            var dbAccount = await context.Accounts.SingleAsync();
            var dbSubscription = await context.Subscriptions.SingleAsync();
            var dbHolder = await context.Holders.SingleAsync();
            var dbWallet = await context.Wallets.SingleAsync();

            //assert
            Assert.True(isHandled);

            RegionAssert.AssertEquality(createRegion, dbRegion);

            EntityAssert.AssertEquality(createAccount, dbAccount);

            EntityAssert.AssertEquality(createSubscription, dbSubscription, createAccount.Id);

            HolderAssert.AssertEquality(holderFaker, dbHolder);

            WalletAssert.AssertEquality(dbWallet, messageBody);
        }

        [Fact]
        public async Task HandleWalletCreated_CreateWalletWithValidDataAndHolderIdIsNotNullAndItIsNotExist_ReturnFalseAndNotHandleIt()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
             .Generate();

            var createAccount = new AccountFaker()
             .RuleFor(f => f.LocationId, createRegion.Id)
             .Generate();

            var createSubscription = new SubscriptionFaker()
             .RuleFor(f => f.AccountId, createAccount.Id)
             .Generate();

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var walletCreatedDataFaker = new WalletCreatedDataFaker()
             .RuleFor(f => f.SubscriptionId, createSubscription.Id)
             .RuleFor(f => f.RegionId, createRegion.Id)
             .RuleFor(f => f.HolderId, Guid.NewGuid().ToString())
             .Generate();

            var messageBody = new MessageBody<WalletCreatedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 1,
                Type = EventType.WalletCreated,
                Data = walletCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbRegion = await context.Regions.SingleAsync();
            var dbAccount = await context.Accounts.SingleAsync();
            var dbSubscription = await context.Subscriptions.SingleAsync();
            var dbHolder = await context.Holders.SingleOrDefaultAsync();
            var dbWallet = await context.Wallets.SingleOrDefaultAsync();

            //assert
            Assert.False(isHandled);

            RegionAssert.AssertEquality(createRegion, dbRegion);

            EntityAssert.AssertEquality(createAccount, dbAccount);

            EntityAssert.AssertEquality(createSubscription, dbSubscription, createAccount.Id);

            Assert.Null(dbHolder);

            Assert.Null(dbWallet);
        }
    }
}
