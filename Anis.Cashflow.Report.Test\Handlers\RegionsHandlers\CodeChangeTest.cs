﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.RegionsHandlers
{
    public class CodeChangeTest : TestBase
    {
        public CodeChangeTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task CodeChange_RegionChanged_ReturnValid()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 1)
                                          .RuleFor(f => f.Code, "010203")
                                          .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<RegionCodeChangedData>()
            {
                AggregateId = aggregateId,
                Sequence = 2,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionCodeChanged,
                Data = new RegionCodeChangedData("020304"),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            var region = await context.Regions.SingleAsync();

            Assert.True(isHandled);

            Assert.NotNull(region);

            RegionAssert.AssertEquality(messageBody, region, createRegion, 2);
        }

        [Fact]
        public async Task CodeChange_CodeChangeForNonExistedRegion_EventNotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var messageBody = new MessageBody<RegionCodeChangedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionCodeChanged,
                Data = new RegionCodeChangedData("020304"),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            Assert.False(isHandled);

            var region = await context.Regions.SingleOrDefaultAsync();

            Assert.Null(region);
        }

        [Fact]
        public async Task CodeChange_EventArrivedWithNotValidSequence_EventNotHandled()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 1)
                                          .RuleFor(f => f.ArabicName, "arabic")
                                          .RuleFor(f => f.EnglishName, "english")
                                          .RuleFor(f => f.Code, "010203")
                                          .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<RegionCodeChangedData>()
            {
                AggregateId = aggregateId,
                Sequence = 4,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionCodeChanged,
                Data = new RegionCodeChangedData("020304"),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            var region = await context.Regions.SingleAsync();

            Assert.False(isHandled);

            RegionAssert.AssertEquality(createRegion, region, 1);
        }

        [Fact]
        public async Task CodeChange_EventArrivedWithOldSequence_EventHandled()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 3)
                                          .RuleFor(f => f.ArabicName, "arabic")
                                          .RuleFor(f => f.EnglishName, "english")
                                          .RuleFor(f => f.Code, "010203")
                                          .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<RegionCodeChangedData>()
            {
                AggregateId = aggregateId,
                Sequence = 1,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionCodeChanged,
                Data = new RegionCodeChangedData("020304"),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            var region = await context.Regions.SingleAsync();

            Assert.True(isHandled);

            RegionAssert.AssertEquality(createRegion, region, 3);
        }
    }
}
