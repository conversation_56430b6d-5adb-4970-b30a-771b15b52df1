﻿using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Test.Live.OperatorBusinessLinkDemo;
using Anis.Cashflow.Report.Test.Live.Protos;
using Anis.Cashflow.Report.Test.Live.Protos.WalltesDemoEvent;
using Ecom.Cards.Grpc.Demo.V2.Regions;

namespace Anis.Cashflow.Report.Test.Live.Asserts
{
    internal class DemoEventAsserts
    {
        internal static void AssertEquality(UnconfirmeAccountRequest request, Account dbAccount)
        {
            Assert.Equal(request.AggregateId, dbAccount.Id);
            Assert.Equal(request.Sequence, dbAccount.Sequence);
        }

        public static void AssertEquality(
            ConfirmeAccountRequest request,
            Account dbAccount)
        {
            Assert.Equal(request.AggregateId, dbAccount.Id);
            Assert.Equal(request.Sequence, dbAccount.Sequence);
            Assert.Equal(request.Number, dbAccount.Number);
            Assert.Equal(request.LocationId, dbAccount.LocationId);
            Assert.Equal(request.OwnerName, dbAccount.OwnerName);
            Assert.Equal(request.Email, dbAccount.Email);
        }

        public static void AssertEquality(
            ChangeCodeRequest request,
            Region dbRegion,
            long sequence = 1)
        {
            Assert.Equal(request.AggregateId, dbRegion.Id);
            Assert.Equal(2, dbRegion.Sequence);
            Assert.Equal(request.Code, dbRegion.Code);
        }

        public static void AssertEquality(
            CreateRequest request,
            Region dbRegion)
        {
            Assert.Equal(request.AggregateId, dbRegion.Id);
            Assert.Equal(request.Code, dbRegion.Code);
            Assert.Equal(request.ArabicName, dbRegion.ArabicName);
            Assert.Equal(request.EnglishName, dbRegion.EnglishName);
        }

        public static void AssertEquality(
            UpdateAccountRequest request,
            Account dbAccount)
        {
            Assert.Equal(request.AggregateId, dbAccount.Id);
            Assert.Equal(request.Sequence, dbAccount.Sequence);
            Assert.Equal(request.Number, dbAccount.Number);
            Assert.Equal(request.LocationId, dbAccount.LocationId);
        }

        public static void AssertTransactionCreatedEquality(
             Wallet walletFaker,
             Wallet dbWallet,
             TransactionRequest request)
        {
            Assert.NotNull(dbWallet);

            Assert.Equal(walletFaker.Id, dbWallet.Id);
            Assert.Equal(walletFaker.Sequence + 1, dbWallet.Sequence);
            Assert.Equal(walletFaker.CurrencyType, dbWallet.CurrencyType);
            Assert.Equal(walletFaker.SubscriptionId, dbWallet.SubscriptionId);
            Assert.Equal(walletFaker.Type, dbWallet.Type);
            Assert.Equal(walletFaker.RegionId, dbWallet.RegionId);
            Assert.Equal(request.AggregateId, dbWallet.Id);
            Assert.Equal(request.Sequence, dbWallet.Sequence);
        }

        public static void AssertEquality(
            Wallet dbWallet,
            TransactionRequest request,
            Wallet walletFaker,
            bool isDebit = false
            )
        {
            AssertTransactionCreatedEquality(walletFaker, dbWallet, request);

            Assert.Equal(walletFaker.CurrentUrgent, dbWallet.CurrentUrgent);
            Assert.Equal(walletFaker.CurrentDelayed, dbWallet.CurrentDelayed);

            if (isDebit)
                Assert.Equal(walletFaker.Balance + (decimal)request.Debit, dbWallet.Balance);

            else
                Assert.Equal(walletFaker.Balance - (decimal)request.Credit, dbWallet.Balance);
        }

        public static void AssertUrgentOrDelayedEquality(
        Wallet dbWallet,
        TransactionRequest request,
        Wallet walletFaker,
        bool isDebit = false,
        bool isUrgent = false
      )
        {
            AssertTransactionCreatedEquality(walletFaker, dbWallet, request);

            if (isDebit)
            {
                Assert.Equal(walletFaker.Balance + (decimal)request.Debit, dbWallet.Balance);

                if (isUrgent)
                    Assert.Equal(walletFaker.CurrentUrgent + (decimal)request.Debit, dbWallet.CurrentUrgent);
                else
                    Assert.Equal(walletFaker.CurrentDelayed + (decimal)request.Debit, dbWallet.CurrentDelayed);

            }
            else
                Assert.Equal(walletFaker.Balance - (decimal)request.Credit, dbWallet.Balance);
        }

        public static void AssertRefundUrgentOrDelayedEquality(
          Wallet dbWallet,
          TransactionRequest request,
          Wallet walletFaker,
          bool isDebit = false,
          bool isUrgent = false
          )
        {
            AssertTransactionCreatedEquality(walletFaker, dbWallet, request);

            if (isDebit)
                Assert.Equal(walletFaker.Balance + (decimal)request.Debit, dbWallet.Balance);

            else
            {
                Assert.Equal(walletFaker.Balance - (decimal)request.Credit, dbWallet.Balance);

                if (isUrgent)
                    Assert.Equal(walletFaker.CurrentUrgent - (decimal)request.Credit, dbWallet.CurrentUrgent);
                else
                    Assert.Equal(walletFaker.CurrentDelayed - (decimal)request.Credit, dbWallet.CurrentDelayed);
            }
        }

        internal static void AssertEquality(WalletRequest request, Wallet dbWallet)
        {
            Assert.NotNull(dbWallet);

            Assert.Equal(request.AggregateId, dbWallet.Id);
            Assert.Equal(request.Type, dbWallet.Type.ToString());
            Assert.Equal(request.SubscriptionId, dbWallet.SubscriptionId);
            Assert.Equal(request.RegionId, dbWallet.RegionId);
            Assert.Equal(1, dbWallet.Sequence);
            Assert.Equal(0, dbWallet.Balance);
            Assert.Equal(0, dbWallet.NetBalance);
            Assert.Equal(0, dbWallet.CurrentUrgent);
            Assert.Equal(0, dbWallet.CurrentDelayed);
            Assert.Equal(request.CurrencyType, (CurrencyType)dbWallet.CurrencyType);
        }

        public static void AssertEquality(
                ChangeAccountPhoneRequest request,
                Account dbAccount)
        {
            Assert.Equal(request.AggregateId, dbAccount.Id);
            Assert.Equal(request.Sequence, dbAccount.Sequence);
            Assert.Equal(request.Phone, dbAccount.Phone);

        }

        public static void AssertEquality(
        ChangeAccountEmailRequest request,
        Account dbAccount)
        {
            Assert.Equal(request.AggregateId, dbAccount.Id);
            Assert.Equal(request.Sequence, dbAccount.Sequence);
            Assert.Equal(request.Email, dbAccount.Email);

        }

        public static void AssertEquality(
        Protos.Holder.CreateRequest request,
        Holder dbHolder)
        {
            Assert.NotNull(dbHolder);

            Assert.Equal(request.Id, dbHolder.Id);
            Assert.Equal(1, dbHolder.Sequence);
            Assert.Equal(request.ArabicName, dbHolder.ArabicName);
            Assert.Equal(request.EnglishName, dbHolder.EnglishName);
        }

        public static void AssertEquality(
        Protos.Holder.ChangeNameRequest request,
        Holder dbHolder)
        {
            Assert.NotNull(dbHolder);

            Assert.Equal(request.Id, dbHolder.Id);
            Assert.Equal(request.Sequence, dbHolder.Sequence);
            Assert.Equal(request.ArabicName, dbHolder.ArabicName);
            Assert.Equal(request.EnglishName, dbHolder.EnglishName);
        }

        public static void AssertEquality(
         Holder holderFaker,
         Holder dbHolder)
        {
            Assert.NotNull(dbHolder);

            Assert.Equal(holderFaker.Id, dbHolder.Id);
            Assert.Equal(2, dbHolder.Sequence);
            Assert.Equal(holderFaker.ArabicName, dbHolder.ArabicName);
            Assert.Equal(holderFaker.EnglishName, dbHolder.EnglishName);
        }

        public static void AssertEquality(NotifyRequest request,
            SubscriptionWallet dbSubscriptionWallet)
        {
            Assert.NotNull(dbSubscriptionWallet);
            Assert.Equal(request.BusinessWalletId, dbSubscriptionWallet.WalletId);
            Assert.Equal(request.OperatorSubscriptionId, dbSubscriptionWallet.SubscriptionId);
        }
    }
}
