﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Wallets.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;
using TransactionType = Anis.Cashflow.Report.Infra.Services.Const.TransactionType;

namespace Anis.Cashflow.Report.Test.Handlers.WalletHandlers
{
    public class OtherTransactionTest : TestBase
    {
        public OtherTransactionTest(ITestOutputHelper output) : base(output)
        {
        }

        [Theory]
        [InlineData(EventType.AllowedDebtUpdated)]
        [InlineData(EventType.AllowedDebtEnabled)]
        [InlineData(EventType.AllowedDebtDisabled)]
        public async Task HandleOtherTransaction_SequenceUpdate_SuccessHandle(
            string eventType)
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 5000)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<Object>
            {
                AggregateId = creatWallet.Id,
                Sequence = creatWallet.Sequence + 1,
                Type = eventType,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 
            Assert.True(isHandled);

            WalletAssert.AssertEquality(creatWallet, dbWallet, sequence: 2);
        }

        [Fact]
        public async Task HandleOtherTransaction_UpdateWalletWithNotValidSequence_NotHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 5000)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var transactionCreatedDataFaker = new TransactionCreatedDataFaker()
              .RuleFor(f => f.Credit, 1000)
              .RuleFor(f => f.Value, -1000)
               .RuleFor(f => f.Type, TransactionType.BlackFridayGift)
              .Generate();

            var messageBody = new MessageBody<object>
            {
                AggregateId = creatWallet.Id,
                Sequence = creatWallet.Sequence + 3,
                Type = EventType.AllowedDebtUpdated,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 
            Assert.False(isHandled);

            WalletAssert.AssertEquality(creatWallet, dbWallet, sequence: creatWallet.Sequence);
        }

        [Fact]
        public async Task HandleOtherTransaction_WalletIsNotExist_NotHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 5000)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var transactionCreatedDataFaker = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Debit, 1000)
             .RuleFor(f => f.Value, 1000)
              .RuleFor(f => f.Type, TransactionType.BlackFridayGift)
             .Generate();

            var messageBody = new MessageBody<object>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = creatWallet.Sequence + 1,
                Type = EventType.AllowedDebtUpdated,
                Data = transactionCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 
            Assert.False(isHandled);

            WalletAssert.AssertEquality(creatWallet, dbWallet, sequence: creatWallet.Sequence);
        }
    }
}
