using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.EventsData.SubscriptionWallets;

namespace Anis.Cashflow.Report.Test.Asserts
{
    public static class SubscriptionWalletAssert
    {
        public static void AssertEquality(
            NotificationBody<BusinessAddedData> notificationBody,
            SubscriptionWallet dbSubscriptionWallet,
            Subscription dbSubscription)
        {

            Assert.Equal(notificationBody.OperatorSubscriptionId, dbSubscriptionWallet.SubscriptionId);
            Assert.Equal(notificationBody.StateVersion, dbSubscription.StateVersion);
            Assert.Equal(NotificationType.BusinessAdded, notificationBody.Type);
            Assert.Equal(1, notificationBody.DataVersion);

            var data = notificationBody.Data!;

            Assert.Equal(data.BusinessWalletId, dbSubscriptionWallet.WalletId);

            Assert.Equal(data.LinkDateTime, dbSubscriptionWallet.LinkDateTime!.Value, precision: TimeSpan.FromMinutes(1));
        }

        public static void AssertEquality(
            SubscriptionWallet subscriptionWallet,
            SubscriptionWallet dbSubscriptionWallet,
            Subscription dbSubscription,
            long sequence = 1)
        {
            Assert.Equal(subscriptionWallet.Id, dbSubscriptionWallet.Id);
            Assert.Equal(subscriptionWallet.SubscriptionId, dbSubscriptionWallet.SubscriptionId);
            Assert.Equal(subscriptionWallet.WalletId, dbSubscriptionWallet.WalletId);
            Assert.Equal(subscriptionWallet.LinkDateTime, dbSubscriptionWallet.LinkDateTime);

            Assert.Equal(sequence, dbSubscription.StateVersion);
            Assert.Equal(subscriptionWallet.SubscriptionId, dbSubscription.Id);
        }
    }
}