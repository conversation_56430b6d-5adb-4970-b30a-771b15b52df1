﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Accounts.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.AccountHandlers
{
    public class AccountPhoneNumberChangeTest : TestBase
    {
        public AccountPhoneNumberChangeTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task ChangePhoneAccount_ChangePhoneValidAccoun_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var region = new RegionFaker().Generate();

            var account = new AccountFaker()
                        .RuleFor(r => r.LocationId, region.Id)
                        .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddAsync(account);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var newPhone = new AccountPhoneNumberChangedDataFaker()
                .RuleFor(a => a.Phone, "**********")
                .Generate();

            var messageBody = new MessageBody<AccountPhoneNumberChangedData>()
            {
                AggregateId = account.Id,
                Sequence = 2,
                Type = EventType.AccountPhoneNumberChanged,
                Data = newPhone,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };
            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(messageBody, dbAccount: dbAccount);
        }

        [Fact]
        public async Task ChangePhoneAccount_ChangePhoneNotValidAccoun_NotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var region = new RegionFaker().Generate();

            var account = new AccountFaker()
                        .RuleFor(r => r.LocationId, region.Id)
                        .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddAsync(account);

            await context.SaveChangesAsync();

            var newPhone = new AccountPhoneNumberChangedDataFaker()
                .RuleFor(a => a.Phone, "**********")
                .Generate();

            var messageBody = new MessageBody<AccountPhoneNumberChangedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.AccountPhoneNumberChanged,
                Data = newPhone,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };
            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount: account, dbAccount: dbAccount);
        }

        [Fact]
        public async Task ChangePhoneAccount_ChangePhonValidAccounAndInvalidGapSequence_NotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var region = new RegionFaker().Generate();

            var account = new AccountFaker()
                        .RuleFor(r => r.LocationId, region.Id)
                        .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddAsync(account);

            await context.SaveChangesAsync();

            var newPhone = new AccountPhoneNumberChangedDataFaker()
                .RuleFor(a => a.Phone, "**********")
                .Generate();

            var messageBody = new MessageBody<AccountPhoneNumberChangedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 5,
                Type = EventType.AccountPhoneNumberChanged,
                Data = newPhone,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };
            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount: account, dbAccount: dbAccount);
        }

        [Fact]
        public async Task ChangePhoneAccount_ChangePhonValidAccounAndInvalidOldSequence_NotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var region = new RegionFaker().Generate();

            var account = new AccountFaker()
                        .RuleFor(r => r.LocationId, region.Id)
                        .RuleFor(r => r.Sequence, 4)
                        .Generate();

            await context.Regions.AddAsync(region);

            await context.Accounts.AddAsync(account);

            await context.SaveChangesAsync();

            var newPhone = new AccountPhoneNumberChangedDataFaker()
                .RuleFor(a => a.Phone, "**********")
                .Generate();

            var messageBody = new MessageBody<AccountPhoneNumberChangedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.AccountPhoneNumberChanged,
                Data = newPhone,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };
            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount: account, dbAccount: dbAccount);
        }
    }
}
