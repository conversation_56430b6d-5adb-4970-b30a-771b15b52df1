﻿using Anis.Cashflow.Report.Application.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace Anis.Cashflow.Report.Infra.Persistence
{
    public class DatabaseBuilder : IHostedService
    {
        private readonly IServiceProvider _provider;
        private readonly ConfigModel _config;

        public DatabaseBuilder(IServiceProvider provider, ConfigModel config)
        {
            _provider = provider;
            _config = config;
        }

        public async Task StartAsync(CancellationToken cancellationToken)
        {
            using var scope = _provider.CreateScope();

            var appDbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            await appDbContext.Database.MigrateAsync();

        }

        public Task StopAsync(CancellationToken cancellationToken)
        {
            return Task.CompletedTask;
        }
    }
}
