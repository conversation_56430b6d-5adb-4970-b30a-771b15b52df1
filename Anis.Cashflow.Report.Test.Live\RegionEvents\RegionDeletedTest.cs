﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Ecom.Cards.Grpc.Demo.V2.Regions;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.RegionEvents
{
    public class RegionDeletedTest : TestBase
    {
        public RegionDeletedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Delete_EventArrivedWithNotValidSequence_EventNotHandled()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var channel = GrpcChannel.ForAddress(Address.Base);

            var grpcClient = new RegionsDemoEvents.RegionsDemoEventsClient(channel);

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 2)
                                          .Generate();


            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var request = new DeleteRequest()
            {
                AggregateId = aggregateId,
                Sequence = 4,
            };

            await grpcClient.DeleteAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<RegionEventsListener>().CloseProccessorAsync();

            var region = await context.Regions.SingleAsync();

            Assert.Equal(2, region.Sequence);
        }
    }
}
