﻿using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.EventsData.SubscriptionWallets;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.OperatorBusinessLinks.NotificationData;
using Anis.Cashflow.Report.Test.Fakers.SubscriptionWallets;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.OperatorBusinessLinkHandlers
{
    public class BusinessAddedTest : TestBase
    {
        public BusinessAddedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Create_BusinessAddedWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessAddedDataFaker()
                .RuleFor(f => f.BusinessWalletId, creatWallet.Id)
                .Generate();

            var notificationBody = new NotificationBody<BusinessAddedData>
            {
                OperatorSubscriptionId = createSubscription.Id,
                StateVersion = 1,
                Type = NotificationType.BusinessAdded,
                Data = businessAddedData,
                DateTime = DateTime.UtcNow,
                DataVersion = 1,
            };

            var isHandled = await mediator.Send(notificationBody);

            var dbAccounts = await context.Accounts.SingleAsync(f => f.Id == createAccount.Id);
            var dbSubscription = await context.Subscriptions.SingleAsync(f => f.Id == createSubscription.Id);
            var dbWallets = await context.Wallets.SingleAsync(f => f.Id == creatWallet.Id);
            var dbSubscriptionWallet = await context.SubscriptionWallets.SingleAsync(f => f.WalletId == businessAddedData.BusinessWalletId);

            Assert.True(isHandled);

            SubscriptionWalletAssert.AssertEquality(notificationBody, dbSubscriptionWallet, dbSubscription);
        }

        [Fact]
        public async Task Create_BusinessAddedWithInvalidData_ReturnFalse()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessAddedDataFaker()
                .Generate();

            var notificationBody = new NotificationBody<BusinessAddedData>
            {
                OperatorSubscriptionId = createSubscription.Id,
                StateVersion = 1,
                Type = NotificationType.BusinessAdded,
                Data = businessAddedData,
                DateTime = DateTime.UtcNow,
                DataVersion = 1,
            };

            var isHandled = await mediator.Send(notificationBody);
            Assert.False(isHandled);
        }

        [Fact]
        public async Task Create_BusinessAddedWithInvalidStateVersion_ReturnFalse()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessAddedDataFaker()
                .RuleFor(f => f.BusinessWalletId, creatWallet.Id)
                .Generate();

            var notificationBody = new NotificationBody<BusinessAddedData>
            {
                OperatorSubscriptionId = createSubscription.Id,
                StateVersion = 2, // Invalid state version
                Type = NotificationType.BusinessAdded,
                Data = businessAddedData,
                DateTime = DateTime.UtcNow,
                DataVersion = 1,
            };

            var isHandled = await mediator.Send(notificationBody);
            Assert.False(isHandled);
        }

        [Fact]
        public async Task Create_BusinessAddedWithInvalidStateVersion_ReturnTrue()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .RuleFor(f => f.StateVersion, 3)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            await context.Subscriptions.AddAsync(createSubscription);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessAddedDataFaker()
                .RuleFor(f => f.BusinessWalletId, creatWallet.Id)
                .RuleFor(f => f.BusinessSubscriptionId, createSubscription.Id)
                .Generate();

            var notificationBody = new NotificationBody<BusinessAddedData>
            {
                OperatorSubscriptionId = createSubscription.Id,
                StateVersion = 1, // Old state version
                Type = NotificationType.BusinessAdded,
                Data = businessAddedData,
                DateTime = DateTime.UtcNow,
                DataVersion = 1,
            };

            var isHandled = await mediator.Send(notificationBody);
            Assert.True(isHandled);
        }

        [Fact]
        public async Task Create_BusinessAddedWithInvalidSubscription_ReturnFalse()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .RuleFor(f => f.StateVersion, 3)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            await context.Subscriptions.AddAsync(createSubscription);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessAddedDataFaker()
                .RuleFor(f => f.BusinessWalletId, creatWallet.Id)
                .Generate();

            var notificationBody = new NotificationBody<BusinessAddedData>
            {
                OperatorSubscriptionId = "invalid-subscription-id", // Invalid subscription ID
                StateVersion = 1,
                Type = NotificationType.BusinessAdded,
                Data = businessAddedData,
                DateTime = DateTime.UtcNow,
                DataVersion = 1,
            };

            var isHandled = await mediator.Send(notificationBody);
            Assert.False(isHandled);
        }

        [Fact]
        public async Task Create_BusinessAddedWithInvalidWallet_ReturnFalse()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            await context.Subscriptions.AddAsync(createSubscription);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessAddedDataFaker()
                .RuleFor(f => f.BusinessWalletId, "invalid-wallet-id") // Invalid wallet ID
                .Generate();

            var notificationBody = new NotificationBody<BusinessAddedData>
            {
                OperatorSubscriptionId = createSubscription.Id,
                StateVersion = 1,
                Type = NotificationType.BusinessAdded,
                Data = businessAddedData,
                DateTime = DateTime.UtcNow,
                DataVersion = 1,
            };
            var isHandled = await mediator.Send(notificationBody);
            Assert.False(isHandled);
        }

        [Fact]
        public async Task Create_BusinessAddedWithExistingLink_ReturnsTrue()
        {
            using var scope = Factory.Services.CreateScope();
            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();
            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .RuleFor(f => f.StateVersion, 2)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var SubscriptionWallet = new SubscriptionWalletFaker()
                .RuleFor(f => f.Wallet, creatWallet)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            await context.SubscriptionWallets.AddAsync(SubscriptionWallet);
            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessAddedDataFaker()
                .RuleFor(f => f.BusinessWalletId, creatWallet.Id)
                .Generate();

            var notificationBody = new NotificationBody<BusinessAddedData>
            {
                OperatorSubscriptionId = createSubscription.Id,
                StateVersion = 3,
                Type = NotificationType.BusinessAdded,
                Data = businessAddedData,
                DateTime = DateTime.UtcNow,
                DataVersion = 1,
            };

            var isHandled = await mediator.Send(notificationBody);

            var dbSubscriptionWallet = await context.SubscriptionWallets.Include(c => c.Subscription)
                .SingleAsync(f => f.WalletId == businessAddedData.BusinessWalletId);

            Assert.True(isHandled);
            Assert.Equal(3, dbSubscriptionWallet.Subscription.StateVersion);
        }

    }
}
