﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.Features.Management.CashflowFilter
{
    public class CashFlowFilterHandler : IRequestHandler<CashFlowFilterQuery, CashFlowFilterResponse>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly BlackListSubscriptionsModel _blackListSubscriptionsModel;

        public CashFlowFilterHandler(IUnitOfWork unitOfWork, BlackListSubscriptionsModel blackListSubscriptionsModel)
        {
            _unitOfWork = unitOfWork;
            _blackListSubscriptionsModel = blackListSubscriptionsModel;
        }

        public async Task<CashFlowFilterResponse> Handle(CashFlowFilterQuery request, CancellationToken cancellationToken)
        {
            return await _unitOfWork.Wallets.FilterAsync(request, _blackListSubscriptionsModel, cancellationToken);
        }
    }
}
