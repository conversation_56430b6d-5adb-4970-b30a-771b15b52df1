﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Wallets.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.WalletHandlers
{
    public class RebuildWalletTest : TestBase
    {
        public RebuildWalletTest(ITestOutputHelper output) : base(output)
        { }

        [Fact]
        public async Task HandleRebuildWallet_RebuildCreateWithValidData_SuccessHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
             .Generate();

            var createAccount = new AccountFaker()
             .RuleFor(f => f.LocationId, createRegion.Id)
             .Generate();

            var createSubscription = new SubscriptionFaker()
             .RuleFor(f => f.AccountId, createAccount.Id)
             .Generate();

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var walletCreatedDataFaker = new WalletCreatedDataFaker()
             .RuleFor(f => f.SubscriptionId, createSubscription.Id)
             .RuleFor(f => f.RegionId, createRegion.Id)
             .Generate();

            var detailedSummations = new List<DetailedSummationModel>
            {
                new DetailedSummationModel
                {
                    Source = "ecom-cards",
                    TotalCredit = 500,
                    TotalDebit = 1000,
                    Type =TransactionType.UrgentTopUp
                },
                new DetailedSummationModel
                {
                    Source = "ecom-cards",
                    TotalCredit = 200,
                    TotalDebit = 500,
                    Type =TransactionType.RefundUrgentTopUp
                },
                new DetailedSummationModel
                {
                    Source = "ecom-cards",
                    TotalCredit = 200,
                    TotalDebit = 700,
                    Type =TransactionType.DelayedTopUp
                },
                new DetailedSummationModel
                {
                    Source = "ecom-cards",
                    TotalCredit = 300,
                    TotalDebit = 900,
                    Type =TransactionType.RefundDelayedTopUp
                }
            };

            var model = new WalletInputModel
            {
                WalletId = Guid.NewGuid().ToString(),
                Sequence = 1,
                Type = WalletType.Normal,
                Balance = 500,
                CurrencyType = CurrencyType.Lyd,
                RegionId = createRegion.Id,
                SubscriptionId = createSubscription.Id,
                SubscriptionType = (int)SubscriptionType.Business,
                DetailedSummations = detailedSummations

            };

            var isHandled = await mediator.Send(model);

            var dbWallet = await context.Wallets.SingleAsync();

            //assert
            Assert.True(isHandled);

            WalletAssert.AssertEquality(dbWallet, model, currentDelayed: 1100, CurrentUrgent: 800);
        }
    }
}
