﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;
using Anis.Cashflow.Report.Domain.Exceptions;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Anis.Cashflow.Report.Application.MessageHandlers.WalletHandlers
{
    public class TransactionCreatedRebuildHandler : IRequestHandler<TransactionRebuildModel<string>, Unit>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<TransactionCreatedRebuildHandler> _logger;

        public TransactionCreatedRebuildHandler(IUnitOfWork unitOfWork, ILogger<TransactionCreatedRebuildHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<Unit> Handle(TransactionRebuildModel<string> request, CancellationToken cancellationToken)
        {
            var wallet = await _unitOfWork.Wallets.FindAsync(request.Models.First().AggregateId);

            if (wallet is null)
                _logger.LogCritical("Wallte is not found  aggregateId : {AggregateId} ", request.Models.First().AggregateId);

            else
            {
                foreach (var item in request.Models)
                {
                    _ = item.Type switch
                    {
                        WalletEventType.TransactionCreated => await HandleCreateTransaction(item, wallet),

                        _ => await HandleOtherAsync(item, wallet)
                    };
                }

                await _unitOfWork.SaveChangesAsync();
            }

            return Unit.Value;
        }

        public async Task<Unit> HandleCreateTransaction(MessageBody<string> message, Wallet wallet)
        {
            var body = JsonConvert.DeserializeObject<TransactionCreatedData>(message.Data)!;

            var result = body.Type switch
            {
                TransactionType.UrgentTopUp => HandleUrgentTopUpTransactionAsync(message, wallet),
                TransactionType.RefundUrgentTopUp => HandleRefundUrgentTopUpTransactionAsync(message, wallet),
                TransactionType.DelayedTopUp => HandleDelayedTopUpTransactionAsync(message, wallet),
                TransactionType.RefundDelayedTopUp => HandleRefundDelayedTopUpTransactionAsync(message, wallet),
                _ => HandleOtherTransactionAsync(message, wallet),
            };

            return await result;
        }

        #region Create Transaction 

        private async Task<Unit> HandleUrgentTopUpTransactionAsync(MessageBody<string> message, Wallet wallet)
        {
            var body = JsonConvert.DeserializeObject<TransactionCreatedData>(message.Data)!;

            if (wallet.Sequence != message.Sequence - 1)
            {
                if (wallet.Sequence >= message.Sequence)
                    return Unit.Value;
                else
                    throw new AppException(ExceptionStatusCode.DataLoss, $"Invalid wallet sequence with id {message.AggregateId} and  wallet sequence : {wallet.Sequence} message sequence : {message.Sequence}");
            }

            if (body.Credit > 0)
                wallet.UpdateCreditBalance(body.Credit);

            else if (body.Debit > 0)
            {
                wallet.UpdateDebitCurrentUrgent(body.Debit);
                wallet.UpdateDebitBalance(body.Debit);
            }

            return await Task.FromResult(Unit.Value);
        }

        private async Task<Unit> HandleRefundUrgentTopUpTransactionAsync(MessageBody<string> message, Wallet wallet)
        {
            var body = JsonConvert.DeserializeObject<TransactionCreatedData>(message.Data)!;

            if (wallet.Sequence != message.Sequence - 1)
            {
                if (wallet.Sequence >= message.Sequence)
                    return Unit.Value;
                else
                    throw new AppException(ExceptionStatusCode.DataLoss, $"Invalid wallet sequence with id {message.AggregateId} and  wallet sequence : {wallet.Sequence} message sequence : {message.Sequence}");
            }

            if (body.Credit > 0)
            {
                wallet.UpdateCreditCurrentUrgent(body.Credit);
                wallet.UpdateCreditBalance(body.Credit);
            }
            else if (body.Debit > 0)
            {
                wallet.UpdateDebitBalance(body.Debit);
            }

            return await Task.FromResult(Unit.Value);
        }

        private async Task<Unit> HandleDelayedTopUpTransactionAsync(MessageBody<string> message, Wallet wallet)
        {
            var body = JsonConvert.DeserializeObject<TransactionCreatedData>(message.Data)!;

            if (wallet.Sequence != message.Sequence - 1)
            {
                if (wallet.Sequence >= message.Sequence)
                    return Unit.Value;
                else
                    throw new AppException(ExceptionStatusCode.DataLoss, $"Invalid wallet sequence with id {message.AggregateId} and  wallet sequence : {wallet.Sequence} message sequence : {message.Sequence}");
            }

            if (body.Debit > 0)
            {
                wallet.UpdateDebitCurrentDelayed(body.Debit);
                wallet.UpdateDebitBalance(body.Debit);
            }
            else if (body.Credit > 0)
                wallet.UpdateCreditBalance(body.Credit);

            return await Task.FromResult(Unit.Value);
        }

        private async Task<Unit> HandleRefundDelayedTopUpTransactionAsync(MessageBody<string> message, Wallet wallet)
        {
            var body = JsonConvert.DeserializeObject<TransactionCreatedData>(message.Data)!;

            if (wallet.Sequence != message.Sequence - 1)
            {
                if (wallet.Sequence >= message.Sequence)
                    return Unit.Value;
                else
                    throw new AppException(ExceptionStatusCode.DataLoss, $"Invalid wallet sequence with id {message.AggregateId} and  wallet sequence : {wallet.Sequence} message sequence : {message.Sequence}");
            }

            if (body.Debit > 0)
                wallet.UpdateDebitBalance(body.Debit);

            else if (body.Credit > 0)
            {
                wallet.UpdateCreditCurrentDelayed(body.Credit, message.DateTime);
                wallet.UpdateCreditBalance(body.Credit);
            }

            return await Task.FromResult(Unit.Value);
        }

        private async Task<Unit> HandleOtherTransactionAsync(MessageBody<string> message, Wallet wallet)
        {
            var body = JsonConvert.DeserializeObject<TransactionCreatedData>(message.Data)!;

            if (wallet.Sequence != message.Sequence - 1)
            {
                if (wallet.Sequence >= message.Sequence)
                    return Unit.Value;
                else
                    throw new AppException(ExceptionStatusCode.DataLoss, $"Invalid wallet sequence with id {message.AggregateId} and  wallet sequence : {wallet.Sequence} message sequence : {message.Sequence}");
            }

            if (body.Debit > 0)
                wallet.UpdateDebitBalance(body.Debit);

            else if (body.Credit > 0)
                wallet.UpdateCreditBalance(body.Credit);

            else
                wallet.IncrementSequence();

            return await Task.FromResult(Unit.Value);
        }

        private async Task<Unit> HandleOtherAsync(MessageBody<string> message, Wallet wallet)
        {
            if (wallet.Sequence != message.Sequence - 1)
            {
                if (wallet.Sequence >= message.Sequence)
                    return Unit.Value;
                else
                    throw new AppException(ExceptionStatusCode.DataLoss, $"Invalid wallet sequence with id {message.AggregateId} and  wallet sequence : {wallet.Sequence} message sequence : {message.Sequence}");
            }

            wallet.IncrementSequence();

            return await Task.FromResult(Unit.Value);
        }

        #endregion
    }
}
