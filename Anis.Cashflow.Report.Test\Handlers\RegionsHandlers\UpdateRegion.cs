﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.RegionsHandlers
{
    public class UpdateRegion : TestBase
    {
        public UpdateRegion(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Update_RegionUpdated_ReturnValid()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 1)
                                          .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<RegionUpdatedData>()
            {
                AggregateId = aggregateId,
                Sequence = 2,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionUpdated,
                Data = new RegionUpdatedData(
                "ara name updated", "en name updated"),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            var region = await context.Regions.SingleAsync();

            Assert.True(isHandled);

            RegionAssert.AssertEquality(messageBody, region,createRegion, 2);
        }

        [Fact]
        public async Task Update_RegionUpdatedForNonExistedRegion_EventNotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var messageBody = new MessageBody<RegionUpdatedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 3,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionUpdated,
                Data = new RegionUpdatedData(
                "ara name updated", "en name updated"),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            Assert.False(isHandled);

            var region = await context.Regions.SingleOrDefaultAsync();


            Assert.Null(region);
        }

        [Fact]
        public async Task Update_EventArrivedWithNotValidSequence_EventNotHandled()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 2)
                                          .Generate();


            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<RegionUpdatedData>()
            {
                AggregateId = aggregateId,
                Sequence = 4,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionUpdated,
                Data = new RegionUpdatedData(
                "ara name updated", "en name updated"),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            var region = await context.Regions.SingleAsync();

            Assert.False(isHandled);

            RegionAssert.AssertEquality(createRegion, region, 2);
        }

        [Fact]
        public async Task Update_EventArrivedWithOldSequence_EventHandled()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var medaiator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 2)
                                          .Generate();


            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<RegionUpdatedData>()
            {
                AggregateId = aggregateId,
                Sequence = 1,
                UserId = Guid.NewGuid().ToString(),
                Type = EventType.RegionUpdated,
                Data = new RegionUpdatedData(
                "ara name updated", "en name updated"),
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await medaiator.Send(messageBody);

            var region = await context.Regions.SingleAsync();

            Assert.True(isHandled);

            RegionAssert.AssertEquality(createRegion, region, 2);
        }
    }
}
