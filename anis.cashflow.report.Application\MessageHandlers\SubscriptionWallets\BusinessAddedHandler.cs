﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.SubscriptionWallets;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.SubscriptionWallets
{
    public class BusinessAddedHandler : IRequestHandler<NotificationBody<BusinessAddedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public BusinessAddedHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> Handle(NotificationBody<BusinessAddedData> notification, CancellationToken cancellationToken)
        {
            var subscription = await _unitOfWork.Subscriptions.FindAsync(notification.OperatorSubscriptionId);
            if (subscription == null)
            {
                return false;
            }

            if (subscription.StateVersion != notification.StateVersion - 1)
                return subscription.StateVersion >= notification.StateVersion;

            var wallet = await _unitOfWork.Wallets.FindAsync(notification.Data!.BusinessWalletId);
            if (wallet == null)
            {
                return false;
            }

            var operatorBusinessLink = await _unitOfWork.SubscriptionWallets.GetBySubscriptionIdIdAndWalletIdAsync(notification.OperatorSubscriptionId, notification.Data!.BusinessWalletId);
            if (operatorBusinessLink == null)
            {
                operatorBusinessLink = new SubscriptionWallet(notification);

                await _unitOfWork.SubscriptionWallets.AddAsync(operatorBusinessLink);
            }

            subscription.IncrementStateVersion();
            await _unitOfWork.SaveChangesAsync();
            return true;
        }
    }
}
