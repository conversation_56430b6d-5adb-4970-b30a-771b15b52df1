﻿using Anis.Cashflow.Report.Application.Features.Management.CashflowFilter;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Domain.Entities;

namespace Anis.Cashflow.Report.Application.Contracts.Repositories
{
    public interface IWalletRepository : IAsyncRepository<Wallet>
    {
        Task<bool> AnyAsync(string walletId);
        Task<Wallet?> FindAsync(string walletId);
        Task<CashFlowFilterResponse> FilterAsync(CashFlowFilterQuery request, BlackListSubscriptionsModel model, CancellationToken cancellationToken);
    }
}
