﻿using Anis.Cashflow.Report.Grpc.ExceptionHandler;
using Anis.Cashflow.Report.Grpc.Interceptors;
using Anis.Cashflow.Report.Grpc.Protos.Client;
using Anis.Cashflow.Report.Grpc.Protos.Client.SpecialQueries;
using Anis.Cashflow.Report.Grpc.Protos.Clients;
using Anis.Cashflow.Report.Grpc.Protos.Clients.Holders;
using Anis.Cashflow.Report.Grpc.Validatiors.Main;
using Anis.Cashflow.Report.Infra.Services.GrpcServices.Protos.DailyLocationSales;
using Anis.Cashflow.Report.Infra.Services.GrpcServices.Protos.DailyWalletSales;
using Calzolari.Grpc.AspNetCore.Validation;

namespace Anis.Cashflow.Report.Grpc
{
    public static class GrpcContainer
    {
        public static IServiceCollection AddGrpcServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddGrpc();

            services.AddGrpc(option =>
            {
                option.Interceptors.Add<ThreadCultureInterceptor>();

                option.EnableMessageValidation();

                option.Interceptors.Add<ExceptionHandlingInterceptor>();
            });

            AddGrpcClients(services, configuration);

            services.AddValidators();

            return services;
        }

        private static void AddGrpcClients(IServiceCollection services, IConfiguration configuration)
        {
            services.AddGrpcClient<SpecialQueries.SpecialQueriesClient>((o) =>
            {
                o.Address = new Uri(configuration["ClientUrls:GateWayEventsHistoryClient"]);
            });

            services.AddGrpcClient<EventsHistory.EventsHistoryClient>((o) =>
            {
                o.Address = new Uri(configuration["ClientUrls:GateWayEventsHistoryClient"]);
            });

            services.AddGrpcClient<DailyLocationSale.DailyLocationSaleClient>((o) =>
            {
                o.Address = new Uri(configuration["ClientUrls:DailyLocationSaleClient"]);
            });

            services.AddGrpcClient<DailyWalletSale.DailyWalletSaleClient>((o) =>
            {
                o.Address = new Uri(configuration["ClientUrls:DailyWalletSaleClient"]);
            });

            services.AddGrpcClient<HoldersEventsHistory.HoldersEventsHistoryClient>((o) =>
            {
                o.Address = new Uri(configuration["ClientUrls:HoldersClient"]);
            });

            services.AddGrpcClient<NotificationHistoryOperatorToBusinessLink.NotificationHistoryOperatorToBusinessLinkClient>((o) =>
            {
                o.Address = new Uri(configuration["ClientUrls:NotificationOperatorBusinessLinkClient"]);
            });
        }
    }
}
