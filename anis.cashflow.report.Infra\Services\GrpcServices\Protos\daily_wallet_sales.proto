syntax = "proto3";

package anis.daily_wallet_sales;

import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Anis.Cashflow.Report.Infra.Services.GrpcServices.Protos.DailyWalletSales";

service DailyWalletSale{
	rpc GetAllWalletAverageSales(RequestAverageWalletSale) returns (ResponseAverageWalletSale);
}

message RequestAverageWalletSale{
	repeated  string wallet_id = 1;
	int32 days = 2;
}

message ResponseAverageWalletSale{
	repeated AverageWalletSaleList Average_wallet_sales = 4;
}
message AverageWalletSaleList{
	string wallet_id = 1;
	int32 days = 2;
	double average=3;
	repeated SalesPerDay sales_per_day = 4;
	double last_day_sales = 5;
}

message SalesPerDay{
	google.protobuf.Timestamp date = 1;
	double sales = 2;
}

enum Currency {
	Non  = 0;
	LYD  = 1;
	USD  = 2;
}