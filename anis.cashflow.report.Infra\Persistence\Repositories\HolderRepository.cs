﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Features.Management.GetHolders;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Infra.Helpers;
using Microsoft.EntityFrameworkCore;

namespace Anis.Cashflow.Report.Infra.Persistence.Repositories
{
    public class HolderRepository : AsyncRepository<Holder>, IHolderRepository
    {
        private readonly AppDbContext _appDbContext;

        public HolderRepository(AppDbContext appDbContext) : base(appDbContext)
        {
            _appDbContext = appDbContext;
        }

        public async Task<List<GetHoldersDTO>> GetHoldersDTOAsync(CancellationToken cancellationToken)
        {
            return await _appDbContext.Holders.Select(h => new GetHoldersDTO()
            {
                Id = h.Id,
                Name = CultureHelper.Read(h.ArabicName, h.EnglishName)
            }).AsNoTracking()
              .ToListAsync(cancellationToken);
        }

        public async Task<bool> IsExistAsync(string id)
        {
            return await _appDbContext.Holders.AnyAsync(h => h.Id == id);
        }
    }
}
