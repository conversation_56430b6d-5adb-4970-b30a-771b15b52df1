﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
	  
  </PropertyGroup>

  <ItemGroup>
    <None Remove="GrpcTest\queries.proto" />
    <None Remove="Protos\queries_consumer.proto" />
    <None Remove="Protos\queries_managment.proto" />
  </ItemGroup>

	<ItemGroup>
		<PackageReference Include="Bogus" Version="34.0.2" />
		<PackageReference Include="Calzolari.Grpc.Net.Client.Validation" Version="6.2.0" />
		<PackageReference Include="Grpc.Net.ClientFactory" Version="2.49.0" />
		<PackageReference Include="Grpc.Tools" Version="2.50.0">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="6.0.11" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="6.0.10" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.4.0" />
		<PackageReference Include="Moq" Version="4.18.2" />
		<PackageReference Include="coverlet.collector" Version="3.1.2">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Serilog.Sinks.XUnit" Version="3.0.3" />
		<PackageReference Include="xunit" Version="2.4.2" />
		<PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\anis.cashflow.report.Grpc\Anis.Cashflow.Report.Grpc.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Protobuf Include="Protos\queries_managment.proto" GrpcServices="Client" />
    <Protobuf Include="Protos\queries_consumer.proto" GrpcServices="Client" />
  </ItemGroup>

</Project>
