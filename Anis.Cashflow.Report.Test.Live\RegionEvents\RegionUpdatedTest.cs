﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Ecom.Cards.Grpc.Demo.V2.Regions;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.RegionEvents
{
    public class RegionUpdatedTest : TestBase
    {
        public RegionUpdatedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Update_RegionUpdated_ReturnValid()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var channel = GrpcChannel.ForAddress(Address.Base);

            var grpcClient = new RegionsDemoEvents.RegionsDemoEventsClient(channel);

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 2)
                                          .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var request = new UpdateRequest()
            {
                AggregateId = aggregateId,
                ArabicName = "ArabicName",
                EnglishName = "EnglishName",
                Sequence = 3
            };

            await grpcClient.UpdateAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<RegionEventsListener>().CloseProccessorAsync();

            var region = await context.Regions.SingleOrDefaultAsync();

            Assert.NotNull(region);

            Assert.Equal(request.AggregateId, region.Id);
            Assert.Equal(request.ArabicName, region.ArabicName);
            Assert.Equal(request.EnglishName, region.EnglishName);
        }
    }
}
