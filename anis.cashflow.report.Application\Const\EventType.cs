﻿namespace Anis.Cashflow.Report.Application.Const
{
    public class EventType
    {
        #region Region Events
        public const string RegionCreated = "RegionCreated";
        public const string RegionCodeChanged = "RegionCodeChanged";
        public const string RegionUpdated = "RegionUpdated";
        public const string RegionDeleted = "RegionDeleted";
        #endregion

        #region Account Events
        public const string AccountConfirmed = "AccountConfirmed";
        public const string AccountDeleted = "AccountDeleted";
        public const string SubscriptionCreated = "SubscriptionCreated";
        public const string SubscriptionUpdated = "SubscriptionUpdated";
        public const string SubscriptionRemoved = "SubscriptionRemoved";
        public const string AccountUnconfirmed = "AccountUnconfirmed";
        public const string AccountUpdated = "AccountUpdated";
        public const string AccountExpirationChanged = "AccountExpirationChanged";
        public const string AccountExpirationRemoved = "AccountExpirationRemoved";
        public const string AccountPriceDifferenceChanged = "AccountPriceDifferenceChanged";
        public const string ExpiryDateRemoved = "ExpiryDateRemoved";
        public const string ExpiryDateChanged = "ExpiryDateChanged";
        public const string AniscomActivated = "AniscomActivated";
        public const string AniscomDeactivated = "AniscomDeactivated";
        public const string AccountAffiliationSet = "AccountAffiliationSet";
        public const string AccountAffiliationRemoved = "AccountAffiliationRemoved";
        #endregion

        #region Wallet Events
        public const string TransactionCreated = "TransactionCreated";
        public const string WalletCreated = "WalletCreated";
        public const string AllowedDebtUpdated = "AllowedDebtUpdated";
        public const string AllowedDebtEnabled = "AllowedDebtEnabled";
        public const string AllowedDebtDisabled = "AllowedDebtDisabled";
        public const string AccountPhoneNumberChanged = "AccountPhoneNumberChanged";
        public const string AccountEmailChanged = "AccountEmailChanged";
        #endregion

        #region Holder Events

        public const string HolderCreated = "HolderCreated";
        public const string HolderDeleted = "HolderDeleted";
        public const string NameChanged = "NameChanged";
        public const string RegionAdded = "RegionAdded";
        public const string RegionDifferenceChanged = "RegionDifferenceChanged";
        public const string RegionRemoved = "RegionRemoved";
        public const string RegionUnLocked = "RegionUnLocked";
        public const string RegionLocked = "RegionLocked";

        #endregion

        #region SubscriptionWallet Events

        public const string BusinessAdded = "BusinessAdded";
        public const string BusinessRemoved = "BusinessRemoved";

        #endregion
    }
}
