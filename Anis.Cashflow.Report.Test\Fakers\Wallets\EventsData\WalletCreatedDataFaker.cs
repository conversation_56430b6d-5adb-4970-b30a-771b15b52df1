﻿using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;

namespace Anis.Cashflow.Report.Test.Fakers.Wallets.EventsData
{
    internal class WalletCreatedDataFaker : PrivateFaker<WalletCreatedData>
    {
        public WalletCreatedDataFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.SubscriptionId, Guid.NewGuid().ToString());
            RuleFor(r => r.RegionId, Guid.NewGuid().ToString());
            RuleFor(r => r.CurrencyType, f => (CurrencyType)f.Random.Int(1, 2));
            RuleFor(r => r.Type, f => (WalletType)f.Random.Int(1, 2));
            RuleFor(r => r.HolderId, r => null!);
        }

    }
}