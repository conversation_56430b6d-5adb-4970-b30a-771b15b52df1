syntax = "proto3";

package Cashflow.Report.V5;

import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Anis.Cashflow.Report.Grpc.Protos.Consumer";

service CashflowReportConsumerV5  {
  rpc Index (Request) returns (Response);
}

message Request{
	google.protobuf.StringValue location_id = 1;
	SubscriptionType type = 2;
	DebtType debt_type = 3;
	google.protobuf.StringValue phone_number = 4;
	google.protobuf.Int32Value current_page = 5;
	google.protobuf.Int32Value page_size = 6;
	bool is_ascending = 7;
	WalletTypeFilter wallet_type_filter = 8;
	int32 days_count = 9;
	google.protobuf.StringValue operator_id = 10;
	bool expiration_only = 11;
}

message Response { 
	int32 current_page = 1;
	int32 page_size = 2;
	int32 total = 3;
	repeated WalletDto wallet_dtos = 4;
}

message WalletDto
{
	string id = 1;
	string subscription_name = 2;
	string phone_number = 3;
	string account_location = 4;
	string wallet_region = 5;
	double urgent = 6;
	double delay = 7;
	double balance = 8;
	double average_sales = 9;
	double positive_balance_rate = 10;
	google.protobuf.Timestamp confirmed_at = 11;
	google.protobuf.Timestamp last_delay_refund_date = 12;
 	string operator_name = 13;
	double total_sales_last_day = 14;
	string account_state = 15;
	google.protobuf.StringValue remaining_time = 16;
}

enum SubscriptionType {
	ALL_OPTIONS = 0;
	OPERATOR = 1;
	BUSINESS = 2;
	NORMAL = 3;
}

enum DebtType {
	ALL = 0;
	NONE = 1;
	DEBIT_ONLY = 2;
	URGENT = 3;
	DELAYED = 4;
	HAS_DEBIT = 5;
}

enum WalletTypeFilter {
	ALL_WALLETS = 0;
	NORMAL_WALLETS = 1;
	PROFIT_WALLETS = 2;
}