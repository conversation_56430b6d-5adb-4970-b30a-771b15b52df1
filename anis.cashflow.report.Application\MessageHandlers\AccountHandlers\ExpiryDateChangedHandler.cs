﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.AccountHandlers
{
    public class ExpiryDateChangedHandler : IRequestHandler<MessageBody<ExpiryDateChangedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public ExpiryDateChangedHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> Handle(MessageBody<ExpiryDateChangedData> messageBody, CancellationToken cancellationToken)
        {

            var account = await _unitOfWork.Accounts.FindAsync(messageBody.AggregateId);

            if (account == null)
            {
                return false;
            }

            if (account.Sequence != messageBody.Sequence - 1)
                return account.Sequence >= messageBody.Sequence;

            account.Modify(messageBody);

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}