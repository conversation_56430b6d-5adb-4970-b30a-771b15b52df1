name: Deploy To Dev & Staging
on:
  push:
   branches: [ "development" ]
  # pull_request:
  #   branches: [ "master" ]
  workflow_dispatch:
env:
    SERVICE_NAME: anis-cashflow-report-v5
    SERVICE_NAMESPACE: anis-cashflow-report
permissions:
  actions: read
  contents: read
  id-token: write
    
jobs:
  build-service-image:
    environment: development
    runs-on: ubuntu-latest
    steps:
      - name: "Checkout Code"
        uses: actions/checkout@v3
      - name: "Looking For Check Docker & Deployment File"
        id: check_files
        uses: andstor/file-existence-action@v1
        with:
          files: "Dockerfile, deployment-development.yaml, deployment-staging.yaml"
          allow_failure: true
      - name: Azure login
        uses: azure/login@v1.4.3
        with:
          client-id: ${{ secrets.AZURE_USER_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
      - name: Build and push image to ACR
        run: az acr build --image ${{ env.SERVICE_NAME }}:${{ github.sha }} --registry anisdev -g container-registries -f ./Dockerfile ./
    
  deploy-to-dev:
    environment: development
    runs-on: ubuntu-latest
    needs:
      - build-service-image
    steps:
      - name: "Checkout Code"
        uses: actions/checkout@v3
      - uses: cschleiden/replace-tokens@v1
        with:
          files: '["deployment-development.yaml"]'
          tokenPrefix: ___
          tokenSuffix: ___
        env:
          SERVICE_NAME: ${{ env.SERVICE_NAME }}
          SERVICE_NAMESPACE: ${{ env.SERVICE_NAMESPACE }}
          CONTAINER_REGISTRY: anisdev
          CONNECTION_STRING: ${{ secrets.DEVELOPMENT_DATABASE_CONNECTION_STRING }}
          ACCOUNTS_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ANIS_DEV_SERVICE_BUS_CONNECTION_STRING }}
          REGIONS_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ANIS_DEV_SERVICE_BUS_CONNECTION_STRING }}
          WALLETS_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ANIS_DEV_SERVICE_BUS_CONNECTION_STRING }}
          HOLDERS_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ANIS_DEV_SERVICE_BUS_CONNECTION_STRING }}
          SUBSCRIPTION_WALLET_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ANIS_DEV_SERVICE_BUS_CONNECTION_STRING }}

          IMAGE_TAG: ${{ github.sha }}
      - name: Azure login
        uses: azure/login@v1.4.3
        with:
          client-id: ${{ secrets.AZURE_USER_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
      - uses: Azure/aks-set-context@v3
        with:
          cluster-name: anis-dev
          resource-group: ${{ secrets.CLUSTER_RESOURCE_GROUP }}
      - uses: Azure/k8s-deploy@v4
        name: Deploys application
        with:
          action: deploy
          images: anisdev.azurecr.io/${{ env.SERVICE_NAME }}:${{ github.sha }}
          manifests: |
            deployment-development.yaml
          namespace: ${{ env.SERVICE_NAMESPACE }}
            
  # deploy-to-staging:
  #   permissions:
  #     actions: read
  #     contents: read
  #     id-token: write
  #   runs-on: self-hosted
  #   needs:
  #     - deploy-to-dev
  #   steps:
  #     - name: "Checkout Code"
  #       uses: actions/checkout@v3
  #     - uses: cschleiden/replace-tokens@v1
  #       with:
  #         files: '["deployment-staging.yaml"]'
  #         tokenPrefix: ___
  #         tokenSuffix: ___
  #       env:
  #         SERVICE_NAME: ${{ env.SERVICE_NAME }}
  #         SERVICE_NAMESPACE: ${{ env.SERVICE_NAMESPACE }}
  #         CONTAINER_REGISTRY: ${{ secrets.AZURE_CONTAINER_REGISTRY }}
  #         CONNECTION_STRING: ${{ secrets.STAGING_DATABASE_CONNECTION_STRING }}
  #         ACCOUNTS_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ECOMLY_SERVICE_BUS_CONNECTION_STRING }}
  #         REGIONS_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ECOMLY_SERVICE_BUS_CONNECTION_STRING }}
  #         WALLETS_SERVICE_BUS_CONNECTION_STRING: ${{ secrets.ECOMLY_SERVICE_BUS_CONNECTION_STRING }}

  #         IMAGE_TAG: ${{ github.sha }}
  #     - name: Azure login
  #       uses: azure/login@v1.4.3
  #       with:
  #         creds: ${{ secrets.AZURE_CREDENTIALS }}
  #     - uses: Azure/aks-set-context@v3
  #       with:
  #         cluster-name: anis-staging
  #         resource-group: ${{ secrets.CLUSTER_RESOURCE_GROUP }}
  #     - uses: Azure/k8s-deploy@v4
  #       name: Deploys application
  #       with:
  #         action: deploy
  #         images: ${{ secrets.AZURE_CONTAINER_REGISTRY }}.azurecr.io/${{ env.SERVICE_NAME }}:${{ github.sha }}
  #         manifests: |
  #           deployment-staging.yaml
  #         namespace: ${{ env.SERVICE_NAMESPACE }}
