﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Anis.Cashflow.Report.Application.MessageHandlers.WalletHandlers
{
    public class WalletRebuildHandler : IRequestHandler<WalletInputModel, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<WalletCreatedHandler> _logger;

        public WalletRebuildHandler(IUnitOfWork unitOfWork, ILogger<WalletCreatedHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<bool> Handle(WalletInputModel request, CancellationToken cancellationToken)
        {
            var wallet = await _unitOfWork.Wallets.AnyAsync(request.WalletId);

            if (wallet) return true;

            var subscription = await _unitOfWork.Subscriptions.FindAsync(request.SubscriptionId);

            if (subscription is null)
            {
                _logger.LogWarning("subscription is null , subscriptionId {SubscriptionId}", request.SubscriptionId);

                return false;
            }

            var region = await _unitOfWork.Regions.FindAsync(request.RegionId);

            if (region is null)
            {
                _logger.LogWarning("region is null , regionId {RegionId}", request.RegionId);

                return false;
            }

            if (!string.IsNullOrWhiteSpace(request.HolderId))
            {

                var holderIsExist = await _unitOfWork.Holders.IsExistAsync(request.HolderId);

                if (!holderIsExist)
                {
                    _logger.LogWarning("holder not found for walletId : {AggregateId}  , holderId {HolderId}", request.WalletId, request.HolderId);

                    return false;
                }
            }

            var currentUrgent = 0.0m;
            var currentDelayed = 0.0m;

            currentUrgent += request.DetailedSummations.Where(d => d.Type == TransactionType.UrgentTopUp).Select(d => d.TotalDebit - d.TotalCredit).FirstOrDefault();

            currentUrgent += request.DetailedSummations.Where(d => d.Type == TransactionType.RefundUrgentTopUp).Select(d => d.TotalDebit - d.TotalCredit).FirstOrDefault();

            currentDelayed += request.DetailedSummations.Where(d => d.Type == TransactionType.DelayedTopUp).Select(d => d.TotalDebit - d.TotalCredit).FirstOrDefault();

            currentDelayed += request.DetailedSummations.Where(d => d.Type == TransactionType.RefundDelayedTopUp).Select(d => d.TotalDebit - d.TotalCredit).FirstOrDefault();

            var newWallet = new Wallet(request, currentUrgent, currentDelayed);

            await _unitOfWork.Wallets.AddAsync(newWallet);

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}