﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.AccountHandlers
{
    public class AccountSequenceIncrementedTest : TestBase
    {
        public AccountSequenceIncrementedTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task Update_AccountUnnecessaryEventWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<SequenceIncrementedData>()
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.AccountDeleted,
                Data = new(),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(messageBody, dbAccount, createAccount);
        }

        [Fact]
        public async Task Update_AccountUnnecessaryEventWithAccountNotExists_ReturnNotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var messageBody = new MessageBody<SequenceIncrementedData>()
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.AccountDeleted,
                Data = new(),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleOrDefaultAsync();

            Assert.False(isHandled);

            Assert.Null(dbAccount);
        }

        [Fact]
        public async Task Update_AccountUnnecessaryEventWithOldSequence_ReturnAlreadyHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .RuleFor(f => f.Sequence, 7)
                .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<SequenceIncrementedData>()
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.AccountPriceDifferenceChanged,
                Data = new(),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccount);
        }

        [Fact]
        public async Task Update_AccountUnnecessaryWithFarNewerSequence_ReturnNotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<SequenceIncrementedData>()
            {
                AggregateId = createAccount.Id,
                Sequence = 6,
                Type = EventType.AccountDeleted,
                Data = new(),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccount = await context.Accounts.SingleAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccount);
        }
    }
}