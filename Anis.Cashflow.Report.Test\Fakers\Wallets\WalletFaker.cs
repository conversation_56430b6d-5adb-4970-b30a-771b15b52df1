﻿using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.Enums;

namespace Anis.Cashflow.Report.Test.Fakers.Wallets
{
    public sealed class WalletFaker : PrivateFaker<Wallet>
    {
        public WalletFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.Id, Guid.NewGuid().ToString());
            RuleFor(r => r.Sequence, 1);
            RuleFor(r => r.Balance, f => decimal.Round(f.Random.Decimal(), 3));
            RuleFor(r => r.CurrentUrgent, f => decimal.Round(f.Random.Decimal(), 3));
            RuleFor(r => r.CurrentDelayed, f => decimal.Round(f.Random.Decimal(), 3));
            RuleFor(r => r.NetBalance, f => decimal.Round(f.Random.Decimal(), 3));
            RuleFor(r => r.CurrencyType, f => (CurrencyType)f.Random.Int(1, 2));
            RuleFor(r => r.Type, f => (WalletType)f.Random.Int(1, 2));
        }
    }
}