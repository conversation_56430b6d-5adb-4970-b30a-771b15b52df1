﻿using Anis.Cashflow.Report.Application.Contracts.Services.GrpcService;
using Anis.Cashflow.Report.Application.Features.Management.GetHolders;
using Anis.Cashflow.Report.Application.Models;
using Anis.Cashflow.Report.Grpc.Extensions;
using Anis.Cashflow.Report.Grpc.Protos.Managmant;
using Anis.Cashflow.Report.Infra.Helpers;
using Google.Protobuf.WellKnownTypes;
using Grpc.Core;
using MediatR;

namespace Anis.Cashflow.Report.Grpc.Services
{
    public class QueryManagmentService : CashflowReportManagmentV5.CashflowReportManagmentV5Base
    {
        private readonly IMediator _mediator;
        private readonly ConfigModel _config;
        private readonly IGrpcClientService _grpcClient;

        public QueryManagmentService(IMediator mediator, ConfigModel config, IGrpcClientService grpcClient)
        {
            _mediator = mediator;
            _config = config;
            _grpcClient = grpcClient;
        }

        public override async Task<Response> Index(Request request, ServerCallContext context)
        {
            var days = request.DaysCount ?? _config.DaysCount;

            var query = request.ToQuery();

            var response = await _mediator.Send(query);

            var walletIds = response.WalletDtos.Select(w => w.Id).ToList();

            var locationSaleAverage = await _grpcClient.GetAverageSaleLocationAsync(request.LocationId, days);

            var walletSaleAverage = await _grpcClient.GetAverageSaleWalletAsync(walletIds, days);

            var resultResponse = new Response
            {
                PageSize = response.PageSize,
                CurrentPage = response.CurrentPage,
                Total = response.Total,
                TotalNetBalance = (double)response.TotalNetBalance,
                AverageSalesLocation = (double)locationSaleAverage,
                AverageSalesLocationRate = GetAverageSalesLocationRate(locationSaleAverage, response.TotalNetBalance)
            };

            resultResponse.WalletDtos.ToOutputs(response.WalletDtos, walletSaleAverage);

            return resultResponse;
        }

        private static double GetAverageSalesLocationRate(decimal locationSaleAverage, decimal totalNetBalance)
        {
            return (double)(locationSaleAverage == 0 ? 0 : (totalNetBalance / locationSaleAverage).Truncate());
        }

        public override async Task<GetHoldersResponse> GetHolders(Empty request, ServerCallContext context)
        {
            var query = new GetHoldersQuery();

            var response = await _mediator.Send(query);

            return response.ToOutput();
        }

        public override async Task<CashflowFilterResponse> CashflowFilter(CashflowFilterRequest request, ServerCallContext context)
        {
            var days = request.DaysCount ?? _config.DaysCount;

            var query = request.ToQuery();

            var response = await _mediator.Send(query);

            var walletIds = response.WalletDtos.Select(w => w.Id).ToList();

            var locationSaleAverage = await _grpcClient.GetAverageSaleLocationAsync(request.LocationId, days);

            var walletSaleAverage = await _grpcClient.GetAverageSaleWalletAsync(walletIds, days);

            var resultResponse = new CashflowFilterResponse
            {
                PageSize = response.PageSize,
                CurrentPage = response.CurrentPage,
                Total = response.Total,
                TotalNetBalance = (double)response.TotalNetBalance,
                AverageSalesLocation = (double)locationSaleAverage,
                AverageSalesLocationRate = GetAverageSalesLocationRate(locationSaleAverage, response.TotalNetBalance)
            };

            resultResponse.WalletDtos.ToOutputs(response.WalletDtos, walletSaleAverage);

            return resultResponse;
        }
    }
}
