﻿using Anis.Cashflow.Report.Domain.Entities;

namespace Anis.Cashflow.Report.Test.Fakers.Holders
{
    public class HolderFaker : PrivateFaker<Holder>
    {
        public HolderFaker()
        {
            UsePrivateConstructor();

            RuleFor(h => h.Id, Guid.NewGuid().ToString());

            RuleFor(h => h.ArabicName, h => h.Random.Word());

            RuleFor(h => h.EnglishName, h => h.Random.Word());

            RuleFor(h => h.Sequence, 1);
        }
    }
}
