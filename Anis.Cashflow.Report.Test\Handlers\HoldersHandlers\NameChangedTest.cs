﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Holders.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Holders;
using Anis.Cashflow.Report.Test.Fakers.Holders.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.HoldersHandlers
{
    public class NameChangedTest : TestBase
    {
        public NameChangedTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task NameChanged_WhenHolderIsNotExist_ReturnFalse()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var nameChangedDataFaker = new NameChangedDataFaker().Generate();

            var message = new MessageBody<NameChangedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.NameChanged,
                Data = nameChangedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(message);

            var dbHolders = await context.Holders.ToListAsync();

            //assert
            Assert.False(isHandled);

            Assert.Empty(dbHolders);
        }

        [Fact]
        public async Task NameChanged_WhenReceivedValidData_ReturnTurnAndHandleMessage()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderFaker = new HolderFaker().Generate();

            await context.Holders.AddAsync(holderFaker);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var nameChangedDataFaker = new NameChangedDataFaker().Generate();

            var message = new MessageBody<NameChangedData>
            {
                AggregateId = holderFaker.Id,
                Sequence = 2,
                Type = EventType.NameChanged,
                Data = nameChangedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(message);

            var dbHolders = await context.Holders.ToListAsync();

            //assert
            Assert.True(isHandled);

            HolderAssert.AssertEquality(message, Assert.Single(dbHolders));
        }

        [Theory]
        [InlineData(2)]
        [InlineData(3)]
        public async Task NameChanged_WhenReceivedSequenceIsLessOrEqualExistHandler_ReturnTurnAndIgnoreMessage(long sequence)
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderFaker = new HolderFaker()
                .RuleFor(h => h.Sequence, 3)
                .Generate();

            await context.Holders.AddAsync(holderFaker);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var nameChangedDataFaker = new NameChangedDataFaker().Generate();

            var message = new MessageBody<NameChangedData>
            {
                AggregateId = holderFaker.Id,
                Sequence = sequence,
                Type = EventType.NameChanged,
                Data = nameChangedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(message);

            var dbHolders = await context.Holders.ToListAsync();

            //assert
            Assert.True(isHandled);

            HolderAssert.AssertEquality(holderFaker, Assert.Single(dbHolders));
        }

        [Fact]
        public async Task NameChanged_WhenReceivedSequenceIsGreaterThenExpected_ReturnFalse()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var holderFaker = new HolderFaker().Generate();

            await context.Holders.AddAsync(holderFaker);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var nameChangedDataFaker = new NameChangedDataFaker().Generate();

            var message = new MessageBody<NameChangedData>
            {
                AggregateId = holderFaker.Id,
                Sequence = 3,
                Type = EventType.NameChanged,
                Data = nameChangedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 1,
            };

            var isHandled = await mediator.Send(message);

            var dbHolders = await context.Holders.ToListAsync();

            //assert
            Assert.False(isHandled);

            HolderAssert.AssertEquality(holderFaker, Assert.Single(dbHolders));
        }
    }
}
