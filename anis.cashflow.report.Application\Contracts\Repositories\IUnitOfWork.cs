﻿namespace Anis.Cashflow.Report.Application.Contracts.Repositories
{
    public interface IUnitOfWork : IDisposable
    {
        IAccountRepository Accounts { get; }
        ISubscriptionRepository Subscriptions { get; }
        IWalletRepository Wallets { get; }
        IRegionRepository Regions { get; }
        IHolderRepository Holders { get; }
        ISubscriptionWalletRepository SubscriptionWallets { get; }

        Task SaveChangesAsync();
    }
}
