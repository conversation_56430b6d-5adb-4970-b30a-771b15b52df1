﻿using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Infra.Persistence.Configurations;
using Microsoft.EntityFrameworkCore;

namespace Anis.Cashflow.Report.Infra.Persistence
{
    public class AppDbContext : DbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options) { }

        public DbSet<Account> Accounts { get; set; }
        public DbSet<Subscription> Subscriptions { get; set; }
        public DbSet<Wallet> Wallets { get; set; }
        public DbSet<Region> Regions { get; set; }
        public DbSet<Holder> Holders { get; set; }
        public DbSet<SubscriptionWallet> SubscriptionWallets { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new AccountConfig());

            modelBuilder.ApplyConfiguration(new SubscriptionConfig());

            modelBuilder.ApplyConfiguration(new WalletConfig());

            modelBuilder.ApplyConfiguration(new RegionConfig());

            modelBuilder.ApplyConfiguration(new HolderConfig());

            modelBuilder.ApplyConfiguration(new SubscriptionWalletConfig());

            base.OnModelCreating(modelBuilder);
        }
    }
}
