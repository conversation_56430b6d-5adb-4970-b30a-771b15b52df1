﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Live.Asserts;
using Ecom.Cards.Grpc.Demo.V2.Regions;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.RegionEvents
{
    public class RegionCodeChangedTest : TestBase
    {
        public RegionCodeChangedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task CodeChange_RegionCodeChanged_ReturnValid()
        {
            var aggregateId = Guid.NewGuid().ToString();

            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var channel = GrpcChannel.ForAddress(Address.Base);

            var grpcClient = new RegionsDemoEvents.RegionsDemoEventsClient(channel);

            var createRegion = new RegionFaker()
                                          .RuleFor(f => f.Id, aggregateId)
                                          .RuleFor(f => f.Sequence, 1)
                                          .RuleFor(f => f.Code, "code test")
                                          .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var request = new ChangeCodeRequest()
            {
                AggregateId = aggregateId,
                Code = "code test changed",
                Sequence = 2
            };

            await grpcClient.ChangeCodeAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<RegionEventsListener>().CloseProccessorAsync();

            var region = await context.Regions.SingleOrDefaultAsync();

            Assert.NotNull(region);

            DemoEventAsserts.AssertEquality(request, region, 2);
        }
    }
}
