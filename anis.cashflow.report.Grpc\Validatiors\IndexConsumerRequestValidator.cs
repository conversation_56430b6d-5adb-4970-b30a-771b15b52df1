﻿using Anis.Cashflow.Report.Grpc.Protos.Consumer;
using Anis.Cashflow.Report.Grpc.Resources;
using FluentValidation;

namespace Anis.Cashflow.Report.Grpc.Validatiors
{
    public class IndexConsumerRequestValidator : AbstractValidator<Request>
    {
        public IndexConsumerRequestValidator()
        {
            RuleFor(r => r).Must(r => r.DaysCount >= 0)
                           .WithName(Titles.DaysCount)
                           .WithMessage(Phrases.DaysCountMustBeEqualZeroOrGreater);

            //RuleFor(r => r).Must(r => ValidateWalletId(r.WalletId))
            //                                  .WithName(Titles.WalletId)
            //                                  .WithMessage(Phrases.InvalidWalledId);
        }

        private bool ValidateWalletId(string walletId)
        {
            if (string.IsNullOrWhiteSpace(walletId))
                return true;

            return Guid.TryParse(walletId, out var _) && walletId != Guid.Empty.ToString();
        }
    }
}
