﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Const\**" />
    <EmbeddedResource Remove="Const\**" />
    <None Remove="Const\**" />
  </ItemGroup>
	
	<ItemGroup>
		<PackageReference Include="MediatR" Version="11.0.0" />
		<PackageReference Include="MediatR.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
	</ItemGroup>
	
	<ItemGroup>
	  <Compile Update="Resources\Phrases.Designer.cs">
	    <DesignTime>True</DesignTime>
	    <AutoGen>True</AutoGen>
	    <DependentUpon>Phrases.resx</DependentUpon>
	  </Compile>
	</ItemGroup>
	
	<ItemGroup>
	  <EmbeddedResource Update="Resources\Phrases.resx">
	    <Generator>ResXFileCodeGenerator</Generator>
	    <LastGenOutput>Phrases.Designer.cs</LastGenOutput>
	  </EmbeddedResource>
	</ItemGroup>

</Project>
