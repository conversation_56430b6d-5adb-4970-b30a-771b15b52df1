﻿using Anis.Cashflow.Report.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Anis.Cashflow.Report.Infra.Persistence.Configurations
{
    public class SubscriptionWalletConfig : IEntityTypeConfiguration<SubscriptionWallet>
    {
        public void Configure(EntityTypeBuilder<SubscriptionWallet> builder)
        {
            builder.HasOne(i => i.Subscription)
                .WithMany(i => i.SubscriptionWallets)
                .HasForeignKey(i => i.SubscriptionId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.HasOne(i => i.Wallet)
                .WithMany(i => i.SubscriptionWallets)
                .HasForeignKey(i => i.WalletId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Property(i => i.LinkDateTime)
                .HasColumnType(Config.DateTime)
                .IsRequired(false);
        }
    }
}