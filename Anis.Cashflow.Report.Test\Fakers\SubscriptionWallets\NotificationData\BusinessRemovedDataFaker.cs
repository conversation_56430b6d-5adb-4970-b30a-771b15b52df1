﻿using Anis.Cashflow.Report.Domain.EventsData.SubscriptionWallets;

namespace Anis.Cashflow.Report.Test.Fakers.SubscriptionWallets.NotificationData
{
    public sealed class BusinessRemovedDataFaker : PrivateFaker<BusinessRemovedData>
    {
        public BusinessRemovedDataFaker()
        {
            UsePrivateConstructor();
            RuleFor(r => r.BusinessWalletId, f => f.Random.String());
            RuleFor(r => r.BusinessSubscriptionId, f => f.Random.String());
            RuleFor(r => r.OperatorWalletId, f => f.Random.String());
        }
    }
}
