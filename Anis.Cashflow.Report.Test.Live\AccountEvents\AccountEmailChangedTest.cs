﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Live.Asserts;
using Anis.Cashflow.Report.Test.Live.Protos;
using Google.Protobuf.WellKnownTypes;
using Grpc.Net.Client;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.AccountEvents
{
    public class AccountEmailChangedTest : TestBase
    {
        public AccountEmailChangedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task ChangeEmailAccount_ChangeEmailValidAccoun_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                        .RuleFor(r => r.LocationId, createRegion.Id)
                        .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var channel = GrpcChannel.ForAddress(Address.EcomCards);

            var grpcClient = new AccountsDemoEvents.AccountsDemoEventsClient(channel);

            var request = new ChangeAccountEmailRequest
            {
                AggregateId = createAccount.Id,
                DateTime = DateTime.UtcNow.ToTimestamp(),
                Email = "<EMAIL>",
                Sequence = 2
            };

            await grpcClient.ChangeAccountEmailAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<AccountEventsListener>().CloseProccessorAsync();

            var dbAccount = await context.Accounts.SingleOrDefaultAsync();

            DemoEventAsserts.AssertEquality(request, dbAccount);

        }
    }
}
