﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;

namespace Anis.Cashflow.Report.Infra.Persistence.Repositories
{
    internal class UnitOfWork : IUnitOfWork
    {
        private readonly AppDbContext _appDbContext;

        public UnitOfWork(AppDbContext appDbContext)
        {
            _appDbContext = appDbContext;
        }

        private IAccountRepository _accounts;
        public IAccountRepository Accounts
        {
            get
            {
                if (_accounts != null)
                    return _accounts;

                return _accounts = new AccountRepository(_appDbContext);
            }
        }

        private ISubscriptionRepository _subscriptions;
        public ISubscriptionRepository Subscriptions
        {
            get
            {
                if (_subscriptions != null)
                    return _subscriptions;

                return _subscriptions = new SubscriptionRepository(_appDbContext);
            }
        }

        private IWalletRepository _wallets;
        public IWalletRepository Wallets
        {
            get
            {
                if (_wallets != null)
                    return _wallets;

                return _wallets = new WalletRepository(_appDbContext);
            }
        }

        private IRegionRepository _regions;
        public IRegionRepository Regions
        {
            get
            {
                if (_regions != null)
                    return _regions;

                return _regions = new RegionRepository(_appDbContext);
            }
        }

        private IHolderRepository _holders;

        public IHolderRepository Holders
        {
            get
            {
                if (_holders != null)
                    return _holders;

                return _holders = new HolderRepository(_appDbContext);
            }
        }

        private ISubscriptionWalletRepository _SubscriptionWallets;
        public ISubscriptionWalletRepository SubscriptionWallets
        {
            get
            {
                if (_SubscriptionWallets != null)
                    return _SubscriptionWallets;
                return _SubscriptionWallets = new SubscriptionWalletRepository(_appDbContext);
            }
        }

        public async Task SaveChangesAsync()
        {
            await _appDbContext.SaveChangesAsync();
        }

        public void Dispose()
        {
            _appDbContext.Dispose();
        }
    }
}
