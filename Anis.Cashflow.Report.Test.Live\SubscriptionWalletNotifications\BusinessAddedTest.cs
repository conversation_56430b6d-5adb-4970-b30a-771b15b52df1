﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Fakers.OperatorBusinessLinks.NotificationData;
using Anis.Cashflow.Report.Test.Live.Asserts;
using Anis.Cashflow.Report.Test.Live.OperatorBusinessLinkDemo;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.OperatorBusinessLinkNotifications
{
    public class BusinessAddedTest : TestBase
    {
        public BusinessAddedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task BusinessAdded_WhenReceivedValidData_ReturnTrueAndHandleNotification()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var channel = GrpcChannel.ForAddress(Address.OperatorBusinessLink);

            var grpcClient = new OperatorToBusinessLinkDemoNotifications.OperatorToBusinessLinkDemoNotificationsClient(channel);

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createOperatorAccount = new AccountFaker()
                .RuleFor(f => f.Location, createRegion)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createAccount)
                .Generate();

            var createOperatorSubscription = new SubscriptionFaker()
                .RuleFor(f => f.Account, createOperatorAccount)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createSubscription)
                .Generate();

            var createOperatorWallet = new WalletFaker()
                .RuleFor(f => f.Region, createRegion)
                .RuleFor(f => f.Subscription, createOperatorSubscription)
                .Generate();

            await context.Wallets.AddRangeAsync(creatWallet, createOperatorWallet);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var businessAddedData = new BusinessAddedDataFaker()
                .RuleFor(f => f.BusinessWalletId, creatWallet.Id)
                .Generate();

            var request = new NotifyRequest
            {
                BusinessSubscriptionId = createSubscription.Id,
                BusinessWalletId = creatWallet.Id,
                NotificationType = NotificationType.BusinessAdded,
                OperatorWalletId = createOperatorWallet.Id,
                OperatorSubscriptionId = createOperatorSubscription.Id,
                StateVersion = 1,
            };

            await grpcClient.NotifyAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<SubscriptionWalletListener>().CloseProccessorAsync();

            var dbSubscriptionWallet = await context.SubscriptionWallets.SingleOrDefaultAsync();

            DemoEventAsserts.AssertEquality(request, dbSubscriptionWallet);
        }
    }
}
