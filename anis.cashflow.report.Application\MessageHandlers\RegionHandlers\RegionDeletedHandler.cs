﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.MessageHandlers.RegionHandlers
{
    public class RegionDeletedHandler : IRequestHandler<MessageBody<RegionDeletedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;

        public RegionDeletedHandler(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<bool> Handle(MessageBody<RegionDeletedData> request, CancellationToken cancellationToken)
        {
            var region = await _unitOfWork.Regions.FindAsync(request.AggregateId);

            if(region == null) return false;

            if (region.Sequence != request.Sequence - 1)
                return region.Sequence >= request.Sequence;

            region.IncrementSequence();

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}