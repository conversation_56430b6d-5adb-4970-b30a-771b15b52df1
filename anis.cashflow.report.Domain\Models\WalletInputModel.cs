﻿using Anis.Cashflow.Report.Domain.Enums;
using MediatR;

namespace Anis.Cashflow.Report.Domain.Models
{
    public class WalletInputModel : IRequest<bool>
    {
        public string WalletId { get; set; }
        public int Sequence { get; set; }
        public int SubscriptionType { get; set; }
        public decimal Balance { get; set; }
        public CurrencyType CurrencyType { get; set; }
        public string SubscriptionId { get; set; }
        public string RegionId { get; set; }
        public WalletType Type { get; set; }
        public string HolderId { get; set; }
        public IEnumerable<DetailedSummationModel> DetailedSummations { get; set; } = new List<DetailedSummationModel>();
    }

    public class DetailedSummationModel
    {
        public string Source { get; set; }
        public string Type { get; set; }
        public decimal TotalDebit { get; set; }
        public decimal TotalCredit { get; set; }

    }
}
