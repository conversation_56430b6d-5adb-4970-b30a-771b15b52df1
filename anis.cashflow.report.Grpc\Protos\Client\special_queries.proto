syntax = "proto3";

package anis.gateway.v1;

import "google/protobuf/wrappers.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Anis.Cashflow.Report.Grpc.Protos.Client.SpecialQueries";

service SpecialQueries {
  rpc GetLatestCashDeposits (GetLatestCashDepositsRequest) returns (stream GetLatestCashDepositsResponse);
  rpc GetWalletsSnapshot (GetWalletsSnapshotRequest) returns (stream GetWalletsSnapshotResponse);
  rpc GetAnisPayWalletIds (GetAnisPayWalletIdsRequest) returns (GetAnisPayWalletIdsResponse);
  rpc GetAccountSnapshot (AccountSnapshotRequest) returns (stream AccountSnapshotResponse);
}

message GetLatestCashDepositsRequest {
	google.protobuf.Int32Value result_number = 1;
}

message GetLatestCashDepositsResponse {
	int32 result_number = 1;
	google.protobuf.StringValue wallet_id = 2;
	string region_id = 3;
	string account_id = 4;
	int32 account_sequence = 5;
	repeated CashDepositResult results = 6;
}

message CashDepositResult {
	string id = 1;
	double value = 2;
	google.protobuf.Timestamp date_time = 3;
}

message GetWalletsSnapshotRequest {
	google.protobuf.Int32Value result_number = 1;
	google.protobuf.Timestamp date_time = 2;
	bool include_detailed_summation = 3;
}

message GetWalletsSnapshotResponse {
	int32 result_number = 1;
	string wallet_id = 2;
	int64 sequence = 3;
	string region_id = 4;
	string subscription_id = 5;
	int32 subscription_type = 6;
	int32 currency_type = 7;
	string type = 8;
	google.protobuf.StringValue holder_id = 9;
	double balance = 10;
	bool allowed_debt_enabled = 11;
	repeated DetailedSummation summations = 12;
}

message DetailedSummation {
	string source = 1;
	string type = 2;
	double total_debit = 3;
	double total_credit = 4;
}

message GetAnisPayWalletIdsRequest {
	string sender_wallet_id = 1;
	string recipient_wallet_id = 2;
}

message GetAnisPayWalletIdsResponse {
	string anis_sender_region_wallet_id = 1;
	string anis_sender_recipient_region_wallet_id = 2;
	string anis_recipient_region_wallet_id = 3;
	string commission_discount_wallet_id = 4;
}

message AccountSnapshotRequest {
	google.protobuf.Int32Value result_number = 1;
}

message AccountSnapshotResponse {
	string account_id = 1;
	string phone_number = 2;
	string location_id = 3;
	string number = 4;
	google.protobuf.StringValue email = 5;
	string location_arabic_name = 6;
	string location_english_name = 7;
	string location_code = 8;
	google.protobuf.Timestamp confirmedAt =9;
}