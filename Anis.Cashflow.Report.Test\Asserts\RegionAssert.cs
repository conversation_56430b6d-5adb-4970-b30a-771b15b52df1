﻿using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.Models;

namespace Anis.Cashflow.Report.Test.Asserts
{
    public static class RegionAssert
    {
        public static void AssertEquality(
            MessageBody<RegionCreatedData> message,
            Region dbRegion,
            long sequence = 1)
        {
            Assert.Equal(message.AggregateId, dbRegion.Id);
            Assert.Equal(message.Data.ArabicName, dbRegion.ArabicName);
            Assert.Equal(message.Data.EnglishName, dbRegion.EnglishName);
            Assert.Equal(message.Data.Code, dbRegion.Code);
            Assert.Equal(sequence, dbRegion.Sequence);
        }

        public static void AssertEquality(
            MessageBody<RegionDeletedData> message,
            Region dbRegion,
            Region createdRegion,
            long sequence = 1)
        {
            Assert.Equal(message.AggregateId, dbRegion.Id);
            Assert.Equal(createdRegion.ArabicName, dbRegion.ArabicName);
            Assert.Equal(createdRegion.EnglishName, dbRegion.EnglishName);
            Assert.Equal(createdRegion.Code, dbRegion.Code);
            Assert.Equal(sequence, dbRegion.Sequence);
        }

        public static void AssertEquality(
            MessageBody<RegionUpdatedData> message,
            Region dbRegion,
            Region createdRegion,
            long sequence = 1)
        {
            Assert.Equal(message.AggregateId, dbRegion.Id);
            Assert.Equal(message.Data.ArabicName, dbRegion.ArabicName);
            Assert.Equal(message.Data.EnglishName, dbRegion.EnglishName);
            Assert.Equal(createdRegion.Code, dbRegion.Code);
            Assert.Equal(sequence, dbRegion.Sequence);
        }

        public static void AssertEquality(
            Region region,
            Region dbRegion,
            long sequence = 1)
        {
            Assert.Equal(region.ArabicName, dbRegion.ArabicName);
            Assert.Equal(region.EnglishName, dbRegion.EnglishName);
            Assert.Equal(region.Code, dbRegion.Code);
            Assert.Equal(sequence, dbRegion.Sequence);
        }

        public static void AssertEquality(
            MessageBody<RegionUpdatedData> message,
            Region dbRegion,
            long sequence = 1)
        {
            Assert.Equal(message.AggregateId, dbRegion.Id);
            Assert.Equal(message.Data.ArabicName, dbRegion.ArabicName);
            Assert.Equal(message.Data.EnglishName, dbRegion.EnglishName);
            Assert.Equal(sequence, dbRegion.Sequence);
        }

        public static void AssertEquality(
            MessageBody<RegionCodeChangedData> message,
            Region dbRegion,
            Region createdRegion,
            long sequence = 1)
        {
            Assert.Equal(message.AggregateId, dbRegion.Id);
            Assert.Equal(message.Data.Code, dbRegion.Code);
            Assert.Equal(sequence, dbRegion.Sequence);
            Assert.Equal(createdRegion.ArabicName, dbRegion.ArabicName);
            Assert.Equal(createdRegion.EnglishName, dbRegion.EnglishName);
        }
    }
}
