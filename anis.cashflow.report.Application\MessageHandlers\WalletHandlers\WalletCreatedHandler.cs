﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Domain.Entities;
using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using MediatR;
using Microsoft.Extensions.Logging;

namespace Anis.Cashflow.Report.Application.MessageHandlers.WalletHandlers
{
    public class WalletCreatedHandler : IRequestHandler<MessageBody<WalletCreatedData>, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<WalletCreatedHandler> _logger;

        public WalletCreatedHandler(IUnitOfWork unitOfWork, ILogger<WalletCreatedHandler> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<bool> Handle(MessageBody<WalletCreatedData> request, CancellationToken cancellationToken)
        {
            var walletIsExist = await _unitOfWork.Wallets.AnyAsync(request.AggregateId);

            if (walletIsExist) return true;

            var subscription = await _unitOfWork.Subscriptions.FindAsync(request.Data.SubscriptionId);

            if (subscription is null)
            {
                _logger.LogWarning("subscription is null  walletId : {AggregateId}  , subscriptionId {SubscriptionId}", request.AggregateId, request.Data.SubscriptionId);

                return false;
            }

            var region = await _unitOfWork.Regions.FindAsync(request.Data.RegionId);

            if (region is null)
            {
                _logger.LogWarning("region is null  walletId : {AggregateId}  , regionId {RegionId}", request.AggregateId, request.Data.RegionId);

                return false;
            }

            if (!string.IsNullOrWhiteSpace(request.Data.HolderId))
            {

                var holderIsExist = await _unitOfWork.Holders.IsExistAsync(request.Data.HolderId);

                if (!holderIsExist)
                {
                    _logger.LogWarning("holder not found for walletId : {AggregateId}  , holderId {HolderId}", request.AggregateId, request.Data.HolderId);

                    return false;
                }
            }

            var wallet = new Wallet(request);

            await _unitOfWork.Wallets.AddAsync(wallet);

            await _unitOfWork.SaveChangesAsync();

            return true;
        }
    }
}
