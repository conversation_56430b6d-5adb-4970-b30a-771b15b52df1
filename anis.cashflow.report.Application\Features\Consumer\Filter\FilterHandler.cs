﻿using Anis.Cashflow.Report.Application.Contracts.Repositories;
using Anis.Cashflow.Report.Application.Contracts.Services.GrpcService;
using Anis.Cashflow.Report.Application.Models;
using MediatR;

namespace Anis.Cashflow.Report.Application.Features.Consumer.Filter
{
    public class FilterHandler : IRequestHandler<FilterQuery, FilterResponse>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly BlackListSubscriptionsModel _blackListSubscriptionsModel;
        private readonly IGrpcClientService _grpcClientService;

        public FilterHandler(IUnitOfWork unitOfWork,
                             BlackListSubscriptionsModel blackListSubscriptionsModel,
                             IGrpcClientService grpcClientService)
        {
            _unitOfWork = unitOfWork;
            _blackListSubscriptionsModel = blackListSubscriptionsModel;
            _grpcClientService = grpcClientService;
        }

        public async Task<FilterResponse> Handle(FilterQuery request, CancellationToken cancellationToken)
        {
            return await _unitOfWork.SubscriptionWallets.FilterConsumerAsync(request: request,
                                                                 model: _blackListSubscriptionsModel,
                                                                 cancellationToken: cancellationToken);
        }
    }
}
