﻿using Anis.Cashflow.Report.Application.Models;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace Anis.Cashflow.Report.Application
{
    public static class ApplicationContainer
    {
        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            services.AddSingleton<BlackListSubscriptionsModel>();

            services.AddMediatR(Assembly.GetExecutingAssembly());

            return services;
        }
    }
}
