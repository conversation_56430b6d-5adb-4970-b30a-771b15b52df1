﻿using Anis.Cashflow.Report.Domain.EventsData.Holders.DataTypes;
using Anis.Cashflow.Report.Domain.Models;

namespace Anis.Cashflow.Report.Domain.Entities
{
    public class Holder
    {
        private Holder() { }

        public Holder(MessageBody<HolderCreatedData> message)
        {
            Id = message.AggregateId;
            Sequence = 1;
            ArabicName = message.Data.ArabicName;
            EnglishName = message.Data.EnglishName;
        }

        public string Id { get; private set; }
        public string ArabicName { get; private set; }
        public string EnglishName { get; private set; }
        public long Sequence { get; private set; }

        private HashSet<Wallet> _wallets = new();
        public IReadOnlyCollection<Wallet> Wallets => _wallets;

        public void Update(MessageBody<NameChangedData> message)
        {
            ArabicName = message.Data.ArabicName;
            EnglishName = message.Data.EnglishName;
            Sequence = message.Sequence;
        }

        public void UpdateSequence(MessageBody<HolderSequenceIncrementedData> message)
        {
            Sequence = message.Sequence;
        }
    }
}