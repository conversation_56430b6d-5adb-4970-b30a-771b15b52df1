﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Contracts\Services\ServiceBus\**" />
    <EmbeddedResource Remove="Contracts\Services\ServiceBus\**" />
    <None Remove="Contracts\Services\ServiceBus\**" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\anis.cashflow.report.Domain\Anis.Cashflow.Report.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
  </ItemGroup>

</Project>
