﻿using Anis.Cashflow.Report.Domain.EventsData.Holders.DataTypes;

namespace Anis.Cashflow.Report.Test.Fakers.Holders.EventsData
{
    public class NameChangedDataFaker : PrivateFaker<NameChangedData>
    {
        public NameChangedDataFaker()
        {
            UsePrivateConstructor();

            RuleFor(h => h.ArabicName, h => h.Random.Word());

            RuleFor(h => h.EnglishName, h => h.Random.Word());
        }
    }
}
