﻿syntax = "proto3";

package Cashflow.Report.V5;

import "google/protobuf/wrappers.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Anis.Cashflow.Report.Grpc.Protos.Managmant";

service CashflowReportManagmentV5 {
  rpc Index (Request) returns (Response);
  rpc CashflowFilter (CashflowFilterRequest) returns (CashflowFilterResponse);
  rpc GetHolders (google.protobuf.Empty) returns (GetHoldersResponse);
}

message Request{ 
	google.protobuf.StringValue location_id = 1;
	SubscriptionType type = 2;
	Currency currency = 3;
	google.protobuf.StringValue phone_number = 4;
	bool debt_only = 5;
	google.protobuf.Int32Value current_page = 6;
	google.protobuf.Int32Value page_size = 7;
	google.protobuf.Int32Value days_count = 8;
	WalletTypeFilter wallet_type_filter = 9;
	CashAndHoldersFilter cash_and_holders_filter = 10;
	google.protobuf.StringValue holder_id = 11;
	google.protobuf.StringValue operator_id = 12;
	bool show_stores_only = 13;
}

message Response { 
	int32 current_page = 1;
	int32 page_size = 2;
	int32 total = 3;
	double total_net_balance = 4;
	double average_sales_location = 5;
	repeated WalletDto wallet_dtos = 6;
	double average_sales_location_rate = 7;
}

message WalletDto
{
	string id = 1;
	string subscription_name = 2;
	double credit = 3;
	double debit = 4;
	double net_balance = 5;
	double urgent = 6;
	double delay = 7;
	string phone_number = 8;
	string state = 9;
	string location = 10;
	string owner = 11;
	google.protobuf.StringValue email = 12;
	string wallet_identifier = 13;
	string wallet_region = 14;
	bool is_normal = 15;
	double average_sales = 16;
	double positve_balance_rate = 17;
	string code = 18;
	google.protobuf.StringValue holder_name = 19;
	google.protobuf.Timestamp confirmed_at = 20;
	google.protobuf.Timestamp last_delay_refund_date = 21;	
	string arabic_location_name = 22;
	string english_location_name = 23;
	string operator_name = 24;
	double total_sales_last_day = 25;
}

message CashflowFilterRequest{
	google.protobuf.StringValue location_id = 1;
	SubscriptionType type = 2;
	Currency currency = 3;
	google.protobuf.StringValue phone_number = 4;
	bool debt_only = 5;
	google.protobuf.Int32Value current_page = 6;
	google.protobuf.Int32Value page_size = 7;
	google.protobuf.Int32Value days_count = 8;
	WalletTypeFilter wallet_type_filter = 9;
	CashAndHoldersFilter cash_and_holders_filter = 10;
	google.protobuf.StringValue holder_id = 11;
	bool expiration_only = 12;
}

message CashflowFilterResponse { 
	int32 current_page = 1;
	int32 page_size = 2;
	int32 total = 3;
	double total_net_balance = 4;
	double average_sales_location = 5;
	repeated CashflowFilterWalletDto wallet_dtos = 6;
	double average_sales_location_rate = 7;
}

message CashflowFilterWalletDto
{
	string id = 1;
	string subscription_name = 2;
	double credit = 3;
	double debit = 4;
	double net_balance = 5;
	double urgent = 6;
	double delay = 7;
	string phone_number = 8;
	string state = 9;
	string location = 10;
	string owner = 11;
	google.protobuf.StringValue email = 12;
	string wallet_identifier = 13;
	string wallet_region = 14;
	bool is_normal = 15;
	double average_sales = 16;
	double positve_balance_rate = 17;
	string code = 18;
	google.protobuf.StringValue holder_name = 19;
	google.protobuf.Timestamp confirmed_at = 20;
	google.protobuf.Timestamp last_delay_refund_date = 21;	
	string arabic_location_name = 22;
	string english_location_name = 23;
	double total_sales_last_day = 24;
	google.protobuf.StringValue remaining_time = 25;
}

message GetHoldersResponse{
	repeated HolderOption holders = 1;
}

message HolderOption{
    string id = 1;
	string name = 2;
}

enum SubscriptionType {
	ALL_OPTIONS = 0;
	OPERATOR = 1;
	BUSINESS = 2;
	NORMAL = 3;
}

enum Currency {
	ALL = 0;
	LYD = 1;
	USD = 2;
}

enum WalletTypeFilter {
	ALL_WALLETS = 0;
	NORMAL_WALLETS = 1;
	PROFIT_WALLETS = 2;
}

enum CashAndHoldersFilter{
	ALL_CASH_AND_HOLDERS = 0;
	CASH_ONLY = 1;
	HOLDERS_ONLY = 2;
	SPECIFIC_HOLDER = 3;
}