﻿using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;

namespace Anis.Cashflow.Report.Test.Fakers.Accounts.EventsData
{
    public class AccountEmailChangedDataFaker : PrivateFaker<AccountEmailChangedData>
    {
        public AccountEmailChangedDataFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.Email, f => f.Random.AlphaNumeric(30));
        }
        
        public static AccountEmailChangedData Create()
            => new AccountEmailChangedDataFaker()
                .Generate();
    }
}
