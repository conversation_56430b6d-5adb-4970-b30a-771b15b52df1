﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Wallets.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Wallets.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;
using TransactionType = Anis.Cashflow.Report.Infra.Services.Const.TransactionType;

namespace Anis.Cashflow.Report.Test.Handlers.WalletHandlers
{
    public class RefundUrgentTopUpTransactionTest : TestBase
    {
        public RefundUrgentTopUpTransactionTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task Update_RefundUrgentTopUpTransactionByCreditWithValidData__SuccessHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 1500)
                .RuleFor(f => f.CurrentUrgent, 1000)
                .RuleFor(f => f.CurrentDelayed, 500)
                .RuleFor(f => f.NetBalance, 0)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var transactionCreatedDataFaker = new TransactionCreatedDataFaker()
              .RuleFor(f => f.Credit, 1000)
              .RuleFor(f => f.Value, -1000)
               .RuleFor(f => f.Type, TransactionType.RefundUrgentTopUp)
              .Generate();

            var messageBody = new MessageBody<TransactionCreatedData>
            {
                AggregateId = creatWallet.Id,
                Sequence = creatWallet.Sequence + 1,
                Type = EventType.TransactionCreated,
                Data = transactionCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 
            Assert.True(isHandled);

            WalletAssert.AssertRefundUrgentOrDelayedEquality(dbWallet, messageBody, creatWallet, isUrgent: true);
        }

        [Fact]
        public async Task Update_RefundUrgentTopUpTransactionByCreditWithNotValidSequence__NotHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 5000)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var transactionCreatedDataFaker = new TransactionCreatedDataFaker()
              .RuleFor(f => f.Credit, 1000)
              .RuleFor(f => f.Value, -1000)
               .RuleFor(f => f.Type, TransactionType.RefundUrgentTopUp)
              .Generate();

            var messageBody = new MessageBody<TransactionCreatedData>
            {
                AggregateId = creatWallet.Id,
                Sequence = creatWallet.Sequence + 3,
                Type = EventType.TransactionCreated,
                Data = transactionCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 
            Assert.False(isHandled);

            WalletAssert.AssertEquality(creatWallet, dbWallet, sequence: creatWallet.Sequence);
        }

        [Fact]
        public async Task Update_RefundUrgentTopUpTransactionByDebitWithValidData__SuccessHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 5500)
                .RuleFor(f => f.CurrentDelayed, 500)
                .RuleFor(f => f.CurrentUrgent, 0)
                .RuleFor(f => f.NetBalance, 5000)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var transactionCreatedDataFaker = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Debit, 1000)
             .RuleFor(f => f.Value, 1000)
              .RuleFor(f => f.Type, TransactionType.RefundUrgentTopUp)
             .Generate();

            var messageBody = new MessageBody<TransactionCreatedData>
            {
                AggregateId = creatWallet.Id,
                Sequence = creatWallet.Sequence + 1,
                Type = EventType.TransactionCreated,
                Data = transactionCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 
            Assert.True(isHandled);

            WalletAssert.AssertRefundUrgentOrDelayedEquality(dbWallet, messageBody, creatWallet, isDebit: true, isUrgent: true);

        }

        [Fact]
        public async Task Update_RefundUrgentTopUpTransactionWalletIsNotExist_NotHandle()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
                .Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            var creatWallet = new WalletFaker()
                .RuleFor(f => f.RegionId, createRegion.Id)
                .RuleFor(f => f.SubscriptionId, createSubscription.Id)
                .RuleFor(f => f.Balance, 5000)
                .Generate();

            await context.Wallets.AddAsync(creatWallet);

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();
            context.ChangeTracker.Clear();

            var transactionCreatedDataFaker = new TransactionCreatedDataFaker()
             .RuleFor(f => f.Debit, 1000)
             .RuleFor(f => f.Value, 1000)
              .RuleFor(f => f.Type, TransactionType.RefundUrgentTopUp)
             .Generate();

            var messageBody = new MessageBody<TransactionCreatedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = creatWallet.Sequence + 1,
                Type = EventType.TransactionCreated,
                Data = transactionCreatedDataFaker,
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbWallet = await context.Wallets.SingleAsync();

            //assert 
            Assert.False(isHandled);

            WalletAssert.AssertEquality(creatWallet, dbWallet, sequence: creatWallet.Sequence);
        }
    }
}
