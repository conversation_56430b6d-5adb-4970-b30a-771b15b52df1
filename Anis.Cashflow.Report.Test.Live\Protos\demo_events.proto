syntax = "proto3";

package anis.holder;

import "google/protobuf/timestamp.proto";
import "google/protobuf/empty.proto";

option csharp_namespace = "Anis.Cashflow.Report.Test.Live.Protos.Holder";

service DemoEvents{
  rpc Create (CreateRequest) returns (google.protobuf.Empty);
  rpc ChangeName (ChangeNameRequest) returns (google.protobuf.Empty);
  rpc Delete (DeleteRequest) returns (google.protobuf.Empty);
  rpc AddRegion (AddRegionRequest) returns (google.protobuf.Empty);
  rpc ChangeRegionDifference (ChangeRegionDifferenceRequest) returns (google.protobuf.Empty);
  rpc RemoveRegion (RemoveRegionRequest) returns (google.protobuf.Empty);
  rpc LockRegion (LockRegionRequest) returns (google.protobuf.Empty);
  rpc UnlockRegion (UnlockRegionRequest) returns (google.protobuf.Empty);
}

message CreateRequest {
  string user_id = 1;
  string arabic_name = 2;
  string english_name = 3;
  string id = 4;
  google.protobuf.Timestamp date_time = 5;
}

message ChangeNameRequest{
  string id = 1;
  string user_id = 2;
  string arabic_name = 3;
  string english_name = 4;
  int32 sequence = 5;
  google.protobuf.Timestamp date_time = 6;
}

message DeleteRequest{
  string id = 1;
  string user_id = 2;
  int32 sequence = 3;
  google.protobuf.Timestamp date_time = 4;
}

message AddRegionRequest{
  string id = 1;
  string user_id = 2;
  string region_Id = 3;
  double local_diffrence = 4;
  double international_diffrence = 5;
  int32 sequence = 6;
  google.protobuf.Timestamp date_time = 7;
}

message ChangeRegionDifferenceRequest{
  string id = 1;
  string user_id = 2;
  string region_Id = 3;
  double local_diffrence = 4;
  double international_diffrence = 5;
  int32 sequence = 6;
  google.protobuf.Timestamp date_time = 7;
}

message RemoveRegionRequest{
  string id = 1;
  string user_id = 2;
  string region_Id = 3;
  int32 sequence = 4;
  google.protobuf.Timestamp date_time = 5;
}

message LockRegionRequest{
  string id = 1;
  string user_id = 2;
  string region_Id = 3;
  int32 sequence = 4;
  google.protobuf.Timestamp date_time = 5;
}

message UnlockRegionRequest{
  string id = 1;
  string user_id = 2;
  string region_Id = 3;
  int32 sequence = 4;
  google.protobuf.Timestamp date_time = 5;
}