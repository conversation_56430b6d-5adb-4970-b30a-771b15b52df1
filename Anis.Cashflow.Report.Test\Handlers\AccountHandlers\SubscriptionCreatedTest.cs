﻿using Anis.Cashflow.Report.Application.Const;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;
using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Test.Asserts;
using Anis.Cashflow.Report.Test.Fakers.Accounts.EventsData;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Handlers.AccountHandlers
{
    public class SubscriptionCreatedTest : TestBase
    {
        public SubscriptionCreatedTest(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task Create_SubscriptionCreatedWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.Accounts.AddAsync(createAccount);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<SubscriptionCreatedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.SubscriptionCreated,
                Data = new SubscriptionCreatedDataFaker().Generate(),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccounts = await context.Accounts.SingleAsync();

            var dbSubscription = await context.Subscriptions.SingleAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(messageBody, dbSubscription, dbAccounts);
        }

        [Fact]
        public async Task Create_SubscriptionCreatedWithNonExistedAccount_NotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var messageBody = new MessageBody<SubscriptionCreatedData>
            {
                AggregateId = Guid.NewGuid().ToString(),
                Sequence = 2,
                Type = EventType.SubscriptionCreated,
                Data = new SubscriptionCreatedDataFaker().Generate(),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            Assert.False(isHandled);

            var account = await context.Accounts.SingleOrDefaultAsync();

            var subscription = await context.Subscriptions.SingleOrDefaultAsync();

            Assert.Null(account);
            Assert.Null(subscription);
        }

        [Fact]
        public async Task Create_EventArrivedWithNotValidSequence_NotHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .Generate();

            await context.Regions.AddAsync(createRegion);

            await context.Accounts.AddAsync(createAccount);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<SubscriptionCreatedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 4,
                Type = EventType.SubscriptionCreated,
                Data = new SubscriptionCreatedDataFaker().Generate(),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccounts = await context.Accounts.SingleAsync();

            var dbSubscription = await context.Subscriptions.SingleOrDefaultAsync();

            Assert.False(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccounts);

            Assert.Null(dbSubscription);
        }

        [Fact]
        public async Task Create_EventArrivedWithOldSequence_EventHandled()
        {
            using var scope = Factory.Services.CreateScope();

            var mediator = scope.ServiceProvider.GetRequiredService<IMediator>();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker().Generate();

            var createAccount = new AccountFaker()
                .RuleFor(f => f.LocationId, createRegion.Id)
                .RuleFor(r => r.Sequence, 3)
                .Generate();

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var messageBody = new MessageBody<SubscriptionCreatedData>
            {
                AggregateId = createAccount.Id,
                Sequence = 2,
                Type = EventType.SubscriptionCreated,
                Data = new SubscriptionCreatedDataFaker().Generate(),
                DateTime = DateTime.UtcNow,
                Version = 2,
            };

            var isHandled = await mediator.Send(messageBody);

            var dbAccounts = await context.Accounts.SingleAsync();

            var dbSubscription = await context.Subscriptions.SingleOrDefaultAsync();

            Assert.True(isHandled);

            EntityAssert.AssertEquality(createAccount, dbAccounts);

            Assert.Null(dbSubscription);
        }
    }
}