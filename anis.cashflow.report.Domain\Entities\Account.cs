﻿using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;

namespace Anis.Cashflow.Report.Domain.Entities
{
    public class Account
    {
        private Account() { }

        public Account(MessageBody<AccountConfirmedData> model)
        {
            Id = model.AggregateId;
            Sequence = model.Sequence;
            OwnerName = model.Data.OwnerName;
            Email = model.Data.Email;
            Phone = model.Data.Phone;
            LocationId = model.Data.LocationId;
            Number = model.Data.Number;
            ConfirmedAt = model.Data.ConfirmedAt;

            _subscriptions = model.Data.Subscriptions.Select(s => new Subscription(model.AggregateId, s)).ToHashSet();
        }

        public string Id { get; private set; }
        public long Sequence { get; private set; }
        public string Number { get; private set; }
        public string OwnerName { get; private set; }
        public string LocationId { get; private set; }
        public Region Location { get; private set; }
        public string Phone { get; private set; }
        public string Email { get; private set; }
        public DateTime? ExpiryAt { get; private set; }
        public DateTime? ConfirmedAt { get; private set; }

        private HashSet<Subscription> _subscriptions = new();
        public IReadOnlyCollection<Subscription> Subscriptions => _subscriptions;

        public void IncrementSequence()
        {
            Sequence++;
        }

        public void Modify(MessageBody<AccountConfirmedData> model)
        {
            Sequence = model.Sequence;

            Number = model.Data.Number;

            Phone = model.Data.Phone;

            ConfirmedAt = model.Data.ConfirmedAt;

            _subscriptions = model.Data.Subscriptions.Select(s => new Subscription(model.AggregateId, s)).ToHashSet();
        }

        public void Modify(MessageBody<AccountUpdatedData> model)
        {
            Sequence = model.Sequence;

            Number = model.Data.Number;

            if (!string.IsNullOrWhiteSpace(model.Data.LocationId))
                LocationId = model.Data.LocationId;
        }

        public void Modify(MessageBody<ExpiryDateRemovedData> model)
        {
            Sequence = model.Sequence;
            ExpiryAt = null;
        }

        public void Modify(MessageBody<ExpiryDateChangedData> model)
        {
            Sequence = model.Sequence;
            ExpiryAt = model.Data.ExpireAt;
        }

        public void Modify(DateTime confirmedAt)
        {
            ConfirmedAt = confirmedAt;
        }

        public void Modify(MessageBody<AccountPhoneNumberChangedData> model)
        {
            Phone = model.Data.Phone;
            Sequence = model.Sequence;
        }

        public void Modify(MessageBody<AccountEmailChangedData> model)
        {
            Email = model.Data.Email;
            Sequence = model.Sequence;
        }
    }
}
