syntax = "proto3";

import "google/protobuf/timestamp.proto";

option csharp_namespace = "Anis.Cashflow.Report.Grpc.Protos.Client";

service NotificationHistoryOperatorToBusinessLink {
	rpc GetNotificationMessages (GetNotificationMessagesRequest) returns (GetNotificationMessagesResponse);
}

message GetNotificationMessagesRequest {
	int32 current_page = 1;
    int32 page_size = 2;
}

message GetNotificationMessagesResponse {
	repeated NotificationMessage notification_messages = 1;
}

message NotificationMessage {
    string operator_subscription_id = 1;
    int32 state_version = 2;
    int32 data_version = 3;
    int32 build_version = 4;
    repeated OperatorTransfers transfers = 5;
}

message OperatorTransfers {
    string operator_wallet_id = 1;
    string business_wallet_id = 2;
    string business_subscription_id = 3;
    google.protobuf.Timestamp link_date_time = 4;
}