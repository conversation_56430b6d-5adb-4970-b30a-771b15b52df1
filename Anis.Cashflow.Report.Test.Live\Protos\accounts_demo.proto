syntax = "proto3";

package anis.gateway.demo.v2;

import "google/protobuf/wrappers.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/Timestamp.proto";
 
option csharp_namespace = "Anis.Cashflow.Report.Test.Live.Protos";

service AccountsDemoEvents {
  rpc ConfirmeAccount (ConfirmeAccountRequest) returns (google.protobuf.Empty);
  rpc UnconfirmeAccount (UnconfirmeAccountRequest) returns (google.protobuf.Empty);
  rpc UpdateAccount (UpdateAccountRequest) returns (google.protobuf.Empty);
  rpc CreateSubscription (CreateSubscriptionRequest) returns (google.protobuf.Empty);
  rpc UpdateSubscription (UpdateSubscriptionRequest) returns (google.protobuf.Empty);
  rpc RemoveSubscription (RemoveSubscriptionRequest) returns (google.protobuf.Empty);
  rpc ChangeAccountPhone (ChangeAccountPhoneRequest) returns (google.protobuf.Empty);
  rpc ChangeAccountEmail (ChangeAccountEmailRequest) returns (google.protobuf.Empty);
}

message ConfirmeAccountRequest {
	string aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	int32 sequence = 3;
	string owner_id = 4;
	google.protobuf.StringValue owner_name = 5;
	google.protobuf.StringValue identity_provider_id = 6;
	google.protobuf.StringValue email = 7;
	google.protobuf.StringValue phone = 8;
	google.protobuf.StringValue business_type = 9;
	google.protobuf.StringValue license_number = 10;
	bool request_pos  = 11;
	string location_id = 12;
	google.protobuf.Timestamp created_at = 13;
	bool is_confirmed = 14;
	google.protobuf.StringValue number = 15;
	google.protobuf.Timestamp confirmed_at = 16;
	google.protobuf.Timestamp upgraded_at = 17;
	repeated SubscriptionRequest subscriptions = 18;
}

message SubscriptionRequest {
	string id = 1;
	google.protobuf.StringValue name = 2;
	string account_id = 3;
	int32 type = 4;
	google.protobuf.StringValue type_display = 5;
	google.protobuf.StringValue number = 6;
	bool is_representative = 7;
}

message UnconfirmeAccountRequest {
	string aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	int32 sequence = 3;
}

message UpdateAccountRequest {
	string aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	int32 sequence = 3;
	google.protobuf.StringValue number = 4;
	google.protobuf.DoubleValue longitude = 5;
	google.protobuf.DoubleValue latitude = 6;
	google.protobuf.StringValue pos_brand = 7;
	google.protobuf.StringValue pos_serial_number = 8;
	string location_id = 9;
}

message CreateSubscriptionRequest {
	string aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	int32 sequence = 3;
	string id = 4;
	google.protobuf.StringValue name = 5;
	int32 type = 6;
	google.protobuf.StringValue type_display = 7;
	google.protobuf.StringValue number = 8;
	bool is_representative = 9;
}

message UpdateSubscriptionRequest {
	string aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	int32 sequence = 3;
	string id = 4;
	google.protobuf.StringValue name = 5;
	double max_debt = 6;
	int32 days_before_debt_expires = 7;
}

message RemoveSubscriptionRequest {
	string aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	int32 sequence = 3;
	string id = 4;
}

message ChangeAccountPhoneRequest {
	string aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	int32 sequence = 3;
	google.protobuf.StringValue phone = 4;
}

message ChangeAccountEmailRequest {
	string aggregate_id = 1;
	google.protobuf.Timestamp date_time = 2;
	int32 sequence = 3;
	google.protobuf.StringValue email = 4;
}