﻿using Anis.Cashflow.Report.Domain.EventsData.Regions;
using Anis.Cashflow.Report.Domain.Models;

namespace Anis.Cashflow.Report.Domain.Entities
{
    public class Region
    {
        private Region() { }

        public Region(MessageBody<RegionCreatedData> model)
        {
            Id = model.AggregateId;
            ArabicName = model.Data.ArabicName;
            EnglishName = model.Data.EnglishName;
            Sequence = 1;
            Code = model.Data.Code;
        }

        public string Id { get; private set; }
        public string ArabicName { get; private set; }
        public string EnglishName { get; private set; }
        public string Code { get; private set; }
        public long Sequence { get; private set; }

        public ICollection<Account> Accounts { get; private set; }
        public ICollection<Wallet> Wallets { get; private set; }

        public void ChangeCode(MessageBody<RegionCodeChangedData> model)
        {
            Sequence = model.Sequence;

            Code = model.Data.Code;
        }

        public void Modify(MessageBody<RegionUpdatedData> message)
        {
            Sequence = message.Sequence;

            ArabicName = message.Data.ArabicName;
            EnglishName = message.Data.EnglishName;
        }

        public void IncrementSequence()
        {
            ++Sequence;
        }
    }
}
