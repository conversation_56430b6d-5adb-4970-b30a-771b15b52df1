using Anis.Cashflow.Report.Application;
using Anis.Cashflow.Report.Grpc;
using Anis.Cashflow.Report.Grpc.Services;
using Anis.Cashflow.Report.Infra;
using Anis.Cashflow.Report.Infra.Services.Logger;
using Serilog;

public class Program
{
    public static void Main(string[] args)
    {
        Log.Logger = LoggerServiceBuilder.Build();

        var builder = WebApplication.CreateBuilder(args);

        builder.Host.UseSerilog();

        var services = builder.Services;

        services.AddGrpcServices(builder.Configuration);

        services.AddApplicationServices();

        services.AddInfraServices(builder.Configuration);

        var app = builder.Build();

        app.MapGrpcService<QueryManagmentService>();
        app.MapGrpcService<QueryConsumerService>();
        app.MapGrpcService<RebuildService>();
        app.MapGrpcService<RebuildServiceV3>();

        app.MapGet("/",
            () => "Communication with gRPC endpoints must be made through a gRPC client. To learn how to create a client, visit: https://go.microsoft.com/fwlink/?linkid=2086909");

        app.Run();

    }
}
