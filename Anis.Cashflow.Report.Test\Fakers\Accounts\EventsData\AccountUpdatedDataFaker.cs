﻿using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;

namespace Anis.Cashflow.Report.Test.Fakers.Accounts.EventsData;

public class AccountUpdatedDataFaker : PrivateFaker<AccountUpdatedData>
{
    public AccountUpdatedDataFaker()
    {
        UsePrivateConstructor();

        RuleFor(c => c.LocationId, Guid.NewGuid().ToString);
        RuleFor(c => c.Number, Guid.NewGuid().ToString);
    }

    public static AccountUpdatedData Create(string locationId)
        => new AccountUpdatedDataFaker()
            .RuleFor(c => c.LocationId, locationId)
            .Generate();
}