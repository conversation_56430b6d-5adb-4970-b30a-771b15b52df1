syntax = "proto3";

package anis.operator_to_business_link_service;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";

option csharp_namespace = "Anis.Cashflow.Report.Test.Live.OperatorBusinessLinkDemo";

service OperatorToBusinessLinkDemoNotifications {
  rpc Notify(NotifyRequest) returns (google.protobuf.Empty);
}

message NotifyRequest {
  string operator_subscription_id = 1;
  int32 state_version = 2;
  string operator_wallet_id = 3;
  string business_wallet_id = 4;
  string business_subscription_id = 5;
  NotificationType notification_type = 6;
}

enum NotificationType {
	BUSINESS_ADDED = 0;
	BUSINESS_REMOVED = 1;
}