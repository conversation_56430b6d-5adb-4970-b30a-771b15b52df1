﻿using Anis.Cashflow.Report.Domain.Models.Configs;
using Anis.Cashflow.Report.Infra.Persistence;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Serilog;
using Serilog.Events;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test
{
    public class TestWebApplicationFactory<TStartup> : WebApplicationFactory<TStartup> where TStartup : class
    {
        private readonly ITestOutputHelper _output;
        private readonly Action<IServiceCollection> _configure;
        private readonly string _dbName;

        public TestWebApplicationFactory(ITestOutputHelper output, Action<IServiceCollection> configure)
        {
            _dbName = Guid.NewGuid().ToString();
            _output = output;
            _configure = configure;
        }

        protected override void ConfigureWebHost(IWebHostBuilder builder)
        {
            builder.ConfigureLogging(loggingBuilder =>
            {
                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Information()
                    .WriteTo.TestOutput(_output, LogEventLevel.Information)
                    .CreateLogger();
            });

            builder.ConfigureServices(services =>
            {
                services.Configure<ServiceBusOptions>(i =>
                {
                    i.AccountTopic = "ecom-cards-accounts-development";
                    i.AccountSubscription = "anis-cashflow-report-testing";
                    i.WalletTopic = "ecom-cards-wallets-development";
                    i.WalletSubscription = "anis-cashflow-report-testing";
                    i.RegionTopic = "ecom-cards-development";
                    i.RegionSubscription = "anis-cashflow-v2-testing";
                    i.HolderTopic = "holder-testing";
                    i.HolderSubscription = "cashflow-report-holder-testing";
                    i.OperatorBusinessLinkTopic = "operator-to-business-link-service-test";
                    i.OperatorBusinessLinkSubscription = "anis-cashflow-report-test";
                });

                _configure(services);

                services.Remove(services.Single(d => d.ServiceType == typeof(DbContextOptions<AppDbContext>)));

                services.Remove(services.Single(d => d.ServiceType == typeof(AppDbContext)));

                services.AddDbContext<AppDbContext>(options =>
                {
#pragma warning disable CS0162 // Unreachable code detected
                    if (TestBase.UseSqlDataBase)
                        options.UseSqlServer("Server=.\\sqlexpress; Database=anis-CashflowReport1; Trusted_Connection=True;");
                    // options.UseSqlServer(@"Data Source=(localdb)\ProjectModels;Initial Catalog=anis-cashflow-report-test; TrustServerCertificate=True;Integrated Security=True;");
                    // options.UseSqlServer("Server=.\\;Database=anis-cash-flow-report-test;TrustServerCertificate=True; Integrated Security=true;");

                    else
                        options.UseInMemoryDatabase(_dbName);
#pragma warning restore CS0162 // Unreachable code detected
                }, ServiceLifetime.Transient);
            });
        }
    }
}
