﻿using Anis.Cashflow.Report.Infra.Persistence;
using Anis.Cashflow.Report.Infra.Services.ServiceBus.Listeners;
using Anis.Cashflow.Report.Test.Live.Protos;
using Google.Protobuf.WellKnownTypes;
using Grpc.Net.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Xunit.Abstractions;

namespace Anis.Cashflow.Report.Test.Live.AccountEvents
{
    public class SubscriptionRemovedTest : TestBase
    {
        public SubscriptionRemovedTest(ITestOutputHelper output) : base(output) { }

        [Fact]
        public async Task Update_SubscriptionRemovedWithValidData_ReturnValid()
        {
            using var scope = Factory.Services.CreateScope();

            var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();

            var createRegion = new RegionFaker()
             .Generate();

            var createAccount = new AccountFaker()
             .RuleFor(f => f.LocationId, createRegion.Id)
             .Generate();

            var createSubscription = new SubscriptionFaker()
                .RuleFor(f => f.AccountId, createAccount.Id)
                .Generate();

            await context.Subscriptions.AddAsync(createSubscription);

            await context.Accounts.AddAsync(createAccount);

            await context.Regions.AddAsync(createRegion);

            await context.SaveChangesAsync();

            context.ChangeTracker.Clear();

            var channel = GrpcChannel.ForAddress(Address.EcomCards);

            var grpcClient = new AccountsDemoEvents.AccountsDemoEventsClient(channel);

            var request = new RemoveSubscriptionRequest
            {
                AggregateId = createAccount.Id,
                DateTime = DateTime.UtcNow.ToTimestamp(),
                Sequence = 2,
                Id = createSubscription.Id,
            };

            await grpcClient.RemoveSubscriptionAsync(request);

            await Task.Delay(Delay);

            await Factory.Services.GetRequiredService<AccountEventsListener>().CloseProccessorAsync();

            var dbAccounts = await context.Accounts.SingleOrDefaultAsync();

            var subscription = await context.Subscriptions.SingleOrDefaultAsync();

            Assert.Equal(createAccount.Id, dbAccounts.Id);
            Assert.Equal(2, dbAccounts.Sequence);

            Assert.NotNull(subscription);
        }
    }
}