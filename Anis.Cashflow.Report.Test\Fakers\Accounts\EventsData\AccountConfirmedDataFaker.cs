﻿using Anis.Cashflow.Report.Domain.Enums;
using Anis.Cashflow.Report.Domain.EventsData.Accounts.DataTypes;
using Anis.Cashflow.Report.Domain.Models;

namespace Anis.Cashflow.Report.Test.Fakers.Accounts.EventsData
{
    public sealed class AccountConfirmedDataFaker : PrivateFaker<AccountConfirmedData>
    {
        public AccountConfirmedDataFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.Email, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.LocationId, Guid.NewGuid().ToString());
            RuleFor(r => r.Number, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.OwnerName, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.Phone, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.Number, f => f.Random.AlphaNumeric(10));
            RuleFor(r => r.Confirmed<PERSON>t, DateTime.UtcNow);
            RuleFor(r => r.Subscriptions, f => new SubscriptionDataFaker().GenerateLazy(f.Random.Number(1, 1)).ToList());
        }
    }

    public sealed class SubscriptionDataFaker : PrivateFaker<SubscriptionModel>
    {
        public SubscriptionDataFaker()
        {
            UsePrivateConstructor();

            RuleFor(r => r.Id, Guid.NewGuid().ToString());
            RuleFor(r => r.Type, SubscriptionType.Business);
            RuleFor(r => r.Name, f => f.Random.AlphaNumeric(10));
        }
    }
}